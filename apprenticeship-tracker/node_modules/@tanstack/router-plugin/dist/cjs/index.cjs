"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const config = require("./core/config.cjs");
const routerCodeSplitterPlugin = require("./core/router-code-splitter-plugin.cjs");
const routerGeneratorPlugin = require("./core/router-generator-plugin.cjs");
const constants = require("./core/constants.cjs");
exports.configSchema = config.configSchema;
exports.getConfig = config.getConfig;
exports.unpluginRouterCodeSplitterFactory = routerCodeSplitterPlugin.unpluginRouterCodeSplitterFactory;
exports.unpluginRouterGeneratorFactory = routerGeneratorPlugin.unpluginRouterGeneratorFactory;
exports.defaultCodeSplitGroupings = constants.defaultCodeSplitGroupings;
exports.splitRouteIdentNodes = constants.splitRouteIdentNodes;
exports.tsrSplit = constants.tsrSplit;
//# sourceMappingURL=index.cjs.map
