(function(){const u=document.createElement("link").relList;if(u&&u.supports&&u.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))c(d);new MutationObserver(d=>{for(const m of d)if(m.type==="childList")for(const f of m.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&c(f)}).observe(document,{childList:!0,subtree:!0});function r(d){const m={};return d.integrity&&(m.integrity=d.integrity),d.referrerPolicy&&(m.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?m.credentials="include":d.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function c(d){if(d.ep)return;d.ep=!0;const m=r(d);fetch(d.href,m)}})();function $m(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var wo={exports:{}},xs={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cm;function $g(){if(cm)return xs;cm=1;var n=Symbol.for("react.transitional.element"),u=Symbol.for("react.fragment");function r(c,d,m){var f=null;if(m!==void 0&&(f=""+m),d.key!==void 0&&(f=""+d.key),"key"in d){m={};for(var v in d)v!=="key"&&(m[v]=d[v])}else m=d;return d=m.ref,{$$typeof:n,type:c,key:f,ref:d!==void 0?d:null,props:m}}return xs.Fragment=u,xs.jsx=r,xs.jsxs=r,xs}var om;function Jg(){return om||(om=1,wo.exports=$g()),wo.exports}var s=Jg(),_o={exports:{}},ve={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var um;function Fg(){if(um)return ve;um=1;var n=Symbol.for("react.transitional.element"),u=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),m=Symbol.for("react.consumer"),f=Symbol.for("react.context"),v=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),b=Symbol.iterator;function j(_){return _===null||typeof _!="object"?null:(_=b&&_[b]||_["@@iterator"],typeof _=="function"?_:null)}var T={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},R=Object.assign,N={};function S(_,L,J){this.props=_,this.context=L,this.refs=N,this.updater=J||T}S.prototype.isReactComponent={},S.prototype.setState=function(_,L){if(typeof _!="object"&&typeof _!="function"&&_!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,_,L,"setState")},S.prototype.forceUpdate=function(_){this.updater.enqueueForceUpdate(this,_,"forceUpdate")};function A(){}A.prototype=S.prototype;function X(_,L,J){this.props=_,this.context=L,this.refs=N,this.updater=J||T}var q=X.prototype=new A;q.constructor=X,R(q,S.prototype),q.isPureReactComponent=!0;var P=Array.isArray,ee={H:null,A:null,T:null,S:null,V:null},re=Object.prototype.hasOwnProperty;function K(_,L,J,Z,F,se){return J=se.ref,{$$typeof:n,type:_,key:L,ref:J!==void 0?J:null,props:se}}function V(_,L){return K(_.type,L,void 0,void 0,void 0,_.props)}function oe(_){return typeof _=="object"&&_!==null&&_.$$typeof===n}function te(_){var L={"=":"=0",":":"=2"};return"$"+_.replace(/[=:]/g,function(J){return L[J]})}var $=/\/+/g;function W(_,L){return typeof _=="object"&&_!==null&&_.key!=null?te(""+_.key):L.toString(36)}function me(){}function be(_){switch(_.status){case"fulfilled":return _.value;case"rejected":throw _.reason;default:switch(typeof _.status=="string"?_.then(me,me):(_.status="pending",_.then(function(L){_.status==="pending"&&(_.status="fulfilled",_.value=L)},function(L){_.status==="pending"&&(_.status="rejected",_.reason=L)})),_.status){case"fulfilled":return _.value;case"rejected":throw _.reason}}throw _}function ge(_,L,J,Z,F){var se=typeof _;(se==="undefined"||se==="boolean")&&(_=null);var I=!1;if(_===null)I=!0;else switch(se){case"bigint":case"string":case"number":I=!0;break;case"object":switch(_.$$typeof){case n:case u:I=!0;break;case x:return I=_._init,ge(I(_._payload),L,J,Z,F)}}if(I)return F=F(_),I=Z===""?"."+W(_,0):Z,P(F)?(J="",I!=null&&(J=I.replace($,"$&/")+"/"),ge(F,L,J,"",function(_e){return _e})):F!=null&&(oe(F)&&(F=V(F,J+(F.key==null||_&&_.key===F.key?"":(""+F.key).replace($,"$&/")+"/")+I)),L.push(F)),1;I=0;var ce=Z===""?".":Z+":";if(P(_))for(var pe=0;pe<_.length;pe++)Z=_[pe],se=ce+W(Z,pe),I+=ge(Z,L,J,se,F);else if(pe=j(_),typeof pe=="function")for(_=pe.call(_),pe=0;!(Z=_.next()).done;)Z=Z.value,se=ce+W(Z,pe++),I+=ge(Z,L,J,se,F);else if(se==="object"){if(typeof _.then=="function")return ge(be(_),L,J,Z,F);throw L=String(_),Error("Objects are not valid as a React child (found: "+(L==="[object Object]"?"object with keys {"+Object.keys(_).join(", ")+"}":L)+"). If you meant to render a collection of children, use an array instead.")}return I}function C(_,L,J){if(_==null)return _;var Z=[],F=0;return ge(_,Z,"","",function(se){return L.call(J,se,F++)}),Z}function Q(_){if(_._status===-1){var L=_._result;L=L(),L.then(function(J){(_._status===0||_._status===-1)&&(_._status=1,_._result=J)},function(J){(_._status===0||_._status===-1)&&(_._status=2,_._result=J)}),_._status===-1&&(_._status=0,_._result=L)}if(_._status===1)return _._result.default;throw _._result}var U=typeof reportError=="function"?reportError:function(_){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var L=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof _=="object"&&_!==null&&typeof _.message=="string"?String(_.message):String(_),error:_});if(!window.dispatchEvent(L))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",_);return}console.error(_)};function ue(){}return ve.Children={map:C,forEach:function(_,L,J){C(_,function(){L.apply(this,arguments)},J)},count:function(_){var L=0;return C(_,function(){L++}),L},toArray:function(_){return C(_,function(L){return L})||[]},only:function(_){if(!oe(_))throw Error("React.Children.only expected to receive a single React element child.");return _}},ve.Component=S,ve.Fragment=r,ve.Profiler=d,ve.PureComponent=X,ve.StrictMode=c,ve.Suspense=p,ve.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=ee,ve.__COMPILER_RUNTIME={__proto__:null,c:function(_){return ee.H.useMemoCache(_)}},ve.cache=function(_){return function(){return _.apply(null,arguments)}},ve.cloneElement=function(_,L,J){if(_==null)throw Error("The argument must be a React element, but you passed "+_+".");var Z=R({},_.props),F=_.key,se=void 0;if(L!=null)for(I in L.ref!==void 0&&(se=void 0),L.key!==void 0&&(F=""+L.key),L)!re.call(L,I)||I==="key"||I==="__self"||I==="__source"||I==="ref"&&L.ref===void 0||(Z[I]=L[I]);var I=arguments.length-2;if(I===1)Z.children=J;else if(1<I){for(var ce=Array(I),pe=0;pe<I;pe++)ce[pe]=arguments[pe+2];Z.children=ce}return K(_.type,F,void 0,void 0,se,Z)},ve.createContext=function(_){return _={$$typeof:f,_currentValue:_,_currentValue2:_,_threadCount:0,Provider:null,Consumer:null},_.Provider=_,_.Consumer={$$typeof:m,_context:_},_},ve.createElement=function(_,L,J){var Z,F={},se=null;if(L!=null)for(Z in L.key!==void 0&&(se=""+L.key),L)re.call(L,Z)&&Z!=="key"&&Z!=="__self"&&Z!=="__source"&&(F[Z]=L[Z]);var I=arguments.length-2;if(I===1)F.children=J;else if(1<I){for(var ce=Array(I),pe=0;pe<I;pe++)ce[pe]=arguments[pe+2];F.children=ce}if(_&&_.defaultProps)for(Z in I=_.defaultProps,I)F[Z]===void 0&&(F[Z]=I[Z]);return K(_,se,void 0,void 0,null,F)},ve.createRef=function(){return{current:null}},ve.forwardRef=function(_){return{$$typeof:v,render:_}},ve.isValidElement=oe,ve.lazy=function(_){return{$$typeof:x,_payload:{_status:-1,_result:_},_init:Q}},ve.memo=function(_,L){return{$$typeof:g,type:_,compare:L===void 0?null:L}},ve.startTransition=function(_){var L=ee.T,J={};ee.T=J;try{var Z=_(),F=ee.S;F!==null&&F(J,Z),typeof Z=="object"&&Z!==null&&typeof Z.then=="function"&&Z.then(ue,U)}catch(se){U(se)}finally{ee.T=L}},ve.unstable_useCacheRefresh=function(){return ee.H.useCacheRefresh()},ve.use=function(_){return ee.H.use(_)},ve.useActionState=function(_,L,J){return ee.H.useActionState(_,L,J)},ve.useCallback=function(_,L){return ee.H.useCallback(_,L)},ve.useContext=function(_){return ee.H.useContext(_)},ve.useDebugValue=function(){},ve.useDeferredValue=function(_,L){return ee.H.useDeferredValue(_,L)},ve.useEffect=function(_,L,J){var Z=ee.H;if(typeof J=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return Z.useEffect(_,L)},ve.useId=function(){return ee.H.useId()},ve.useImperativeHandle=function(_,L,J){return ee.H.useImperativeHandle(_,L,J)},ve.useInsertionEffect=function(_,L){return ee.H.useInsertionEffect(_,L)},ve.useLayoutEffect=function(_,L){return ee.H.useLayoutEffect(_,L)},ve.useMemo=function(_,L){return ee.H.useMemo(_,L)},ve.useOptimistic=function(_,L){return ee.H.useOptimistic(_,L)},ve.useReducer=function(_,L,J){return ee.H.useReducer(_,L,J)},ve.useRef=function(_){return ee.H.useRef(_)},ve.useState=function(_){return ee.H.useState(_)},ve.useSyncExternalStore=function(_,L,J){return ee.H.useSyncExternalStore(_,L,J)},ve.useTransition=function(){return ee.H.useTransition()},ve.version="19.1.0",ve}var dm;function Ts(){return dm||(dm=1,_o.exports=Fg()),_o.exports}var G=Ts();const Kt=$m(G);var Ro={exports:{}},ys={},Mo={exports:{}},To={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fm;function Pg(){return fm||(fm=1,function(n){function u(C,Q){var U=C.length;C.push(Q);e:for(;0<U;){var ue=U-1>>>1,_=C[ue];if(0<d(_,Q))C[ue]=Q,C[U]=_,U=ue;else break e}}function r(C){return C.length===0?null:C[0]}function c(C){if(C.length===0)return null;var Q=C[0],U=C.pop();if(U!==Q){C[0]=U;e:for(var ue=0,_=C.length,L=_>>>1;ue<L;){var J=2*(ue+1)-1,Z=C[J],F=J+1,se=C[F];if(0>d(Z,U))F<_&&0>d(se,Z)?(C[ue]=se,C[F]=U,ue=F):(C[ue]=Z,C[J]=U,ue=J);else if(F<_&&0>d(se,U))C[ue]=se,C[F]=U,ue=F;else break e}}return Q}function d(C,Q){var U=C.sortIndex-Q.sortIndex;return U!==0?U:C.id-Q.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var m=performance;n.unstable_now=function(){return m.now()}}else{var f=Date,v=f.now();n.unstable_now=function(){return f.now()-v}}var p=[],g=[],x=1,b=null,j=3,T=!1,R=!1,N=!1,S=!1,A=typeof setTimeout=="function"?setTimeout:null,X=typeof clearTimeout=="function"?clearTimeout:null,q=typeof setImmediate<"u"?setImmediate:null;function P(C){for(var Q=r(g);Q!==null;){if(Q.callback===null)c(g);else if(Q.startTime<=C)c(g),Q.sortIndex=Q.expirationTime,u(p,Q);else break;Q=r(g)}}function ee(C){if(N=!1,P(C),!R)if(r(p)!==null)R=!0,re||(re=!0,W());else{var Q=r(g);Q!==null&&ge(ee,Q.startTime-C)}}var re=!1,K=-1,V=5,oe=-1;function te(){return S?!0:!(n.unstable_now()-oe<V)}function $(){if(S=!1,re){var C=n.unstable_now();oe=C;var Q=!0;try{e:{R=!1,N&&(N=!1,X(K),K=-1),T=!0;var U=j;try{t:{for(P(C),b=r(p);b!==null&&!(b.expirationTime>C&&te());){var ue=b.callback;if(typeof ue=="function"){b.callback=null,j=b.priorityLevel;var _=ue(b.expirationTime<=C);if(C=n.unstable_now(),typeof _=="function"){b.callback=_,P(C),Q=!0;break t}b===r(p)&&c(p),P(C)}else c(p);b=r(p)}if(b!==null)Q=!0;else{var L=r(g);L!==null&&ge(ee,L.startTime-C),Q=!1}}break e}finally{b=null,j=U,T=!1}Q=void 0}}finally{Q?W():re=!1}}}var W;if(typeof q=="function")W=function(){q($)};else if(typeof MessageChannel<"u"){var me=new MessageChannel,be=me.port2;me.port1.onmessage=$,W=function(){be.postMessage(null)}}else W=function(){A($,0)};function ge(C,Q){K=A(function(){C(n.unstable_now())},Q)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(C){C.callback=null},n.unstable_forceFrameRate=function(C){0>C||125<C?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):V=0<C?Math.floor(1e3/C):5},n.unstable_getCurrentPriorityLevel=function(){return j},n.unstable_next=function(C){switch(j){case 1:case 2:case 3:var Q=3;break;default:Q=j}var U=j;j=Q;try{return C()}finally{j=U}},n.unstable_requestPaint=function(){S=!0},n.unstable_runWithPriority=function(C,Q){switch(C){case 1:case 2:case 3:case 4:case 5:break;default:C=3}var U=j;j=C;try{return Q()}finally{j=U}},n.unstable_scheduleCallback=function(C,Q,U){var ue=n.unstable_now();switch(typeof U=="object"&&U!==null?(U=U.delay,U=typeof U=="number"&&0<U?ue+U:ue):U=ue,C){case 1:var _=-1;break;case 2:_=250;break;case 5:_=1073741823;break;case 4:_=1e4;break;default:_=5e3}return _=U+_,C={id:x++,callback:Q,priorityLevel:C,startTime:U,expirationTime:_,sortIndex:-1},U>ue?(C.sortIndex=U,u(g,C),r(p)===null&&C===r(g)&&(N?(X(K),K=-1):N=!0,ge(ee,U-ue))):(C.sortIndex=_,u(p,C),R||T||(R=!0,re||(re=!0,W()))),C},n.unstable_shouldYield=te,n.unstable_wrapCallback=function(C){var Q=j;return function(){var U=j;j=Q;try{return C.apply(this,arguments)}finally{j=U}}}}(To)),To}var hm;function Wg(){return hm||(hm=1,Mo.exports=Pg()),Mo.exports}var Eo={exports:{}},vt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mm;function Ig(){if(mm)return vt;mm=1;var n=Ts();function u(p){var g="https://react.dev/errors/"+p;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var x=2;x<arguments.length;x++)g+="&args[]="+encodeURIComponent(arguments[x])}return"Minified React error #"+p+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var c={d:{f:r,r:function(){throw Error(u(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},d=Symbol.for("react.portal");function m(p,g,x){var b=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:d,key:b==null?null:""+b,children:p,containerInfo:g,implementation:x}}var f=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function v(p,g){if(p==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return vt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,vt.createPortal=function(p,g){var x=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(u(299));return m(p,g,null,x)},vt.flushSync=function(p){var g=f.T,x=c.p;try{if(f.T=null,c.p=2,p)return p()}finally{f.T=g,c.p=x,c.d.f()}},vt.preconnect=function(p,g){typeof p=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,c.d.C(p,g))},vt.prefetchDNS=function(p){typeof p=="string"&&c.d.D(p)},vt.preinit=function(p,g){if(typeof p=="string"&&g&&typeof g.as=="string"){var x=g.as,b=v(x,g.crossOrigin),j=typeof g.integrity=="string"?g.integrity:void 0,T=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;x==="style"?c.d.S(p,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:b,integrity:j,fetchPriority:T}):x==="script"&&c.d.X(p,{crossOrigin:b,integrity:j,fetchPriority:T,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},vt.preinitModule=function(p,g){if(typeof p=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var x=v(g.as,g.crossOrigin);c.d.M(p,{crossOrigin:x,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&c.d.M(p)},vt.preload=function(p,g){if(typeof p=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var x=g.as,b=v(x,g.crossOrigin);c.d.L(p,x,{crossOrigin:b,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},vt.preloadModule=function(p,g){if(typeof p=="string")if(g){var x=v(g.as,g.crossOrigin);c.d.m(p,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:x,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else c.d.m(p)},vt.requestFormReset=function(p){c.d.r(p)},vt.unstable_batchedUpdates=function(p,g){return p(g)},vt.useFormState=function(p,g,x){return f.H.useFormState(p,g,x)},vt.useFormStatus=function(){return f.H.useHostTransitionStatus()},vt.version="19.1.0",vt}var pm;function Jm(){if(pm)return Eo.exports;pm=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(u){console.error(u)}}return n(),Eo.exports=Ig(),Eo.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gm;function ev(){if(gm)return ys;gm=1;var n=Wg(),u=Ts(),r=Jm();function c(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function m(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function f(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function v(e){if(m(e)!==e)throw Error(c(188))}function p(e){var t=e.alternate;if(!t){if(t=m(e),t===null)throw Error(c(188));return t!==e?null:e}for(var a=e,l=t;;){var i=a.return;if(i===null)break;var o=i.alternate;if(o===null){if(l=i.return,l!==null){a=l;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===a)return v(i),e;if(o===l)return v(i),t;o=o.sibling}throw Error(c(188))}if(a.return!==l.return)a=i,l=o;else{for(var h=!1,y=i.child;y;){if(y===a){h=!0,a=i,l=o;break}if(y===l){h=!0,l=i,a=o;break}y=y.sibling}if(!h){for(y=o.child;y;){if(y===a){h=!0,a=o,l=i;break}if(y===l){h=!0,l=o,a=i;break}y=y.sibling}if(!h)throw Error(c(189))}}if(a.alternate!==l)throw Error(c(190))}if(a.tag!==3)throw Error(c(188));return a.stateNode.current===a?e:t}function g(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=g(e),t!==null)return t;e=e.sibling}return null}var x=Object.assign,b=Symbol.for("react.element"),j=Symbol.for("react.transitional.element"),T=Symbol.for("react.portal"),R=Symbol.for("react.fragment"),N=Symbol.for("react.strict_mode"),S=Symbol.for("react.profiler"),A=Symbol.for("react.provider"),X=Symbol.for("react.consumer"),q=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),ee=Symbol.for("react.suspense"),re=Symbol.for("react.suspense_list"),K=Symbol.for("react.memo"),V=Symbol.for("react.lazy"),oe=Symbol.for("react.activity"),te=Symbol.for("react.memo_cache_sentinel"),$=Symbol.iterator;function W(e){return e===null||typeof e!="object"?null:(e=$&&e[$]||e["@@iterator"],typeof e=="function"?e:null)}var me=Symbol.for("react.client.reference");function be(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===me?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case R:return"Fragment";case S:return"Profiler";case N:return"StrictMode";case ee:return"Suspense";case re:return"SuspenseList";case oe:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case T:return"Portal";case q:return(e.displayName||"Context")+".Provider";case X:return(e._context.displayName||"Context")+".Consumer";case P:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case K:return t=e.displayName||null,t!==null?t:be(e.type)||"Memo";case V:t=e._payload,e=e._init;try{return be(e(t))}catch{}}return null}var ge=Array.isArray,C=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Q=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,U={pending:!1,data:null,method:null,action:null},ue=[],_=-1;function L(e){return{current:e}}function J(e){0>_||(e.current=ue[_],ue[_]=null,_--)}function Z(e,t){_++,ue[_]=e.current,e.current=t}var F=L(null),se=L(null),I=L(null),ce=L(null);function pe(e,t){switch(Z(I,t),Z(se,e),Z(F,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?Uh(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=Uh(t),e=Lh(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}J(F),Z(F,e)}function _e(){J(F),J(se),J(I)}function Ce(e){e.memoizedState!==null&&Z(ce,e);var t=F.current,a=Lh(t,e.type);t!==a&&(Z(se,e),Z(F,a))}function et(e){se.current===e&&(J(F),J(se)),ce.current===e&&(J(ce),hs._currentValue=U)}var Ke=Object.prototype.hasOwnProperty,Jt=n.unstable_scheduleCallback,xn=n.unstable_cancelCallback,ur=n.unstable_shouldYield,dr=n.unstable_requestPaint,mt=n.unstable_now,yn=n.unstable_getCurrentPriorityLevel,bl=n.unstable_ImmediatePriority,bn=n.unstable_UserBlockingPriority,Pa=n.unstable_NormalPriority,Ge=n.unstable_LowPriority,Fe=n.unstable_IdlePriority,Sn=n.log,Ds=n.unstable_setDisableYieldValue,Wa=null,yt=null;function wa(e){if(typeof Sn=="function"&&Ds(e),yt&&typeof yt.setStrictMode=="function")try{yt.setStrictMode(Wa,e)}catch{}}var Rt=Math.clz32?Math.clz32:Op,Ap=Math.log,zp=Math.LN2;function Op(e){return e>>>=0,e===0?32:31-(Ap(e)/zp|0)|0}var As=256,zs=4194304;function Ia(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Os(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var i=0,o=e.suspendedLanes,h=e.pingedLanes;e=e.warmLanes;var y=l&134217727;return y!==0?(l=y&~o,l!==0?i=Ia(l):(h&=y,h!==0?i=Ia(h):a||(a=y&~e,a!==0&&(i=Ia(a))))):(y=l&~o,y!==0?i=Ia(y):h!==0?i=Ia(h):a||(a=l&~e,a!==0&&(i=Ia(a)))),i===0?0:t!==0&&t!==i&&(t&o)===0&&(o=i&-i,a=t&-t,o>=a||o===32&&(a&4194048)!==0)?t:i}function jn(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function kp(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function xu(){var e=As;return As<<=1,(As&4194048)===0&&(As=256),e}function yu(){var e=zs;return zs<<=1,(zs&62914560)===0&&(zs=4194304),e}function fr(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function Nn(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Up(e,t,a,l,i,o){var h=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var y=e.entanglements,w=e.expirationTimes,z=e.hiddenUpdates;for(a=h&~a;0<a;){var B=31-Rt(a),Y=1<<B;y[B]=0,w[B]=-1;var O=z[B];if(O!==null)for(z[B]=null,B=0;B<O.length;B++){var k=O[B];k!==null&&(k.lane&=-536870913)}a&=~Y}l!==0&&bu(e,l,0),o!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=o&~(h&~t))}function bu(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-Rt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function Su(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-Rt(a),i=1<<l;i&t|e[l]&t&&(e[l]|=t),a&=~i}}function hr(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function mr(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function ju(){var e=Q.p;return e!==0?e:(e=window.event,e===void 0?32:am(e.type))}function Lp(e,t){var a=Q.p;try{return Q.p=e,t()}finally{Q.p=a}}var _a=Math.random().toString(36).slice(2),pt="__reactFiber$"+_a,St="__reactProps$"+_a,Sl="__reactContainer$"+_a,pr="__reactEvents$"+_a,Bp="__reactListeners$"+_a,qp="__reactHandles$"+_a,Nu="__reactResources$"+_a,wn="__reactMarker$"+_a;function gr(e){delete e[pt],delete e[St],delete e[pr],delete e[Bp],delete e[qp]}function jl(e){var t=e[pt];if(t)return t;for(var a=e.parentNode;a;){if(t=a[Sl]||a[pt]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=Vh(e);e!==null;){if(a=e[pt])return a;e=Vh(e)}return t}e=a,a=e.parentNode}return null}function Nl(e){if(e=e[pt]||e[Sl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function _n(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(c(33))}function wl(e){var t=e[Nu];return t||(t=e[Nu]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function rt(e){e[wn]=!0}var wu=new Set,_u={};function el(e,t){_l(e,t),_l(e+"Capture",t)}function _l(e,t){for(_u[e]=t,e=0;e<t.length;e++)wu.add(t[e])}var Hp=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ru={},Mu={};function Vp(e){return Ke.call(Mu,e)?!0:Ke.call(Ru,e)?!1:Hp.test(e)?Mu[e]=!0:(Ru[e]=!0,!1)}function ks(e,t,a){if(Vp(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function Us(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function ia(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var vr,Tu;function Rl(e){if(vr===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);vr=t&&t[1]||"",Tu=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+vr+e+Tu}var xr=!1;function yr(e,t){if(!e||xr)return"";xr=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var Y=function(){throw Error()};if(Object.defineProperty(Y.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Y,[])}catch(k){var O=k}Reflect.construct(e,[],Y)}else{try{Y.call()}catch(k){O=k}e.call(Y.prototype)}}else{try{throw Error()}catch(k){O=k}(Y=e())&&typeof Y.catch=="function"&&Y.catch(function(){})}}catch(k){if(k&&O&&typeof k.stack=="string")return[k.stack,O.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=l.DetermineComponentFrameRoot(),h=o[0],y=o[1];if(h&&y){var w=h.split(`
`),z=y.split(`
`);for(i=l=0;l<w.length&&!w[l].includes("DetermineComponentFrameRoot");)l++;for(;i<z.length&&!z[i].includes("DetermineComponentFrameRoot");)i++;if(l===w.length||i===z.length)for(l=w.length-1,i=z.length-1;1<=l&&0<=i&&w[l]!==z[i];)i--;for(;1<=l&&0<=i;l--,i--)if(w[l]!==z[i]){if(l!==1||i!==1)do if(l--,i--,0>i||w[l]!==z[i]){var B=`
`+w[l].replace(" at new "," at ");return e.displayName&&B.includes("<anonymous>")&&(B=B.replace("<anonymous>",e.displayName)),B}while(1<=l&&0<=i);break}}}finally{xr=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?Rl(a):""}function Gp(e){switch(e.tag){case 26:case 27:case 5:return Rl(e.type);case 16:return Rl("Lazy");case 13:return Rl("Suspense");case 19:return Rl("SuspenseList");case 0:case 15:return yr(e.type,!1);case 11:return yr(e.type.render,!1);case 1:return yr(e.type,!0);case 31:return Rl("Activity");default:return""}}function Eu(e){try{var t="";do t+=Gp(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function kt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Cu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Yp(e){var t=Cu(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var i=a.get,o=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(h){l=""+h,o.call(this,h)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(h){l=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ls(e){e._valueTracker||(e._valueTracker=Yp(e))}function Du(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=Cu(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function Bs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Xp=/[\n"\\]/g;function Ut(e){return e.replace(Xp,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function br(e,t,a,l,i,o,h,y){e.name="",h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.type=h:e.removeAttribute("type"),t!=null?h==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+kt(t)):e.value!==""+kt(t)&&(e.value=""+kt(t)):h!=="submit"&&h!=="reset"||e.removeAttribute("value"),t!=null?Sr(e,h,kt(t)):a!=null?Sr(e,h,kt(a)):l!=null&&e.removeAttribute("value"),i==null&&o!=null&&(e.defaultChecked=!!o),i!=null&&(e.checked=i&&typeof i!="function"&&typeof i!="symbol"),y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.name=""+kt(y):e.removeAttribute("name")}function Au(e,t,a,l,i,o,h,y){if(o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(e.type=o),t!=null||a!=null){if(!(o!=="submit"&&o!=="reset"||t!=null))return;a=a!=null?""+kt(a):"",t=t!=null?""+kt(t):a,y||t===e.value||(e.value=t),e.defaultValue=t}l=l??i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=y?e.checked:!!l,e.defaultChecked=!!l,h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"&&(e.name=h)}function Sr(e,t,a){t==="number"&&Bs(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function Ml(e,t,a,l){if(e=e.options,t){t={};for(var i=0;i<a.length;i++)t["$"+a[i]]=!0;for(a=0;a<e.length;a++)i=t.hasOwnProperty("$"+e[a].value),e[a].selected!==i&&(e[a].selected=i),i&&l&&(e[a].defaultSelected=!0)}else{for(a=""+kt(a),t=null,i=0;i<e.length;i++){if(e[i].value===a){e[i].selected=!0,l&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function zu(e,t,a){if(t!=null&&(t=""+kt(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+kt(a):""}function Ou(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(c(92));if(ge(l)){if(1<l.length)throw Error(c(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=kt(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function Tl(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var Qp=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function ku(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||Qp.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function Uu(e,t,a){if(t!=null&&typeof t!="object")throw Error(c(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var i in t)l=t[i],t.hasOwnProperty(i)&&a[i]!==l&&ku(e,i,l)}else for(var o in t)t.hasOwnProperty(o)&&ku(e,o,t[o])}function jr(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Zp=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Kp=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function qs(e){return Kp.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Nr=null;function wr(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var El=null,Cl=null;function Lu(e){var t=Nl(e);if(t&&(e=t.stateNode)){var a=e[St]||null;e:switch(e=t.stateNode,t.type){case"input":if(br(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Ut(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var i=l[St]||null;if(!i)throw Error(c(90));br(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&Du(l)}break e;case"textarea":zu(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&Ml(e,!!a.multiple,t,!1)}}}var _r=!1;function Bu(e,t,a){if(_r)return e(t,a);_r=!0;try{var l=e(t);return l}finally{if(_r=!1,(El!==null||Cl!==null)&&(wi(),El&&(t=El,e=Cl,Cl=El=null,Lu(t),e)))for(t=0;t<e.length;t++)Lu(e[t])}}function Rn(e,t){var a=e.stateNode;if(a===null)return null;var l=a[St]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(c(231,t,typeof a));return a}var ra=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Rr=!1;if(ra)try{var Mn={};Object.defineProperty(Mn,"passive",{get:function(){Rr=!0}}),window.addEventListener("test",Mn,Mn),window.removeEventListener("test",Mn,Mn)}catch{Rr=!1}var Ra=null,Mr=null,Hs=null;function qu(){if(Hs)return Hs;var e,t=Mr,a=t.length,l,i="value"in Ra?Ra.value:Ra.textContent,o=i.length;for(e=0;e<a&&t[e]===i[e];e++);var h=a-e;for(l=1;l<=h&&t[a-l]===i[o-l];l++);return Hs=i.slice(e,1<l?1-l:void 0)}function Vs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Gs(){return!0}function Hu(){return!1}function jt(e){function t(a,l,i,o,h){this._reactName=a,this._targetInst=i,this.type=l,this.nativeEvent=o,this.target=h,this.currentTarget=null;for(var y in e)e.hasOwnProperty(y)&&(a=e[y],this[y]=a?a(o):o[y]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Gs:Hu,this.isPropagationStopped=Hu,this}return x(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Gs)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Gs)},persist:function(){},isPersistent:Gs}),t}var tl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ys=jt(tl),Tn=x({},tl,{view:0,detail:0}),$p=jt(Tn),Tr,Er,En,Xs=x({},Tn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Dr,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==En&&(En&&e.type==="mousemove"?(Tr=e.screenX-En.screenX,Er=e.screenY-En.screenY):Er=Tr=0,En=e),Tr)},movementY:function(e){return"movementY"in e?e.movementY:Er}}),Vu=jt(Xs),Jp=x({},Xs,{dataTransfer:0}),Fp=jt(Jp),Pp=x({},Tn,{relatedTarget:0}),Cr=jt(Pp),Wp=x({},tl,{animationName:0,elapsedTime:0,pseudoElement:0}),Ip=jt(Wp),e0=x({},tl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),t0=jt(e0),a0=x({},tl,{data:0}),Gu=jt(a0),l0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},n0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},s0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function i0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=s0[e])?!!t[e]:!1}function Dr(){return i0}var r0=x({},Tn,{key:function(e){if(e.key){var t=l0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Vs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?n0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Dr,charCode:function(e){return e.type==="keypress"?Vs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Vs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),c0=jt(r0),o0=x({},Xs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Yu=jt(o0),u0=x({},Tn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Dr}),d0=jt(u0),f0=x({},tl,{propertyName:0,elapsedTime:0,pseudoElement:0}),h0=jt(f0),m0=x({},Xs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),p0=jt(m0),g0=x({},tl,{newState:0,oldState:0}),v0=jt(g0),x0=[9,13,27,32],Ar=ra&&"CompositionEvent"in window,Cn=null;ra&&"documentMode"in document&&(Cn=document.documentMode);var y0=ra&&"TextEvent"in window&&!Cn,Xu=ra&&(!Ar||Cn&&8<Cn&&11>=Cn),Qu=" ",Zu=!1;function Ku(e,t){switch(e){case"keyup":return x0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function $u(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Dl=!1;function b0(e,t){switch(e){case"compositionend":return $u(t);case"keypress":return t.which!==32?null:(Zu=!0,Qu);case"textInput":return e=t.data,e===Qu&&Zu?null:e;default:return null}}function S0(e,t){if(Dl)return e==="compositionend"||!Ar&&Ku(e,t)?(e=qu(),Hs=Mr=Ra=null,Dl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Xu&&t.locale!=="ko"?null:t.data;default:return null}}var j0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ju(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!j0[e.type]:t==="textarea"}function Fu(e,t,a,l){El?Cl?Cl.push(l):Cl=[l]:El=l,t=Ci(t,"onChange"),0<t.length&&(a=new Ys("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var Dn=null,An=null;function N0(e){Dh(e,0)}function Qs(e){var t=_n(e);if(Du(t))return e}function Pu(e,t){if(e==="change")return t}var Wu=!1;if(ra){var zr;if(ra){var Or="oninput"in document;if(!Or){var Iu=document.createElement("div");Iu.setAttribute("oninput","return;"),Or=typeof Iu.oninput=="function"}zr=Or}else zr=!1;Wu=zr&&(!document.documentMode||9<document.documentMode)}function ed(){Dn&&(Dn.detachEvent("onpropertychange",td),An=Dn=null)}function td(e){if(e.propertyName==="value"&&Qs(An)){var t=[];Fu(t,An,e,wr(e)),Bu(N0,t)}}function w0(e,t,a){e==="focusin"?(ed(),Dn=t,An=a,Dn.attachEvent("onpropertychange",td)):e==="focusout"&&ed()}function _0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Qs(An)}function R0(e,t){if(e==="click")return Qs(t)}function M0(e,t){if(e==="input"||e==="change")return Qs(t)}function T0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Mt=typeof Object.is=="function"?Object.is:T0;function zn(e,t){if(Mt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var i=a[l];if(!Ke.call(t,i)||!Mt(e[i],t[i]))return!1}return!0}function ad(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ld(e,t){var a=ad(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=ad(a)}}function nd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?nd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function sd(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Bs(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=Bs(e.document)}return t}function kr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var E0=ra&&"documentMode"in document&&11>=document.documentMode,Al=null,Ur=null,On=null,Lr=!1;function id(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;Lr||Al==null||Al!==Bs(l)||(l=Al,"selectionStart"in l&&kr(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),On&&zn(On,l)||(On=l,l=Ci(Ur,"onSelect"),0<l.length&&(t=new Ys("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=Al)))}function al(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var zl={animationend:al("Animation","AnimationEnd"),animationiteration:al("Animation","AnimationIteration"),animationstart:al("Animation","AnimationStart"),transitionrun:al("Transition","TransitionRun"),transitionstart:al("Transition","TransitionStart"),transitioncancel:al("Transition","TransitionCancel"),transitionend:al("Transition","TransitionEnd")},Br={},rd={};ra&&(rd=document.createElement("div").style,"AnimationEvent"in window||(delete zl.animationend.animation,delete zl.animationiteration.animation,delete zl.animationstart.animation),"TransitionEvent"in window||delete zl.transitionend.transition);function ll(e){if(Br[e])return Br[e];if(!zl[e])return e;var t=zl[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in rd)return Br[e]=t[a];return e}var cd=ll("animationend"),od=ll("animationiteration"),ud=ll("animationstart"),C0=ll("transitionrun"),D0=ll("transitionstart"),A0=ll("transitioncancel"),dd=ll("transitionend"),fd=new Map,qr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");qr.push("scrollEnd");function Ft(e,t){fd.set(e,t),el(t,[e])}var hd=new WeakMap;function Lt(e,t){if(typeof e=="object"&&e!==null){var a=hd.get(e);return a!==void 0?a:(t={value:e,source:t,stack:Eu(t)},hd.set(e,t),t)}return{value:e,source:t,stack:Eu(t)}}var Bt=[],Ol=0,Hr=0;function Zs(){for(var e=Ol,t=Hr=Ol=0;t<e;){var a=Bt[t];Bt[t++]=null;var l=Bt[t];Bt[t++]=null;var i=Bt[t];Bt[t++]=null;var o=Bt[t];if(Bt[t++]=null,l!==null&&i!==null){var h=l.pending;h===null?i.next=i:(i.next=h.next,h.next=i),l.pending=i}o!==0&&md(a,i,o)}}function Ks(e,t,a,l){Bt[Ol++]=e,Bt[Ol++]=t,Bt[Ol++]=a,Bt[Ol++]=l,Hr|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function Vr(e,t,a,l){return Ks(e,t,a,l),$s(e)}function kl(e,t){return Ks(e,null,null,t),$s(e)}function md(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var i=!1,o=e.return;o!==null;)o.childLanes|=a,l=o.alternate,l!==null&&(l.childLanes|=a),o.tag===22&&(e=o.stateNode,e===null||e._visibility&1||(i=!0)),e=o,o=o.return;return e.tag===3?(o=e.stateNode,i&&t!==null&&(i=31-Rt(a),e=o.hiddenUpdates,l=e[i],l===null?e[i]=[t]:l.push(t),t.lane=a|536870912),o):null}function $s(e){if(50<ss)throw ss=0,Kc=null,Error(c(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Ul={};function z0(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Tt(e,t,a,l){return new z0(e,t,a,l)}function Gr(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ca(e,t){var a=e.alternate;return a===null?(a=Tt(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function pd(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Js(e,t,a,l,i,o){var h=0;if(l=e,typeof e=="function")Gr(e)&&(h=1);else if(typeof e=="string")h=kg(e,a,F.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case oe:return e=Tt(31,a,t,i),e.elementType=oe,e.lanes=o,e;case R:return nl(a.children,i,o,t);case N:h=8,i|=24;break;case S:return e=Tt(12,a,t,i|2),e.elementType=S,e.lanes=o,e;case ee:return e=Tt(13,a,t,i),e.elementType=ee,e.lanes=o,e;case re:return e=Tt(19,a,t,i),e.elementType=re,e.lanes=o,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case A:case q:h=10;break e;case X:h=9;break e;case P:h=11;break e;case K:h=14;break e;case V:h=16,l=null;break e}h=29,a=Error(c(130,e===null?"null":typeof e,"")),l=null}return t=Tt(h,a,t,i),t.elementType=e,t.type=l,t.lanes=o,t}function nl(e,t,a,l){return e=Tt(7,e,l,t),e.lanes=a,e}function Yr(e,t,a){return e=Tt(6,e,null,t),e.lanes=a,e}function Xr(e,t,a){return t=Tt(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Ll=[],Bl=0,Fs=null,Ps=0,qt=[],Ht=0,sl=null,oa=1,ua="";function il(e,t){Ll[Bl++]=Ps,Ll[Bl++]=Fs,Fs=e,Ps=t}function gd(e,t,a){qt[Ht++]=oa,qt[Ht++]=ua,qt[Ht++]=sl,sl=e;var l=oa;e=ua;var i=32-Rt(l)-1;l&=~(1<<i),a+=1;var o=32-Rt(t)+i;if(30<o){var h=i-i%5;o=(l&(1<<h)-1).toString(32),l>>=h,i-=h,oa=1<<32-Rt(t)+i|a<<i|l,ua=o+e}else oa=1<<o|a<<i|l,ua=e}function Qr(e){e.return!==null&&(il(e,1),gd(e,1,0))}function Zr(e){for(;e===Fs;)Fs=Ll[--Bl],Ll[Bl]=null,Ps=Ll[--Bl],Ll[Bl]=null;for(;e===sl;)sl=qt[--Ht],qt[Ht]=null,ua=qt[--Ht],qt[Ht]=null,oa=qt[--Ht],qt[Ht]=null}var bt=null,$e=null,ze=!1,rl=null,ea=!1,Kr=Error(c(519));function cl(e){var t=Error(c(418,""));throw Ln(Lt(t,e)),Kr}function vd(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[pt]=e,t[St]=l,a){case"dialog":we("cancel",t),we("close",t);break;case"iframe":case"object":case"embed":we("load",t);break;case"video":case"audio":for(a=0;a<rs.length;a++)we(rs[a],t);break;case"source":we("error",t);break;case"img":case"image":case"link":we("error",t),we("load",t);break;case"details":we("toggle",t);break;case"input":we("invalid",t),Au(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),Ls(t);break;case"select":we("invalid",t);break;case"textarea":we("invalid",t),Ou(t,l.value,l.defaultValue,l.children),Ls(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||kh(t.textContent,a)?(l.popover!=null&&(we("beforetoggle",t),we("toggle",t)),l.onScroll!=null&&we("scroll",t),l.onScrollEnd!=null&&we("scrollend",t),l.onClick!=null&&(t.onclick=Di),t=!0):t=!1,t||cl(e)}function xd(e){for(bt=e.return;bt;)switch(bt.tag){case 5:case 13:ea=!1;return;case 27:case 3:ea=!0;return;default:bt=bt.return}}function kn(e){if(e!==bt)return!1;if(!ze)return xd(e),ze=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||oo(e.type,e.memoizedProps)),a=!a),a&&$e&&cl(e),xd(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(c(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){$e=Wt(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}$e=null}}else t===27?(t=$e,Ga(e.type)?(e=mo,mo=null,$e=e):$e=t):$e=bt?Wt(e.stateNode.nextSibling):null;return!0}function Un(){$e=bt=null,ze=!1}function yd(){var e=rl;return e!==null&&(_t===null?_t=e:_t.push.apply(_t,e),rl=null),e}function Ln(e){rl===null?rl=[e]:rl.push(e)}var $r=L(null),ol=null,da=null;function Ma(e,t,a){Z($r,t._currentValue),t._currentValue=a}function fa(e){e._currentValue=$r.current,J($r)}function Jr(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function Fr(e,t,a,l){var i=e.child;for(i!==null&&(i.return=e);i!==null;){var o=i.dependencies;if(o!==null){var h=i.child;o=o.firstContext;e:for(;o!==null;){var y=o;o=i;for(var w=0;w<t.length;w++)if(y.context===t[w]){o.lanes|=a,y=o.alternate,y!==null&&(y.lanes|=a),Jr(o.return,a,e),l||(h=null);break e}o=y.next}}else if(i.tag===18){if(h=i.return,h===null)throw Error(c(341));h.lanes|=a,o=h.alternate,o!==null&&(o.lanes|=a),Jr(h,a,e),h=null}else h=i.child;if(h!==null)h.return=i;else for(h=i;h!==null;){if(h===e){h=null;break}if(i=h.sibling,i!==null){i.return=h.return,h=i;break}h=h.return}i=h}}function Bn(e,t,a,l){e=null;for(var i=t,o=!1;i!==null;){if(!o){if((i.flags&524288)!==0)o=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var h=i.alternate;if(h===null)throw Error(c(387));if(h=h.memoizedProps,h!==null){var y=i.type;Mt(i.pendingProps.value,h.value)||(e!==null?e.push(y):e=[y])}}else if(i===ce.current){if(h=i.alternate,h===null)throw Error(c(387));h.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(e!==null?e.push(hs):e=[hs])}i=i.return}e!==null&&Fr(t,e,a,l),t.flags|=262144}function Ws(e){for(e=e.firstContext;e!==null;){if(!Mt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function ul(e){ol=e,da=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function gt(e){return bd(ol,e)}function Is(e,t){return ol===null&&ul(e),bd(e,t)}function bd(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},da===null){if(e===null)throw Error(c(308));da=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else da=da.next=t;return a}var O0=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},k0=n.unstable_scheduleCallback,U0=n.unstable_NormalPriority,lt={$$typeof:q,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Pr(){return{controller:new O0,data:new Map,refCount:0}}function qn(e){e.refCount--,e.refCount===0&&k0(U0,function(){e.controller.abort()})}var Hn=null,Wr=0,ql=0,Hl=null;function L0(e,t){if(Hn===null){var a=Hn=[];Wr=0,ql=eo(),Hl={status:"pending",value:void 0,then:function(l){a.push(l)}}}return Wr++,t.then(Sd,Sd),t}function Sd(){if(--Wr===0&&Hn!==null){Hl!==null&&(Hl.status="fulfilled");var e=Hn;Hn=null,ql=0,Hl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function B0(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(i){a.push(i)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var i=0;i<a.length;i++)(0,a[i])(t)},function(i){for(l.status="rejected",l.reason=i,i=0;i<a.length;i++)(0,a[i])(void 0)}),l}var jd=C.S;C.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&L0(e,t),jd!==null&&jd(e,t)};var dl=L(null);function Ir(){var e=dl.current;return e!==null?e:Xe.pooledCache}function ei(e,t){t===null?Z(dl,dl.current):Z(dl,t.pool)}function Nd(){var e=Ir();return e===null?null:{parent:lt._currentValue,pool:e}}var Vn=Error(c(460)),wd=Error(c(474)),ti=Error(c(542)),ec={then:function(){}};function _d(e){return e=e.status,e==="fulfilled"||e==="rejected"}function ai(){}function Rd(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(ai,ai),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Td(e),e;default:if(typeof t.status=="string")t.then(ai,ai);else{if(e=Xe,e!==null&&100<e.shellSuspendCounter)throw Error(c(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var i=t;i.status="fulfilled",i.value=l}},function(l){if(t.status==="pending"){var i=t;i.status="rejected",i.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Td(e),e}throw Gn=t,Vn}}var Gn=null;function Md(){if(Gn===null)throw Error(c(459));var e=Gn;return Gn=null,e}function Td(e){if(e===Vn||e===ti)throw Error(c(483))}var Ta=!1;function tc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ac(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Ea(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Ca(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(Le&2)!==0){var i=l.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),l.pending=t,t=$s(e),md(e,null,a),t}return Ks(e,l,t,a),$s(e)}function Yn(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Su(e,a)}}function lc(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var i=null,o=null;if(a=a.firstBaseUpdate,a!==null){do{var h={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};o===null?i=o=h:o=o.next=h,a=a.next}while(a!==null);o===null?i=o=t:o=o.next=t}else i=o=t;a={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var nc=!1;function Xn(){if(nc){var e=Hl;if(e!==null)throw e}}function Qn(e,t,a,l){nc=!1;var i=e.updateQueue;Ta=!1;var o=i.firstBaseUpdate,h=i.lastBaseUpdate,y=i.shared.pending;if(y!==null){i.shared.pending=null;var w=y,z=w.next;w.next=null,h===null?o=z:h.next=z,h=w;var B=e.alternate;B!==null&&(B=B.updateQueue,y=B.lastBaseUpdate,y!==h&&(y===null?B.firstBaseUpdate=z:y.next=z,B.lastBaseUpdate=w))}if(o!==null){var Y=i.baseState;h=0,B=z=w=null,y=o;do{var O=y.lane&-536870913,k=O!==y.lane;if(k?(Te&O)===O:(l&O)===O){O!==0&&O===ql&&(nc=!0),B!==null&&(B=B.next={lane:0,tag:y.tag,payload:y.payload,callback:null,next:null});e:{var he=e,de=y;O=t;var Ve=a;switch(de.tag){case 1:if(he=de.payload,typeof he=="function"){Y=he.call(Ve,Y,O);break e}Y=he;break e;case 3:he.flags=he.flags&-65537|128;case 0:if(he=de.payload,O=typeof he=="function"?he.call(Ve,Y,O):he,O==null)break e;Y=x({},Y,O);break e;case 2:Ta=!0}}O=y.callback,O!==null&&(e.flags|=64,k&&(e.flags|=8192),k=i.callbacks,k===null?i.callbacks=[O]:k.push(O))}else k={lane:O,tag:y.tag,payload:y.payload,callback:y.callback,next:null},B===null?(z=B=k,w=Y):B=B.next=k,h|=O;if(y=y.next,y===null){if(y=i.shared.pending,y===null)break;k=y,y=k.next,k.next=null,i.lastBaseUpdate=k,i.shared.pending=null}}while(!0);B===null&&(w=Y),i.baseState=w,i.firstBaseUpdate=z,i.lastBaseUpdate=B,o===null&&(i.shared.lanes=0),Ba|=h,e.lanes=h,e.memoizedState=Y}}function Ed(e,t){if(typeof e!="function")throw Error(c(191,e));e.call(t)}function Cd(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)Ed(a[e],t)}var Vl=L(null),li=L(0);function Dd(e,t){e=ya,Z(li,e),Z(Vl,t),ya=e|t.baseLanes}function sc(){Z(li,ya),Z(Vl,Vl.current)}function ic(){ya=li.current,J(Vl),J(li)}var Da=0,xe=null,qe=null,tt=null,ni=!1,Gl=!1,fl=!1,si=0,Zn=0,Yl=null,q0=0;function Pe(){throw Error(c(321))}function rc(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!Mt(e[a],t[a]))return!1;return!0}function cc(e,t,a,l,i,o){return Da=o,xe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,C.H=e===null||e.memoizedState===null?pf:gf,fl=!1,o=a(l,i),fl=!1,Gl&&(o=zd(t,a,l,i)),Ad(e),o}function Ad(e){C.H=di;var t=qe!==null&&qe.next!==null;if(Da=0,tt=qe=xe=null,ni=!1,Zn=0,Yl=null,t)throw Error(c(300));e===null||ct||(e=e.dependencies,e!==null&&Ws(e)&&(ct=!0))}function zd(e,t,a,l){xe=e;var i=0;do{if(Gl&&(Yl=null),Zn=0,Gl=!1,25<=i)throw Error(c(301));if(i+=1,tt=qe=null,e.updateQueue!=null){var o=e.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,o.memoCache!=null&&(o.memoCache.index=0)}C.H=Z0,o=t(a,l)}while(Gl);return o}function H0(){var e=C.H,t=e.useState()[0];return t=typeof t.then=="function"?Kn(t):t,e=e.useState()[0],(qe!==null?qe.memoizedState:null)!==e&&(xe.flags|=1024),t}function oc(){var e=si!==0;return si=0,e}function uc(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function dc(e){if(ni){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}ni=!1}Da=0,tt=qe=xe=null,Gl=!1,Zn=si=0,Yl=null}function Nt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return tt===null?xe.memoizedState=tt=e:tt=tt.next=e,tt}function at(){if(qe===null){var e=xe.alternate;e=e!==null?e.memoizedState:null}else e=qe.next;var t=tt===null?xe.memoizedState:tt.next;if(t!==null)tt=t,qe=e;else{if(e===null)throw xe.alternate===null?Error(c(467)):Error(c(310));qe=e,e={memoizedState:qe.memoizedState,baseState:qe.baseState,baseQueue:qe.baseQueue,queue:qe.queue,next:null},tt===null?xe.memoizedState=tt=e:tt=tt.next=e}return tt}function fc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Kn(e){var t=Zn;return Zn+=1,Yl===null&&(Yl=[]),e=Rd(Yl,e,t),t=xe,(tt===null?t.memoizedState:tt.next)===null&&(t=t.alternate,C.H=t===null||t.memoizedState===null?pf:gf),e}function ii(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Kn(e);if(e.$$typeof===q)return gt(e)}throw Error(c(438,String(e)))}function hc(e){var t=null,a=xe.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=xe.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(i){return i.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=fc(),xe.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=te;return t.index++,a}function ha(e,t){return typeof t=="function"?t(e):t}function ri(e){var t=at();return mc(t,qe,e)}function mc(e,t,a){var l=e.queue;if(l===null)throw Error(c(311));l.lastRenderedReducer=a;var i=e.baseQueue,o=l.pending;if(o!==null){if(i!==null){var h=i.next;i.next=o.next,o.next=h}t.baseQueue=i=o,l.pending=null}if(o=e.baseState,i===null)e.memoizedState=o;else{t=i.next;var y=h=null,w=null,z=t,B=!1;do{var Y=z.lane&-536870913;if(Y!==z.lane?(Te&Y)===Y:(Da&Y)===Y){var O=z.revertLane;if(O===0)w!==null&&(w=w.next={lane:0,revertLane:0,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null}),Y===ql&&(B=!0);else if((Da&O)===O){z=z.next,O===ql&&(B=!0);continue}else Y={lane:0,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},w===null?(y=w=Y,h=o):w=w.next=Y,xe.lanes|=O,Ba|=O;Y=z.action,fl&&a(o,Y),o=z.hasEagerState?z.eagerState:a(o,Y)}else O={lane:Y,revertLane:z.revertLane,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},w===null?(y=w=O,h=o):w=w.next=O,xe.lanes|=Y,Ba|=Y;z=z.next}while(z!==null&&z!==t);if(w===null?h=o:w.next=y,!Mt(o,e.memoizedState)&&(ct=!0,B&&(a=Hl,a!==null)))throw a;e.memoizedState=o,e.baseState=h,e.baseQueue=w,l.lastRenderedState=o}return i===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function pc(e){var t=at(),a=t.queue;if(a===null)throw Error(c(311));a.lastRenderedReducer=e;var l=a.dispatch,i=a.pending,o=t.memoizedState;if(i!==null){a.pending=null;var h=i=i.next;do o=e(o,h.action),h=h.next;while(h!==i);Mt(o,t.memoizedState)||(ct=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),a.lastRenderedState=o}return[o,l]}function Od(e,t,a){var l=xe,i=at(),o=ze;if(o){if(a===void 0)throw Error(c(407));a=a()}else a=t();var h=!Mt((qe||i).memoizedState,a);h&&(i.memoizedState=a,ct=!0),i=i.queue;var y=Ld.bind(null,l,i,e);if($n(2048,8,y,[e]),i.getSnapshot!==t||h||tt!==null&&tt.memoizedState.tag&1){if(l.flags|=2048,Xl(9,ci(),Ud.bind(null,l,i,a,t),null),Xe===null)throw Error(c(349));o||(Da&124)!==0||kd(l,t,a)}return a}function kd(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=xe.updateQueue,t===null?(t=fc(),xe.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function Ud(e,t,a,l){t.value=a,t.getSnapshot=l,Bd(t)&&qd(e)}function Ld(e,t,a){return a(function(){Bd(t)&&qd(e)})}function Bd(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!Mt(e,a)}catch{return!0}}function qd(e){var t=kl(e,2);t!==null&&zt(t,e,2)}function gc(e){var t=Nt();if(typeof e=="function"){var a=e;if(e=a(),fl){wa(!0);try{a()}finally{wa(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ha,lastRenderedState:e},t}function Hd(e,t,a,l){return e.baseState=a,mc(e,qe,typeof l=="function"?l:ha)}function V0(e,t,a,l,i){if(ui(e))throw Error(c(485));if(e=t.action,e!==null){var o={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(h){o.listeners.push(h)}};C.T!==null?a(!0):o.isTransition=!1,l(o),a=t.pending,a===null?(o.next=t.pending=o,Vd(t,o)):(o.next=a.next,t.pending=a.next=o)}}function Vd(e,t){var a=t.action,l=t.payload,i=e.state;if(t.isTransition){var o=C.T,h={};C.T=h;try{var y=a(i,l),w=C.S;w!==null&&w(h,y),Gd(e,t,y)}catch(z){vc(e,t,z)}finally{C.T=o}}else try{o=a(i,l),Gd(e,t,o)}catch(z){vc(e,t,z)}}function Gd(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){Yd(e,t,l)},function(l){return vc(e,t,l)}):Yd(e,t,a)}function Yd(e,t,a){t.status="fulfilled",t.value=a,Xd(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,Vd(e,a)))}function vc(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,Xd(t),t=t.next;while(t!==l)}e.action=null}function Xd(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Qd(e,t){return t}function Zd(e,t){if(ze){var a=Xe.formState;if(a!==null){e:{var l=xe;if(ze){if($e){t:{for(var i=$e,o=ea;i.nodeType!==8;){if(!o){i=null;break t}if(i=Wt(i.nextSibling),i===null){i=null;break t}}o=i.data,i=o==="F!"||o==="F"?i:null}if(i){$e=Wt(i.nextSibling),l=i.data==="F!";break e}}cl(l)}l=!1}l&&(t=a[0])}}return a=Nt(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Qd,lastRenderedState:t},a.queue=l,a=ff.bind(null,xe,l),l.dispatch=a,l=gc(!1),o=jc.bind(null,xe,!1,l.queue),l=Nt(),i={state:t,dispatch:null,action:e,pending:null},l.queue=i,a=V0.bind(null,xe,i,o,a),i.dispatch=a,l.memoizedState=e,[t,a,!1]}function Kd(e){var t=at();return $d(t,qe,e)}function $d(e,t,a){if(t=mc(e,t,Qd)[0],e=ri(ha)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=Kn(t)}catch(h){throw h===Vn?ti:h}else l=t;t=at();var i=t.queue,o=i.dispatch;return a!==t.memoizedState&&(xe.flags|=2048,Xl(9,ci(),G0.bind(null,i,a),null)),[l,o,e]}function G0(e,t){e.action=t}function Jd(e){var t=at(),a=qe;if(a!==null)return $d(t,a,e);at(),t=t.memoizedState,a=at();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function Xl(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=xe.updateQueue,t===null&&(t=fc(),xe.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function ci(){return{destroy:void 0,resource:void 0}}function Fd(){return at().memoizedState}function oi(e,t,a,l){var i=Nt();l=l===void 0?null:l,xe.flags|=e,i.memoizedState=Xl(1|t,ci(),a,l)}function $n(e,t,a,l){var i=at();l=l===void 0?null:l;var o=i.memoizedState.inst;qe!==null&&l!==null&&rc(l,qe.memoizedState.deps)?i.memoizedState=Xl(t,o,a,l):(xe.flags|=e,i.memoizedState=Xl(1|t,o,a,l))}function Pd(e,t){oi(8390656,8,e,t)}function Wd(e,t){$n(2048,8,e,t)}function Id(e,t){return $n(4,2,e,t)}function ef(e,t){return $n(4,4,e,t)}function tf(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function af(e,t,a){a=a!=null?a.concat([e]):null,$n(4,4,tf.bind(null,t,e),a)}function xc(){}function lf(e,t){var a=at();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&rc(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function nf(e,t){var a=at();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&rc(t,l[1]))return l[0];if(l=e(),fl){wa(!0);try{e()}finally{wa(!1)}}return a.memoizedState=[l,t],l}function yc(e,t,a){return a===void 0||(Da&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=ch(),xe.lanes|=e,Ba|=e,a)}function sf(e,t,a,l){return Mt(a,t)?a:Vl.current!==null?(e=yc(e,a,l),Mt(e,t)||(ct=!0),e):(Da&42)===0?(ct=!0,e.memoizedState=a):(e=ch(),xe.lanes|=e,Ba|=e,t)}function rf(e,t,a,l,i){var o=Q.p;Q.p=o!==0&&8>o?o:8;var h=C.T,y={};C.T=y,jc(e,!1,t,a);try{var w=i(),z=C.S;if(z!==null&&z(y,w),w!==null&&typeof w=="object"&&typeof w.then=="function"){var B=B0(w,l);Jn(e,t,B,At(e))}else Jn(e,t,l,At(e))}catch(Y){Jn(e,t,{then:function(){},status:"rejected",reason:Y},At())}finally{Q.p=o,C.T=h}}function Y0(){}function bc(e,t,a,l){if(e.tag!==5)throw Error(c(476));var i=cf(e).queue;rf(e,i,t,U,a===null?Y0:function(){return of(e),a(l)})}function cf(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:U,baseState:U,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ha,lastRenderedState:U},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ha,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function of(e){var t=cf(e).next.queue;Jn(e,t,{},At())}function Sc(){return gt(hs)}function uf(){return at().memoizedState}function df(){return at().memoizedState}function X0(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=At();e=Ea(a);var l=Ca(t,e,a);l!==null&&(zt(l,t,a),Yn(l,t,a)),t={cache:Pr()},e.payload=t;return}t=t.return}}function Q0(e,t,a){var l=At();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},ui(e)?hf(t,a):(a=Vr(e,t,a,l),a!==null&&(zt(a,e,l),mf(a,t,l)))}function ff(e,t,a){var l=At();Jn(e,t,a,l)}function Jn(e,t,a,l){var i={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(ui(e))hf(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var h=t.lastRenderedState,y=o(h,a);if(i.hasEagerState=!0,i.eagerState=y,Mt(y,h))return Ks(e,t,i,0),Xe===null&&Zs(),!1}catch{}finally{}if(a=Vr(e,t,i,l),a!==null)return zt(a,e,l),mf(a,t,l),!0}return!1}function jc(e,t,a,l){if(l={lane:2,revertLane:eo(),action:l,hasEagerState:!1,eagerState:null,next:null},ui(e)){if(t)throw Error(c(479))}else t=Vr(e,a,l,2),t!==null&&zt(t,e,2)}function ui(e){var t=e.alternate;return e===xe||t!==null&&t===xe}function hf(e,t){Gl=ni=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function mf(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,Su(e,a)}}var di={readContext:gt,use:ii,useCallback:Pe,useContext:Pe,useEffect:Pe,useImperativeHandle:Pe,useLayoutEffect:Pe,useInsertionEffect:Pe,useMemo:Pe,useReducer:Pe,useRef:Pe,useState:Pe,useDebugValue:Pe,useDeferredValue:Pe,useTransition:Pe,useSyncExternalStore:Pe,useId:Pe,useHostTransitionStatus:Pe,useFormState:Pe,useActionState:Pe,useOptimistic:Pe,useMemoCache:Pe,useCacheRefresh:Pe},pf={readContext:gt,use:ii,useCallback:function(e,t){return Nt().memoizedState=[e,t===void 0?null:t],e},useContext:gt,useEffect:Pd,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,oi(4194308,4,tf.bind(null,t,e),a)},useLayoutEffect:function(e,t){return oi(4194308,4,e,t)},useInsertionEffect:function(e,t){oi(4,2,e,t)},useMemo:function(e,t){var a=Nt();t=t===void 0?null:t;var l=e();if(fl){wa(!0);try{e()}finally{wa(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=Nt();if(a!==void 0){var i=a(t);if(fl){wa(!0);try{a(t)}finally{wa(!1)}}}else i=t;return l.memoizedState=l.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},l.queue=e,e=e.dispatch=Q0.bind(null,xe,e),[l.memoizedState,e]},useRef:function(e){var t=Nt();return e={current:e},t.memoizedState=e},useState:function(e){e=gc(e);var t=e.queue,a=ff.bind(null,xe,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:xc,useDeferredValue:function(e,t){var a=Nt();return yc(a,e,t)},useTransition:function(){var e=gc(!1);return e=rf.bind(null,xe,e.queue,!0,!1),Nt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=xe,i=Nt();if(ze){if(a===void 0)throw Error(c(407));a=a()}else{if(a=t(),Xe===null)throw Error(c(349));(Te&124)!==0||kd(l,t,a)}i.memoizedState=a;var o={value:a,getSnapshot:t};return i.queue=o,Pd(Ld.bind(null,l,o,e),[e]),l.flags|=2048,Xl(9,ci(),Ud.bind(null,l,o,a,t),null),a},useId:function(){var e=Nt(),t=Xe.identifierPrefix;if(ze){var a=ua,l=oa;a=(l&~(1<<32-Rt(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=si++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=q0++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Sc,useFormState:Zd,useActionState:Zd,useOptimistic:function(e){var t=Nt();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=jc.bind(null,xe,!0,a),a.dispatch=t,[e,t]},useMemoCache:hc,useCacheRefresh:function(){return Nt().memoizedState=X0.bind(null,xe)}},gf={readContext:gt,use:ii,useCallback:lf,useContext:gt,useEffect:Wd,useImperativeHandle:af,useInsertionEffect:Id,useLayoutEffect:ef,useMemo:nf,useReducer:ri,useRef:Fd,useState:function(){return ri(ha)},useDebugValue:xc,useDeferredValue:function(e,t){var a=at();return sf(a,qe.memoizedState,e,t)},useTransition:function(){var e=ri(ha)[0],t=at().memoizedState;return[typeof e=="boolean"?e:Kn(e),t]},useSyncExternalStore:Od,useId:uf,useHostTransitionStatus:Sc,useFormState:Kd,useActionState:Kd,useOptimistic:function(e,t){var a=at();return Hd(a,qe,e,t)},useMemoCache:hc,useCacheRefresh:df},Z0={readContext:gt,use:ii,useCallback:lf,useContext:gt,useEffect:Wd,useImperativeHandle:af,useInsertionEffect:Id,useLayoutEffect:ef,useMemo:nf,useReducer:pc,useRef:Fd,useState:function(){return pc(ha)},useDebugValue:xc,useDeferredValue:function(e,t){var a=at();return qe===null?yc(a,e,t):sf(a,qe.memoizedState,e,t)},useTransition:function(){var e=pc(ha)[0],t=at().memoizedState;return[typeof e=="boolean"?e:Kn(e),t]},useSyncExternalStore:Od,useId:uf,useHostTransitionStatus:Sc,useFormState:Jd,useActionState:Jd,useOptimistic:function(e,t){var a=at();return qe!==null?Hd(a,qe,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:hc,useCacheRefresh:df},Ql=null,Fn=0;function fi(e){var t=Fn;return Fn+=1,Ql===null&&(Ql=[]),Rd(Ql,e,t)}function Pn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function hi(e,t){throw t.$$typeof===b?Error(c(525)):(e=Object.prototype.toString.call(t),Error(c(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function vf(e){var t=e._init;return t(e._payload)}function xf(e){function t(E,M){if(e){var D=E.deletions;D===null?(E.deletions=[M],E.flags|=16):D.push(M)}}function a(E,M){if(!e)return null;for(;M!==null;)t(E,M),M=M.sibling;return null}function l(E){for(var M=new Map;E!==null;)E.key!==null?M.set(E.key,E):M.set(E.index,E),E=E.sibling;return M}function i(E,M){return E=ca(E,M),E.index=0,E.sibling=null,E}function o(E,M,D){return E.index=D,e?(D=E.alternate,D!==null?(D=D.index,D<M?(E.flags|=67108866,M):D):(E.flags|=67108866,M)):(E.flags|=1048576,M)}function h(E){return e&&E.alternate===null&&(E.flags|=67108866),E}function y(E,M,D,H){return M===null||M.tag!==6?(M=Yr(D,E.mode,H),M.return=E,M):(M=i(M,D),M.return=E,M)}function w(E,M,D,H){var ae=D.type;return ae===R?B(E,M,D.props.children,H,D.key):M!==null&&(M.elementType===ae||typeof ae=="object"&&ae!==null&&ae.$$typeof===V&&vf(ae)===M.type)?(M=i(M,D.props),Pn(M,D),M.return=E,M):(M=Js(D.type,D.key,D.props,null,E.mode,H),Pn(M,D),M.return=E,M)}function z(E,M,D,H){return M===null||M.tag!==4||M.stateNode.containerInfo!==D.containerInfo||M.stateNode.implementation!==D.implementation?(M=Xr(D,E.mode,H),M.return=E,M):(M=i(M,D.children||[]),M.return=E,M)}function B(E,M,D,H,ae){return M===null||M.tag!==7?(M=nl(D,E.mode,H,ae),M.return=E,M):(M=i(M,D),M.return=E,M)}function Y(E,M,D){if(typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint")return M=Yr(""+M,E.mode,D),M.return=E,M;if(typeof M=="object"&&M!==null){switch(M.$$typeof){case j:return D=Js(M.type,M.key,M.props,null,E.mode,D),Pn(D,M),D.return=E,D;case T:return M=Xr(M,E.mode,D),M.return=E,M;case V:var H=M._init;return M=H(M._payload),Y(E,M,D)}if(ge(M)||W(M))return M=nl(M,E.mode,D,null),M.return=E,M;if(typeof M.then=="function")return Y(E,fi(M),D);if(M.$$typeof===q)return Y(E,Is(E,M),D);hi(E,M)}return null}function O(E,M,D,H){var ae=M!==null?M.key:null;if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return ae!==null?null:y(E,M,""+D,H);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case j:return D.key===ae?w(E,M,D,H):null;case T:return D.key===ae?z(E,M,D,H):null;case V:return ae=D._init,D=ae(D._payload),O(E,M,D,H)}if(ge(D)||W(D))return ae!==null?null:B(E,M,D,H,null);if(typeof D.then=="function")return O(E,M,fi(D),H);if(D.$$typeof===q)return O(E,M,Is(E,D),H);hi(E,D)}return null}function k(E,M,D,H,ae){if(typeof H=="string"&&H!==""||typeof H=="number"||typeof H=="bigint")return E=E.get(D)||null,y(M,E,""+H,ae);if(typeof H=="object"&&H!==null){switch(H.$$typeof){case j:return E=E.get(H.key===null?D:H.key)||null,w(M,E,H,ae);case T:return E=E.get(H.key===null?D:H.key)||null,z(M,E,H,ae);case V:var Se=H._init;return H=Se(H._payload),k(E,M,D,H,ae)}if(ge(H)||W(H))return E=E.get(D)||null,B(M,E,H,ae,null);if(typeof H.then=="function")return k(E,M,D,fi(H),ae);if(H.$$typeof===q)return k(E,M,D,Is(M,H),ae);hi(M,H)}return null}function he(E,M,D,H){for(var ae=null,Se=null,ie=M,fe=M=0,ut=null;ie!==null&&fe<D.length;fe++){ie.index>fe?(ut=ie,ie=null):ut=ie.sibling;var De=O(E,ie,D[fe],H);if(De===null){ie===null&&(ie=ut);break}e&&ie&&De.alternate===null&&t(E,ie),M=o(De,M,fe),Se===null?ae=De:Se.sibling=De,Se=De,ie=ut}if(fe===D.length)return a(E,ie),ze&&il(E,fe),ae;if(ie===null){for(;fe<D.length;fe++)ie=Y(E,D[fe],H),ie!==null&&(M=o(ie,M,fe),Se===null?ae=ie:Se.sibling=ie,Se=ie);return ze&&il(E,fe),ae}for(ie=l(ie);fe<D.length;fe++)ut=k(ie,E,fe,D[fe],H),ut!==null&&(e&&ut.alternate!==null&&ie.delete(ut.key===null?fe:ut.key),M=o(ut,M,fe),Se===null?ae=ut:Se.sibling=ut,Se=ut);return e&&ie.forEach(function(Ka){return t(E,Ka)}),ze&&il(E,fe),ae}function de(E,M,D,H){if(D==null)throw Error(c(151));for(var ae=null,Se=null,ie=M,fe=M=0,ut=null,De=D.next();ie!==null&&!De.done;fe++,De=D.next()){ie.index>fe?(ut=ie,ie=null):ut=ie.sibling;var Ka=O(E,ie,De.value,H);if(Ka===null){ie===null&&(ie=ut);break}e&&ie&&Ka.alternate===null&&t(E,ie),M=o(Ka,M,fe),Se===null?ae=Ka:Se.sibling=Ka,Se=Ka,ie=ut}if(De.done)return a(E,ie),ze&&il(E,fe),ae;if(ie===null){for(;!De.done;fe++,De=D.next())De=Y(E,De.value,H),De!==null&&(M=o(De,M,fe),Se===null?ae=De:Se.sibling=De,Se=De);return ze&&il(E,fe),ae}for(ie=l(ie);!De.done;fe++,De=D.next())De=k(ie,E,fe,De.value,H),De!==null&&(e&&De.alternate!==null&&ie.delete(De.key===null?fe:De.key),M=o(De,M,fe),Se===null?ae=De:Se.sibling=De,Se=De);return e&&ie.forEach(function(Kg){return t(E,Kg)}),ze&&il(E,fe),ae}function Ve(E,M,D,H){if(typeof D=="object"&&D!==null&&D.type===R&&D.key===null&&(D=D.props.children),typeof D=="object"&&D!==null){switch(D.$$typeof){case j:e:{for(var ae=D.key;M!==null;){if(M.key===ae){if(ae=D.type,ae===R){if(M.tag===7){a(E,M.sibling),H=i(M,D.props.children),H.return=E,E=H;break e}}else if(M.elementType===ae||typeof ae=="object"&&ae!==null&&ae.$$typeof===V&&vf(ae)===M.type){a(E,M.sibling),H=i(M,D.props),Pn(H,D),H.return=E,E=H;break e}a(E,M);break}else t(E,M);M=M.sibling}D.type===R?(H=nl(D.props.children,E.mode,H,D.key),H.return=E,E=H):(H=Js(D.type,D.key,D.props,null,E.mode,H),Pn(H,D),H.return=E,E=H)}return h(E);case T:e:{for(ae=D.key;M!==null;){if(M.key===ae)if(M.tag===4&&M.stateNode.containerInfo===D.containerInfo&&M.stateNode.implementation===D.implementation){a(E,M.sibling),H=i(M,D.children||[]),H.return=E,E=H;break e}else{a(E,M);break}else t(E,M);M=M.sibling}H=Xr(D,E.mode,H),H.return=E,E=H}return h(E);case V:return ae=D._init,D=ae(D._payload),Ve(E,M,D,H)}if(ge(D))return he(E,M,D,H);if(W(D)){if(ae=W(D),typeof ae!="function")throw Error(c(150));return D=ae.call(D),de(E,M,D,H)}if(typeof D.then=="function")return Ve(E,M,fi(D),H);if(D.$$typeof===q)return Ve(E,M,Is(E,D),H);hi(E,D)}return typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint"?(D=""+D,M!==null&&M.tag===6?(a(E,M.sibling),H=i(M,D),H.return=E,E=H):(a(E,M),H=Yr(D,E.mode,H),H.return=E,E=H),h(E)):a(E,M)}return function(E,M,D,H){try{Fn=0;var ae=Ve(E,M,D,H);return Ql=null,ae}catch(ie){if(ie===Vn||ie===ti)throw ie;var Se=Tt(29,ie,null,E.mode);return Se.lanes=H,Se.return=E,Se}finally{}}}var Zl=xf(!0),yf=xf(!1),Vt=L(null),ta=null;function Aa(e){var t=e.alternate;Z(nt,nt.current&1),Z(Vt,e),ta===null&&(t===null||Vl.current!==null||t.memoizedState!==null)&&(ta=e)}function bf(e){if(e.tag===22){if(Z(nt,nt.current),Z(Vt,e),ta===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(ta=e)}}else za()}function za(){Z(nt,nt.current),Z(Vt,Vt.current)}function ma(e){J(Vt),ta===e&&(ta=null),J(nt)}var nt=L(0);function mi(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||ho(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Nc(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:x({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var wc={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=At(),i=Ea(l);i.payload=t,a!=null&&(i.callback=a),t=Ca(e,i,l),t!==null&&(zt(t,e,l),Yn(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=At(),i=Ea(l);i.tag=1,i.payload=t,a!=null&&(i.callback=a),t=Ca(e,i,l),t!==null&&(zt(t,e,l),Yn(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=At(),l=Ea(a);l.tag=2,t!=null&&(l.callback=t),t=Ca(e,l,a),t!==null&&(zt(t,e,a),Yn(t,e,a))}};function Sf(e,t,a,l,i,o,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,o,h):t.prototype&&t.prototype.isPureReactComponent?!zn(a,l)||!zn(i,o):!0}function jf(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&wc.enqueueReplaceState(t,t.state,null)}function hl(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=x({},a));for(var i in e)a[i]===void 0&&(a[i]=e[i])}return a}var pi=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Nf(e){pi(e)}function wf(e){console.error(e)}function _f(e){pi(e)}function gi(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Rf(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function _c(e,t,a){return a=Ea(a),a.tag=3,a.payload={element:null},a.callback=function(){gi(e,t)},a}function Mf(e){return e=Ea(e),e.tag=3,e}function Tf(e,t,a,l){var i=a.type.getDerivedStateFromError;if(typeof i=="function"){var o=l.value;e.payload=function(){return i(o)},e.callback=function(){Rf(t,a,l)}}var h=a.stateNode;h!==null&&typeof h.componentDidCatch=="function"&&(e.callback=function(){Rf(t,a,l),typeof i!="function"&&(qa===null?qa=new Set([this]):qa.add(this));var y=l.stack;this.componentDidCatch(l.value,{componentStack:y!==null?y:""})})}function K0(e,t,a,l,i){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&Bn(t,a,i,!0),a=Vt.current,a!==null){switch(a.tag){case 13:return ta===null?Jc():a.alternate===null&&Je===0&&(Je=3),a.flags&=-257,a.flags|=65536,a.lanes=i,l===ec?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),Pc(e,l,i)),!1;case 22:return a.flags|=65536,l===ec?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),Pc(e,l,i)),!1}throw Error(c(435,a.tag))}return Pc(e,l,i),Jc(),!1}if(ze)return t=Vt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=i,l!==Kr&&(e=Error(c(422),{cause:l}),Ln(Lt(e,a)))):(l!==Kr&&(t=Error(c(423),{cause:l}),Ln(Lt(t,a))),e=e.current.alternate,e.flags|=65536,i&=-i,e.lanes|=i,l=Lt(l,a),i=_c(e.stateNode,l,i),lc(e,i),Je!==4&&(Je=2)),!1;var o=Error(c(520),{cause:l});if(o=Lt(o,a),ns===null?ns=[o]:ns.push(o),Je!==4&&(Je=2),t===null)return!0;l=Lt(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=i&-i,a.lanes|=e,e=_c(a.stateNode,l,e),lc(a,e),!1;case 1:if(t=a.type,o=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||o!==null&&typeof o.componentDidCatch=="function"&&(qa===null||!qa.has(o))))return a.flags|=65536,i&=-i,a.lanes|=i,i=Mf(i),Tf(i,e,a,l),lc(a,i),!1}a=a.return}while(a!==null);return!1}var Ef=Error(c(461)),ct=!1;function dt(e,t,a,l){t.child=e===null?yf(t,null,a,l):Zl(t,e.child,a,l)}function Cf(e,t,a,l,i){a=a.render;var o=t.ref;if("ref"in l){var h={};for(var y in l)y!=="ref"&&(h[y]=l[y])}else h=l;return ul(t),l=cc(e,t,a,h,o,i),y=oc(),e!==null&&!ct?(uc(e,t,i),pa(e,t,i)):(ze&&y&&Qr(t),t.flags|=1,dt(e,t,l,i),t.child)}function Df(e,t,a,l,i){if(e===null){var o=a.type;return typeof o=="function"&&!Gr(o)&&o.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=o,Af(e,t,o,l,i)):(e=Js(a.type,null,l,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!zc(e,i)){var h=o.memoizedProps;if(a=a.compare,a=a!==null?a:zn,a(h,l)&&e.ref===t.ref)return pa(e,t,i)}return t.flags|=1,e=ca(o,l),e.ref=t.ref,e.return=t,t.child=e}function Af(e,t,a,l,i){if(e!==null){var o=e.memoizedProps;if(zn(o,l)&&e.ref===t.ref)if(ct=!1,t.pendingProps=l=o,zc(e,i))(e.flags&131072)!==0&&(ct=!0);else return t.lanes=e.lanes,pa(e,t,i)}return Rc(e,t,a,l,i)}function zf(e,t,a){var l=t.pendingProps,i=l.children,o=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=o!==null?o.baseLanes|a:a,e!==null){for(i=t.child=e.child,o=0;i!==null;)o=o|i.lanes|i.childLanes,i=i.sibling;t.childLanes=o&~l}else t.childLanes=0,t.child=null;return Of(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&ei(t,o!==null?o.cachePool:null),o!==null?Dd(t,o):sc(),bf(t);else return t.lanes=t.childLanes=536870912,Of(e,t,o!==null?o.baseLanes|a:a,a)}else o!==null?(ei(t,o.cachePool),Dd(t,o),za(),t.memoizedState=null):(e!==null&&ei(t,null),sc(),za());return dt(e,t,i,a),t.child}function Of(e,t,a,l){var i=Ir();return i=i===null?null:{parent:lt._currentValue,pool:i},t.memoizedState={baseLanes:a,cachePool:i},e!==null&&ei(t,null),sc(),bf(t),e!==null&&Bn(e,t,l,!0),null}function vi(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(c(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function Rc(e,t,a,l,i){return ul(t),a=cc(e,t,a,l,void 0,i),l=oc(),e!==null&&!ct?(uc(e,t,i),pa(e,t,i)):(ze&&l&&Qr(t),t.flags|=1,dt(e,t,a,i),t.child)}function kf(e,t,a,l,i,o){return ul(t),t.updateQueue=null,a=zd(t,l,a,i),Ad(e),l=oc(),e!==null&&!ct?(uc(e,t,o),pa(e,t,o)):(ze&&l&&Qr(t),t.flags|=1,dt(e,t,a,o),t.child)}function Uf(e,t,a,l,i){if(ul(t),t.stateNode===null){var o=Ul,h=a.contextType;typeof h=="object"&&h!==null&&(o=gt(h)),o=new a(l,o),t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,o.updater=wc,t.stateNode=o,o._reactInternals=t,o=t.stateNode,o.props=l,o.state=t.memoizedState,o.refs={},tc(t),h=a.contextType,o.context=typeof h=="object"&&h!==null?gt(h):Ul,o.state=t.memoizedState,h=a.getDerivedStateFromProps,typeof h=="function"&&(Nc(t,a,h,l),o.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(h=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),h!==o.state&&wc.enqueueReplaceState(o,o.state,null),Qn(t,l,o,i),Xn(),o.state=t.memoizedState),typeof o.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){o=t.stateNode;var y=t.memoizedProps,w=hl(a,y);o.props=w;var z=o.context,B=a.contextType;h=Ul,typeof B=="object"&&B!==null&&(h=gt(B));var Y=a.getDerivedStateFromProps;B=typeof Y=="function"||typeof o.getSnapshotBeforeUpdate=="function",y=t.pendingProps!==y,B||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(y||z!==h)&&jf(t,o,l,h),Ta=!1;var O=t.memoizedState;o.state=O,Qn(t,l,o,i),Xn(),z=t.memoizedState,y||O!==z||Ta?(typeof Y=="function"&&(Nc(t,a,Y,l),z=t.memoizedState),(w=Ta||Sf(t,a,w,l,O,z,h))?(B||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=z),o.props=l,o.state=z,o.context=h,l=w):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{o=t.stateNode,ac(e,t),h=t.memoizedProps,B=hl(a,h),o.props=B,Y=t.pendingProps,O=o.context,z=a.contextType,w=Ul,typeof z=="object"&&z!==null&&(w=gt(z)),y=a.getDerivedStateFromProps,(z=typeof y=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(h!==Y||O!==w)&&jf(t,o,l,w),Ta=!1,O=t.memoizedState,o.state=O,Qn(t,l,o,i),Xn();var k=t.memoizedState;h!==Y||O!==k||Ta||e!==null&&e.dependencies!==null&&Ws(e.dependencies)?(typeof y=="function"&&(Nc(t,a,y,l),k=t.memoizedState),(B=Ta||Sf(t,a,B,l,O,k,w)||e!==null&&e.dependencies!==null&&Ws(e.dependencies))?(z||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(l,k,w),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(l,k,w)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||h===e.memoizedProps&&O===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&O===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=k),o.props=l,o.state=k,o.context=w,l=B):(typeof o.componentDidUpdate!="function"||h===e.memoizedProps&&O===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||h===e.memoizedProps&&O===e.memoizedState||(t.flags|=1024),l=!1)}return o=l,vi(e,t),l=(t.flags&128)!==0,o||l?(o=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:o.render(),t.flags|=1,e!==null&&l?(t.child=Zl(t,e.child,null,i),t.child=Zl(t,null,a,i)):dt(e,t,a,i),t.memoizedState=o.state,e=t.child):e=pa(e,t,i),e}function Lf(e,t,a,l){return Un(),t.flags|=256,dt(e,t,a,l),t.child}var Mc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Tc(e){return{baseLanes:e,cachePool:Nd()}}function Ec(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=Gt),e}function Bf(e,t,a){var l=t.pendingProps,i=!1,o=(t.flags&128)!==0,h;if((h=o)||(h=e!==null&&e.memoizedState===null?!1:(nt.current&2)!==0),h&&(i=!0,t.flags&=-129),h=(t.flags&32)!==0,t.flags&=-33,e===null){if(ze){if(i?Aa(t):za(),ze){var y=$e,w;if(w=y){e:{for(w=y,y=ea;w.nodeType!==8;){if(!y){y=null;break e}if(w=Wt(w.nextSibling),w===null){y=null;break e}}y=w}y!==null?(t.memoizedState={dehydrated:y,treeContext:sl!==null?{id:oa,overflow:ua}:null,retryLane:536870912,hydrationErrors:null},w=Tt(18,null,null,0),w.stateNode=y,w.return=t,t.child=w,bt=t,$e=null,w=!0):w=!1}w||cl(t)}if(y=t.memoizedState,y!==null&&(y=y.dehydrated,y!==null))return ho(y)?t.lanes=32:t.lanes=536870912,null;ma(t)}return y=l.children,l=l.fallback,i?(za(),i=t.mode,y=xi({mode:"hidden",children:y},i),l=nl(l,i,a,null),y.return=t,l.return=t,y.sibling=l,t.child=y,i=t.child,i.memoizedState=Tc(a),i.childLanes=Ec(e,h,a),t.memoizedState=Mc,l):(Aa(t),Cc(t,y))}if(w=e.memoizedState,w!==null&&(y=w.dehydrated,y!==null)){if(o)t.flags&256?(Aa(t),t.flags&=-257,t=Dc(e,t,a)):t.memoizedState!==null?(za(),t.child=e.child,t.flags|=128,t=null):(za(),i=l.fallback,y=t.mode,l=xi({mode:"visible",children:l.children},y),i=nl(i,y,a,null),i.flags|=2,l.return=t,i.return=t,l.sibling=i,t.child=l,Zl(t,e.child,null,a),l=t.child,l.memoizedState=Tc(a),l.childLanes=Ec(e,h,a),t.memoizedState=Mc,t=i);else if(Aa(t),ho(y)){if(h=y.nextSibling&&y.nextSibling.dataset,h)var z=h.dgst;h=z,l=Error(c(419)),l.stack="",l.digest=h,Ln({value:l,source:null,stack:null}),t=Dc(e,t,a)}else if(ct||Bn(e,t,a,!1),h=(a&e.childLanes)!==0,ct||h){if(h=Xe,h!==null&&(l=a&-a,l=(l&42)!==0?1:hr(l),l=(l&(h.suspendedLanes|a))!==0?0:l,l!==0&&l!==w.retryLane))throw w.retryLane=l,kl(e,l),zt(h,e,l),Ef;y.data==="$?"||Jc(),t=Dc(e,t,a)}else y.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=w.treeContext,$e=Wt(y.nextSibling),bt=t,ze=!0,rl=null,ea=!1,e!==null&&(qt[Ht++]=oa,qt[Ht++]=ua,qt[Ht++]=sl,oa=e.id,ua=e.overflow,sl=t),t=Cc(t,l.children),t.flags|=4096);return t}return i?(za(),i=l.fallback,y=t.mode,w=e.child,z=w.sibling,l=ca(w,{mode:"hidden",children:l.children}),l.subtreeFlags=w.subtreeFlags&65011712,z!==null?i=ca(z,i):(i=nl(i,y,a,null),i.flags|=2),i.return=t,l.return=t,l.sibling=i,t.child=l,l=i,i=t.child,y=e.child.memoizedState,y===null?y=Tc(a):(w=y.cachePool,w!==null?(z=lt._currentValue,w=w.parent!==z?{parent:z,pool:z}:w):w=Nd(),y={baseLanes:y.baseLanes|a,cachePool:w}),i.memoizedState=y,i.childLanes=Ec(e,h,a),t.memoizedState=Mc,l):(Aa(t),a=e.child,e=a.sibling,a=ca(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(h=t.deletions,h===null?(t.deletions=[e],t.flags|=16):h.push(e)),t.child=a,t.memoizedState=null,a)}function Cc(e,t){return t=xi({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function xi(e,t){return e=Tt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Dc(e,t,a){return Zl(t,e.child,null,a),e=Cc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function qf(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Jr(e.return,t,a)}function Ac(e,t,a,l,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=l,o.tail=a,o.tailMode=i)}function Hf(e,t,a){var l=t.pendingProps,i=l.revealOrder,o=l.tail;if(dt(e,t,l.children,a),l=nt.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&qf(e,a,t);else if(e.tag===19)qf(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(Z(nt,l),i){case"forwards":for(a=t.child,i=null;a!==null;)e=a.alternate,e!==null&&mi(e)===null&&(i=a),a=a.sibling;a=i,a===null?(i=t.child,t.child=null):(i=a.sibling,a.sibling=null),Ac(t,!1,i,a,o);break;case"backwards":for(a=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&mi(e)===null){t.child=i;break}e=i.sibling,i.sibling=a,a=i,i=e}Ac(t,!0,a,null,o);break;case"together":Ac(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function pa(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),Ba|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(Bn(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(c(153));if(t.child!==null){for(e=t.child,a=ca(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=ca(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function zc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Ws(e)))}function $0(e,t,a){switch(t.tag){case 3:pe(t,t.stateNode.containerInfo),Ma(t,lt,e.memoizedState.cache),Un();break;case 27:case 5:Ce(t);break;case 4:pe(t,t.stateNode.containerInfo);break;case 10:Ma(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(Aa(t),t.flags|=128,null):(a&t.child.childLanes)!==0?Bf(e,t,a):(Aa(t),e=pa(e,t,a),e!==null?e.sibling:null);Aa(t);break;case 19:var i=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(Bn(e,t,a,!1),l=(a&t.childLanes)!==0),i){if(l)return Hf(e,t,a);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),Z(nt,nt.current),l)break;return null;case 22:case 23:return t.lanes=0,zf(e,t,a);case 24:Ma(t,lt,e.memoizedState.cache)}return pa(e,t,a)}function Vf(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)ct=!0;else{if(!zc(e,a)&&(t.flags&128)===0)return ct=!1,$0(e,t,a);ct=(e.flags&131072)!==0}else ct=!1,ze&&(t.flags&1048576)!==0&&gd(t,Ps,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,i=l._init;if(l=i(l._payload),t.type=l,typeof l=="function")Gr(l)?(e=hl(l,e),t.tag=1,t=Uf(null,t,l,e,a)):(t.tag=0,t=Rc(null,t,l,e,a));else{if(l!=null){if(i=l.$$typeof,i===P){t.tag=11,t=Cf(null,t,l,e,a);break e}else if(i===K){t.tag=14,t=Df(null,t,l,e,a);break e}}throw t=be(l)||l,Error(c(306,t,""))}}return t;case 0:return Rc(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,i=hl(l,t.pendingProps),Uf(e,t,l,i,a);case 3:e:{if(pe(t,t.stateNode.containerInfo),e===null)throw Error(c(387));l=t.pendingProps;var o=t.memoizedState;i=o.element,ac(e,t),Qn(t,l,null,a);var h=t.memoizedState;if(l=h.cache,Ma(t,lt,l),l!==o.cache&&Fr(t,[lt],a,!0),Xn(),l=h.element,o.isDehydrated)if(o={element:l,isDehydrated:!1,cache:h.cache},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){t=Lf(e,t,l,a);break e}else if(l!==i){i=Lt(Error(c(424)),t),Ln(i),t=Lf(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for($e=Wt(e.firstChild),bt=t,ze=!0,rl=null,ea=!0,a=yf(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(Un(),l===i){t=pa(e,t,a);break e}dt(e,t,l,a)}t=t.child}return t;case 26:return vi(e,t),e===null?(a=Qh(t.type,null,t.pendingProps,null))?t.memoizedState=a:ze||(a=t.type,e=t.pendingProps,l=Ai(I.current).createElement(a),l[pt]=t,l[St]=e,ht(l,a,e),rt(l),t.stateNode=l):t.memoizedState=Qh(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Ce(t),e===null&&ze&&(l=t.stateNode=Gh(t.type,t.pendingProps,I.current),bt=t,ea=!0,i=$e,Ga(t.type)?(mo=i,$e=Wt(l.firstChild)):$e=i),dt(e,t,t.pendingProps.children,a),vi(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&ze&&((i=l=$e)&&(l=jg(l,t.type,t.pendingProps,ea),l!==null?(t.stateNode=l,bt=t,$e=Wt(l.firstChild),ea=!1,i=!0):i=!1),i||cl(t)),Ce(t),i=t.type,o=t.pendingProps,h=e!==null?e.memoizedProps:null,l=o.children,oo(i,o)?l=null:h!==null&&oo(i,h)&&(t.flags|=32),t.memoizedState!==null&&(i=cc(e,t,H0,null,null,a),hs._currentValue=i),vi(e,t),dt(e,t,l,a),t.child;case 6:return e===null&&ze&&((e=a=$e)&&(a=Ng(a,t.pendingProps,ea),a!==null?(t.stateNode=a,bt=t,$e=null,e=!0):e=!1),e||cl(t)),null;case 13:return Bf(e,t,a);case 4:return pe(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=Zl(t,null,l,a):dt(e,t,l,a),t.child;case 11:return Cf(e,t,t.type,t.pendingProps,a);case 7:return dt(e,t,t.pendingProps,a),t.child;case 8:return dt(e,t,t.pendingProps.children,a),t.child;case 12:return dt(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,Ma(t,t.type,l.value),dt(e,t,l.children,a),t.child;case 9:return i=t.type._context,l=t.pendingProps.children,ul(t),i=gt(i),l=l(i),t.flags|=1,dt(e,t,l,a),t.child;case 14:return Df(e,t,t.type,t.pendingProps,a);case 15:return Af(e,t,t.type,t.pendingProps,a);case 19:return Hf(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=xi(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=ca(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return zf(e,t,a);case 24:return ul(t),l=gt(lt),e===null?(i=Ir(),i===null&&(i=Xe,o=Pr(),i.pooledCache=o,o.refCount++,o!==null&&(i.pooledCacheLanes|=a),i=o),t.memoizedState={parent:l,cache:i},tc(t),Ma(t,lt,i)):((e.lanes&a)!==0&&(ac(e,t),Qn(t,null,null,a),Xn()),i=e.memoizedState,o=t.memoizedState,i.parent!==l?(i={parent:l,cache:l},t.memoizedState=i,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=i),Ma(t,lt,l)):(l=o.cache,Ma(t,lt,l),l!==i.cache&&Fr(t,[lt],a,!0))),dt(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(c(156,t.tag))}function ga(e){e.flags|=4}function Gf(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Fh(t)){if(t=Vt.current,t!==null&&((Te&4194048)===Te?ta!==null:(Te&62914560)!==Te&&(Te&536870912)===0||t!==ta))throw Gn=ec,wd;e.flags|=8192}}function yi(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?yu():536870912,e.lanes|=t,Fl|=t)}function Wn(e,t){if(!ze)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Ze(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var i=e.child;i!==null;)a|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)a|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function J0(e,t,a){var l=t.pendingProps;switch(Zr(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ze(t),null;case 1:return Ze(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),fa(lt),_e(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(kn(t)?ga(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,yd())),Ze(t),null;case 26:return a=t.memoizedState,e===null?(ga(t),a!==null?(Ze(t),Gf(t,a)):(Ze(t),t.flags&=-16777217)):a?a!==e.memoizedState?(ga(t),Ze(t),Gf(t,a)):(Ze(t),t.flags&=-16777217):(e.memoizedProps!==l&&ga(t),Ze(t),t.flags&=-16777217),null;case 27:et(t),a=I.current;var i=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&ga(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return Ze(t),null}e=F.current,kn(t)?vd(t):(e=Gh(i,l,a),t.stateNode=e,ga(t))}return Ze(t),null;case 5:if(et(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&ga(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return Ze(t),null}if(e=F.current,kn(t))vd(t);else{switch(i=Ai(I.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?i.createElement(a,{is:l.is}):i.createElement(a)}}e[pt]=t,e[St]=l;e:for(i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;i.sibling===null;){if(i.return===null||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(ht(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&ga(t)}}return Ze(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&ga(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(c(166));if(e=I.current,kn(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,i=bt,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}e[pt]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||kh(e.nodeValue,a)),e||cl(t)}else e=Ai(e).createTextNode(l),e[pt]=t,t.stateNode=e}return Ze(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(i=kn(t),l!==null&&l.dehydrated!==null){if(e===null){if(!i)throw Error(c(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(c(317));i[pt]=t}else Un(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ze(t),i=!1}else i=yd(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return t.flags&256?(ma(t),t):(ma(t),null)}if(ma(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var o=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(o=l.memoizedState.cachePool.pool),o!==i&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),yi(t,t.updateQueue),Ze(t),null;case 4:return _e(),e===null&&no(t.stateNode.containerInfo),Ze(t),null;case 10:return fa(t.type),Ze(t),null;case 19:if(J(nt),i=t.memoizedState,i===null)return Ze(t),null;if(l=(t.flags&128)!==0,o=i.rendering,o===null)if(l)Wn(i,!1);else{if(Je!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(o=mi(e),o!==null){for(t.flags|=128,Wn(i,!1),e=o.updateQueue,t.updateQueue=e,yi(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)pd(a,e),a=a.sibling;return Z(nt,nt.current&1|2),t.child}e=e.sibling}i.tail!==null&&mt()>ji&&(t.flags|=128,l=!0,Wn(i,!1),t.lanes=4194304)}else{if(!l)if(e=mi(o),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,yi(t,e),Wn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!ze)return Ze(t),null}else 2*mt()-i.renderingStartTime>ji&&a!==536870912&&(t.flags|=128,l=!0,Wn(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(e=i.last,e!==null?e.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=mt(),t.sibling=null,e=nt.current,Z(nt,l?e&1|2:e&1),t):(Ze(t),null);case 22:case 23:return ma(t),ic(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(Ze(t),t.subtreeFlags&6&&(t.flags|=8192)):Ze(t),a=t.updateQueue,a!==null&&yi(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&J(dl),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),fa(lt),Ze(t),null;case 25:return null;case 30:return null}throw Error(c(156,t.tag))}function F0(e,t){switch(Zr(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return fa(lt),_e(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return et(t),null;case 13:if(ma(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(c(340));Un()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return J(nt),null;case 4:return _e(),null;case 10:return fa(t.type),null;case 22:case 23:return ma(t),ic(),e!==null&&J(dl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return fa(lt),null;case 25:return null;default:return null}}function Yf(e,t){switch(Zr(t),t.tag){case 3:fa(lt),_e();break;case 26:case 27:case 5:et(t);break;case 4:_e();break;case 13:ma(t);break;case 19:J(nt);break;case 10:fa(t.type);break;case 22:case 23:ma(t),ic(),e!==null&&J(dl);break;case 24:fa(lt)}}function In(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var i=l.next;a=i;do{if((a.tag&e)===e){l=void 0;var o=a.create,h=a.inst;l=o(),h.destroy=l}a=a.next}while(a!==i)}}catch(y){Ye(t,t.return,y)}}function Oa(e,t,a){try{var l=t.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var o=i.next;l=o;do{if((l.tag&e)===e){var h=l.inst,y=h.destroy;if(y!==void 0){h.destroy=void 0,i=t;var w=a,z=y;try{z()}catch(B){Ye(i,w,B)}}}l=l.next}while(l!==o)}}catch(B){Ye(t,t.return,B)}}function Xf(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{Cd(t,a)}catch(l){Ye(e,e.return,l)}}}function Qf(e,t,a){a.props=hl(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){Ye(e,t,l)}}function es(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(i){Ye(e,t,i)}}function aa(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(i){Ye(e,t,i)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(i){Ye(e,t,i)}else a.current=null}function Zf(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(i){Ye(e,e.return,i)}}function Oc(e,t,a){try{var l=e.stateNode;vg(l,e.type,a,t),l[St]=t}catch(i){Ye(e,e.return,i)}}function Kf(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Ga(e.type)||e.tag===4}function kc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Kf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Ga(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Uc(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=Di));else if(l!==4&&(l===27&&Ga(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(Uc(e,t,a),e=e.sibling;e!==null;)Uc(e,t,a),e=e.sibling}function bi(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&Ga(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(bi(e,t,a),e=e.sibling;e!==null;)bi(e,t,a),e=e.sibling}function $f(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);ht(t,l,a),t[pt]=e,t[St]=a}catch(o){Ye(e,e.return,o)}}var va=!1,We=!1,Lc=!1,Jf=typeof WeakSet=="function"?WeakSet:Set,ot=null;function P0(e,t){if(e=e.containerInfo,ro=Bi,e=sd(e),kr(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var i=l.anchorOffset,o=l.focusNode;l=l.focusOffset;try{a.nodeType,o.nodeType}catch{a=null;break e}var h=0,y=-1,w=-1,z=0,B=0,Y=e,O=null;t:for(;;){for(var k;Y!==a||i!==0&&Y.nodeType!==3||(y=h+i),Y!==o||l!==0&&Y.nodeType!==3||(w=h+l),Y.nodeType===3&&(h+=Y.nodeValue.length),(k=Y.firstChild)!==null;)O=Y,Y=k;for(;;){if(Y===e)break t;if(O===a&&++z===i&&(y=h),O===o&&++B===l&&(w=h),(k=Y.nextSibling)!==null)break;Y=O,O=Y.parentNode}Y=k}a=y===-1||w===-1?null:{start:y,end:w}}else a=null}a=a||{start:0,end:0}}else a=null;for(co={focusedElem:e,selectionRange:a},Bi=!1,ot=t;ot!==null;)if(t=ot,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,ot=e;else for(;ot!==null;){switch(t=ot,o=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&o!==null){e=void 0,a=t,i=o.memoizedProps,o=o.memoizedState,l=a.stateNode;try{var he=hl(a.type,i,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(he,o),l.__reactInternalSnapshotBeforeUpdate=e}catch(de){Ye(a,a.return,de)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)fo(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":fo(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(c(163))}if(e=t.sibling,e!==null){e.return=t.return,ot=e;break}ot=t.return}}function Ff(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:ka(e,a),l&4&&In(5,a);break;case 1:if(ka(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(h){Ye(a,a.return,h)}else{var i=hl(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(h){Ye(a,a.return,h)}}l&64&&Xf(a),l&512&&es(a,a.return);break;case 3:if(ka(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{Cd(e,t)}catch(h){Ye(a,a.return,h)}}break;case 27:t===null&&l&4&&$f(a);case 26:case 5:ka(e,a),t===null&&l&4&&Zf(a),l&512&&es(a,a.return);break;case 12:ka(e,a);break;case 13:ka(e,a),l&4&&If(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=ig.bind(null,a),wg(e,a))));break;case 22:if(l=a.memoizedState!==null||va,!l){t=t!==null&&t.memoizedState!==null||We,i=va;var o=We;va=l,(We=t)&&!o?Ua(e,a,(a.subtreeFlags&8772)!==0):ka(e,a),va=i,We=o}break;case 30:break;default:ka(e,a)}}function Pf(e){var t=e.alternate;t!==null&&(e.alternate=null,Pf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&gr(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Qe=null,wt=!1;function xa(e,t,a){for(a=a.child;a!==null;)Wf(e,t,a),a=a.sibling}function Wf(e,t,a){if(yt&&typeof yt.onCommitFiberUnmount=="function")try{yt.onCommitFiberUnmount(Wa,a)}catch{}switch(a.tag){case 26:We||aa(a,t),xa(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:We||aa(a,t);var l=Qe,i=wt;Ga(a.type)&&(Qe=a.stateNode,wt=!1),xa(e,t,a),os(a.stateNode),Qe=l,wt=i;break;case 5:We||aa(a,t);case 6:if(l=Qe,i=wt,Qe=null,xa(e,t,a),Qe=l,wt=i,Qe!==null)if(wt)try{(Qe.nodeType===9?Qe.body:Qe.nodeName==="HTML"?Qe.ownerDocument.body:Qe).removeChild(a.stateNode)}catch(o){Ye(a,t,o)}else try{Qe.removeChild(a.stateNode)}catch(o){Ye(a,t,o)}break;case 18:Qe!==null&&(wt?(e=Qe,Hh(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),vs(e)):Hh(Qe,a.stateNode));break;case 4:l=Qe,i=wt,Qe=a.stateNode.containerInfo,wt=!0,xa(e,t,a),Qe=l,wt=i;break;case 0:case 11:case 14:case 15:We||Oa(2,a,t),We||Oa(4,a,t),xa(e,t,a);break;case 1:We||(aa(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&Qf(a,t,l)),xa(e,t,a);break;case 21:xa(e,t,a);break;case 22:We=(l=We)||a.memoizedState!==null,xa(e,t,a),We=l;break;default:xa(e,t,a)}}function If(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{vs(e)}catch(a){Ye(t,t.return,a)}}function W0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Jf),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Jf),t;default:throw Error(c(435,e.tag))}}function Bc(e,t){var a=W0(e);t.forEach(function(l){var i=rg.bind(null,e,l);a.has(l)||(a.add(l),l.then(i,i))})}function Et(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var i=a[l],o=e,h=t,y=h;e:for(;y!==null;){switch(y.tag){case 27:if(Ga(y.type)){Qe=y.stateNode,wt=!1;break e}break;case 5:Qe=y.stateNode,wt=!1;break e;case 3:case 4:Qe=y.stateNode.containerInfo,wt=!0;break e}y=y.return}if(Qe===null)throw Error(c(160));Wf(o,h,i),Qe=null,wt=!1,o=i.alternate,o!==null&&(o.return=null),i.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)eh(t,e),t=t.sibling}var Pt=null;function eh(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Et(t,e),Ct(e),l&4&&(Oa(3,e,e.return),In(3,e),Oa(5,e,e.return));break;case 1:Et(t,e),Ct(e),l&512&&(We||a===null||aa(a,a.return)),l&64&&va&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var i=Pt;if(Et(t,e),Ct(e),l&512&&(We||a===null||aa(a,a.return)),l&4){var o=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,i=i.ownerDocument||i;t:switch(l){case"title":o=i.getElementsByTagName("title")[0],(!o||o[wn]||o[pt]||o.namespaceURI==="http://www.w3.org/2000/svg"||o.hasAttribute("itemprop"))&&(o=i.createElement(l),i.head.insertBefore(o,i.querySelector("head > title"))),ht(o,l,a),o[pt]=e,rt(o),l=o;break e;case"link":var h=$h("link","href",i).get(l+(a.href||""));if(h){for(var y=0;y<h.length;y++)if(o=h[y],o.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&o.getAttribute("rel")===(a.rel==null?null:a.rel)&&o.getAttribute("title")===(a.title==null?null:a.title)&&o.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){h.splice(y,1);break t}}o=i.createElement(l),ht(o,l,a),i.head.appendChild(o);break;case"meta":if(h=$h("meta","content",i).get(l+(a.content||""))){for(y=0;y<h.length;y++)if(o=h[y],o.getAttribute("content")===(a.content==null?null:""+a.content)&&o.getAttribute("name")===(a.name==null?null:a.name)&&o.getAttribute("property")===(a.property==null?null:a.property)&&o.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&o.getAttribute("charset")===(a.charSet==null?null:a.charSet)){h.splice(y,1);break t}}o=i.createElement(l),ht(o,l,a),i.head.appendChild(o);break;default:throw Error(c(468,l))}o[pt]=e,rt(o),l=o}e.stateNode=l}else Jh(i,e.type,e.stateNode);else e.stateNode=Kh(i,l,e.memoizedProps);else o!==l?(o===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):o.count--,l===null?Jh(i,e.type,e.stateNode):Kh(i,l,e.memoizedProps)):l===null&&e.stateNode!==null&&Oc(e,e.memoizedProps,a.memoizedProps)}break;case 27:Et(t,e),Ct(e),l&512&&(We||a===null||aa(a,a.return)),a!==null&&l&4&&Oc(e,e.memoizedProps,a.memoizedProps);break;case 5:if(Et(t,e),Ct(e),l&512&&(We||a===null||aa(a,a.return)),e.flags&32){i=e.stateNode;try{Tl(i,"")}catch(k){Ye(e,e.return,k)}}l&4&&e.stateNode!=null&&(i=e.memoizedProps,Oc(e,i,a!==null?a.memoizedProps:i)),l&1024&&(Lc=!0);break;case 6:if(Et(t,e),Ct(e),l&4){if(e.stateNode===null)throw Error(c(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(k){Ye(e,e.return,k)}}break;case 3:if(ki=null,i=Pt,Pt=zi(t.containerInfo),Et(t,e),Pt=i,Ct(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{vs(t.containerInfo)}catch(k){Ye(e,e.return,k)}Lc&&(Lc=!1,th(e));break;case 4:l=Pt,Pt=zi(e.stateNode.containerInfo),Et(t,e),Ct(e),Pt=l;break;case 12:Et(t,e),Ct(e);break;case 13:Et(t,e),Ct(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Xc=mt()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Bc(e,l)));break;case 22:i=e.memoizedState!==null;var w=a!==null&&a.memoizedState!==null,z=va,B=We;if(va=z||i,We=B||w,Et(t,e),We=B,va=z,Ct(e),l&8192)e:for(t=e.stateNode,t._visibility=i?t._visibility&-2:t._visibility|1,i&&(a===null||w||va||We||ml(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){w=a=t;try{if(o=w.stateNode,i)h=o.style,typeof h.setProperty=="function"?h.setProperty("display","none","important"):h.display="none";else{y=w.stateNode;var Y=w.memoizedProps.style,O=Y!=null&&Y.hasOwnProperty("display")?Y.display:null;y.style.display=O==null||typeof O=="boolean"?"":(""+O).trim()}}catch(k){Ye(w,w.return,k)}}}else if(t.tag===6){if(a===null){w=t;try{w.stateNode.nodeValue=i?"":w.memoizedProps}catch(k){Ye(w,w.return,k)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,Bc(e,a))));break;case 19:Et(t,e),Ct(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Bc(e,l)));break;case 30:break;case 21:break;default:Et(t,e),Ct(e)}}function Ct(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(Kf(l)){a=l;break}l=l.return}if(a==null)throw Error(c(160));switch(a.tag){case 27:var i=a.stateNode,o=kc(e);bi(e,o,i);break;case 5:var h=a.stateNode;a.flags&32&&(Tl(h,""),a.flags&=-33);var y=kc(e);bi(e,y,h);break;case 3:case 4:var w=a.stateNode.containerInfo,z=kc(e);Uc(e,z,w);break;default:throw Error(c(161))}}catch(B){Ye(e,e.return,B)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function th(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;th(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function ka(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Ff(e,t.alternate,t),t=t.sibling}function ml(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Oa(4,t,t.return),ml(t);break;case 1:aa(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&Qf(t,t.return,a),ml(t);break;case 27:os(t.stateNode);case 26:case 5:aa(t,t.return),ml(t);break;case 22:t.memoizedState===null&&ml(t);break;case 30:ml(t);break;default:ml(t)}e=e.sibling}}function Ua(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,i=e,o=t,h=o.flags;switch(o.tag){case 0:case 11:case 15:Ua(i,o,a),In(4,o);break;case 1:if(Ua(i,o,a),l=o,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(z){Ye(l,l.return,z)}if(l=o,i=l.updateQueue,i!==null){var y=l.stateNode;try{var w=i.shared.hiddenCallbacks;if(w!==null)for(i.shared.hiddenCallbacks=null,i=0;i<w.length;i++)Ed(w[i],y)}catch(z){Ye(l,l.return,z)}}a&&h&64&&Xf(o),es(o,o.return);break;case 27:$f(o);case 26:case 5:Ua(i,o,a),a&&l===null&&h&4&&Zf(o),es(o,o.return);break;case 12:Ua(i,o,a);break;case 13:Ua(i,o,a),a&&h&4&&If(i,o);break;case 22:o.memoizedState===null&&Ua(i,o,a),es(o,o.return);break;case 30:break;default:Ua(i,o,a)}t=t.sibling}}function qc(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&qn(a))}function Hc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&qn(e))}function la(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)ah(e,t,a,l),t=t.sibling}function ah(e,t,a,l){var i=t.flags;switch(t.tag){case 0:case 11:case 15:la(e,t,a,l),i&2048&&In(9,t);break;case 1:la(e,t,a,l);break;case 3:la(e,t,a,l),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&qn(e)));break;case 12:if(i&2048){la(e,t,a,l),e=t.stateNode;try{var o=t.memoizedProps,h=o.id,y=o.onPostCommit;typeof y=="function"&&y(h,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(w){Ye(t,t.return,w)}}else la(e,t,a,l);break;case 13:la(e,t,a,l);break;case 23:break;case 22:o=t.stateNode,h=t.alternate,t.memoizedState!==null?o._visibility&2?la(e,t,a,l):ts(e,t):o._visibility&2?la(e,t,a,l):(o._visibility|=2,Kl(e,t,a,l,(t.subtreeFlags&10256)!==0)),i&2048&&qc(h,t);break;case 24:la(e,t,a,l),i&2048&&Hc(t.alternate,t);break;default:la(e,t,a,l)}}function Kl(e,t,a,l,i){for(i=i&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var o=e,h=t,y=a,w=l,z=h.flags;switch(h.tag){case 0:case 11:case 15:Kl(o,h,y,w,i),In(8,h);break;case 23:break;case 22:var B=h.stateNode;h.memoizedState!==null?B._visibility&2?Kl(o,h,y,w,i):ts(o,h):(B._visibility|=2,Kl(o,h,y,w,i)),i&&z&2048&&qc(h.alternate,h);break;case 24:Kl(o,h,y,w,i),i&&z&2048&&Hc(h.alternate,h);break;default:Kl(o,h,y,w,i)}t=t.sibling}}function ts(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,i=l.flags;switch(l.tag){case 22:ts(a,l),i&2048&&qc(l.alternate,l);break;case 24:ts(a,l),i&2048&&Hc(l.alternate,l);break;default:ts(a,l)}t=t.sibling}}var as=8192;function $l(e){if(e.subtreeFlags&as)for(e=e.child;e!==null;)lh(e),e=e.sibling}function lh(e){switch(e.tag){case 26:$l(e),e.flags&as&&e.memoizedState!==null&&Lg(Pt,e.memoizedState,e.memoizedProps);break;case 5:$l(e);break;case 3:case 4:var t=Pt;Pt=zi(e.stateNode.containerInfo),$l(e),Pt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=as,as=16777216,$l(e),as=t):$l(e));break;default:$l(e)}}function nh(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function ls(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];ot=l,ih(l,e)}nh(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)sh(e),e=e.sibling}function sh(e){switch(e.tag){case 0:case 11:case 15:ls(e),e.flags&2048&&Oa(9,e,e.return);break;case 3:ls(e);break;case 12:ls(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Si(e)):ls(e);break;default:ls(e)}}function Si(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];ot=l,ih(l,e)}nh(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Oa(8,t,t.return),Si(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,Si(t));break;default:Si(t)}e=e.sibling}}function ih(e,t){for(;ot!==null;){var a=ot;switch(a.tag){case 0:case 11:case 15:Oa(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:qn(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,ot=l;else e:for(a=e;ot!==null;){l=ot;var i=l.sibling,o=l.return;if(Pf(l),l===a){ot=null;break e}if(i!==null){i.return=o,ot=i;break e}ot=o}}}var I0={getCacheForType:function(e){var t=gt(lt),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},eg=typeof WeakMap=="function"?WeakMap:Map,Le=0,Xe=null,Ne=null,Te=0,Be=0,Dt=null,La=!1,Jl=!1,Vc=!1,ya=0,Je=0,Ba=0,pl=0,Gc=0,Gt=0,Fl=0,ns=null,_t=null,Yc=!1,Xc=0,ji=1/0,Ni=null,qa=null,ft=0,Ha=null,Pl=null,Wl=0,Qc=0,Zc=null,rh=null,ss=0,Kc=null;function At(){if((Le&2)!==0&&Te!==0)return Te&-Te;if(C.T!==null){var e=ql;return e!==0?e:eo()}return ju()}function ch(){Gt===0&&(Gt=(Te&536870912)===0||ze?xu():536870912);var e=Vt.current;return e!==null&&(e.flags|=32),Gt}function zt(e,t,a){(e===Xe&&(Be===2||Be===9)||e.cancelPendingCommit!==null)&&(Il(e,0),Va(e,Te,Gt,!1)),Nn(e,a),((Le&2)===0||e!==Xe)&&(e===Xe&&((Le&2)===0&&(pl|=a),Je===4&&Va(e,Te,Gt,!1)),na(e))}function oh(e,t,a){if((Le&6)!==0)throw Error(c(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||jn(e,t),i=l?lg(e,t):Fc(e,t,!0),o=l;do{if(i===0){Jl&&!l&&Va(e,t,0,!1);break}else{if(a=e.current.alternate,o&&!tg(a)){i=Fc(e,t,!1),o=!1;continue}if(i===2){if(o=t,e.errorRecoveryDisabledLanes&o)var h=0;else h=e.pendingLanes&-536870913,h=h!==0?h:h&536870912?536870912:0;if(h!==0){t=h;e:{var y=e;i=ns;var w=y.current.memoizedState.isDehydrated;if(w&&(Il(y,h).flags|=256),h=Fc(y,h,!1),h!==2){if(Vc&&!w){y.errorRecoveryDisabledLanes|=o,pl|=o,i=4;break e}o=_t,_t=i,o!==null&&(_t===null?_t=o:_t.push.apply(_t,o))}i=h}if(o=!1,i!==2)continue}}if(i===1){Il(e,0),Va(e,t,0,!0);break}e:{switch(l=e,o=i,o){case 0:case 1:throw Error(c(345));case 4:if((t&4194048)!==t)break;case 6:Va(l,t,Gt,!La);break e;case 2:_t=null;break;case 3:case 5:break;default:throw Error(c(329))}if((t&62914560)===t&&(i=Xc+300-mt(),10<i)){if(Va(l,t,Gt,!La),Os(l,0,!0)!==0)break e;l.timeoutHandle=Bh(uh.bind(null,l,a,_t,Ni,Yc,t,Gt,pl,Fl,La,o,2,-0,0),i);break e}uh(l,a,_t,Ni,Yc,t,Gt,pl,Fl,La,o,0,-0,0)}}break}while(!0);na(e)}function uh(e,t,a,l,i,o,h,y,w,z,B,Y,O,k){if(e.timeoutHandle=-1,Y=t.subtreeFlags,(Y&8192||(Y&16785408)===16785408)&&(fs={stylesheets:null,count:0,unsuspend:Ug},lh(t),Y=Bg(),Y!==null)){e.cancelPendingCommit=Y(vh.bind(null,e,t,o,a,l,i,h,y,w,B,1,O,k)),Va(e,o,h,!z);return}vh(e,t,o,a,l,i,h,y,w)}function tg(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var i=a[l],o=i.getSnapshot;i=i.value;try{if(!Mt(o(),i))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Va(e,t,a,l){t&=~Gc,t&=~pl,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var i=t;0<i;){var o=31-Rt(i),h=1<<o;l[o]=-1,i&=~h}a!==0&&bu(e,a,t)}function wi(){return(Le&6)===0?(is(0),!1):!0}function $c(){if(Ne!==null){if(Be===0)var e=Ne.return;else e=Ne,da=ol=null,dc(e),Ql=null,Fn=0,e=Ne;for(;e!==null;)Yf(e.alternate,e),e=e.return;Ne=null}}function Il(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,yg(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),$c(),Xe=e,Ne=a=ca(e.current,null),Te=t,Be=0,Dt=null,La=!1,Jl=jn(e,t),Vc=!1,Fl=Gt=Gc=pl=Ba=Je=0,_t=ns=null,Yc=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var i=31-Rt(l),o=1<<i;t|=e[i],l&=~o}return ya=t,Zs(),a}function dh(e,t){xe=null,C.H=di,t===Vn||t===ti?(t=Md(),Be=3):t===wd?(t=Md(),Be=4):Be=t===Ef?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Dt=t,Ne===null&&(Je=1,gi(e,Lt(t,e.current)))}function fh(){var e=C.H;return C.H=di,e===null?di:e}function hh(){var e=C.A;return C.A=I0,e}function Jc(){Je=4,La||(Te&4194048)!==Te&&Vt.current!==null||(Jl=!0),(Ba&134217727)===0&&(pl&134217727)===0||Xe===null||Va(Xe,Te,Gt,!1)}function Fc(e,t,a){var l=Le;Le|=2;var i=fh(),o=hh();(Xe!==e||Te!==t)&&(Ni=null,Il(e,t)),t=!1;var h=Je;e:do try{if(Be!==0&&Ne!==null){var y=Ne,w=Dt;switch(Be){case 8:$c(),h=6;break e;case 3:case 2:case 9:case 6:Vt.current===null&&(t=!0);var z=Be;if(Be=0,Dt=null,en(e,y,w,z),a&&Jl){h=0;break e}break;default:z=Be,Be=0,Dt=null,en(e,y,w,z)}}ag(),h=Je;break}catch(B){dh(e,B)}while(!0);return t&&e.shellSuspendCounter++,da=ol=null,Le=l,C.H=i,C.A=o,Ne===null&&(Xe=null,Te=0,Zs()),h}function ag(){for(;Ne!==null;)mh(Ne)}function lg(e,t){var a=Le;Le|=2;var l=fh(),i=hh();Xe!==e||Te!==t?(Ni=null,ji=mt()+500,Il(e,t)):Jl=jn(e,t);e:do try{if(Be!==0&&Ne!==null){t=Ne;var o=Dt;t:switch(Be){case 1:Be=0,Dt=null,en(e,t,o,1);break;case 2:case 9:if(_d(o)){Be=0,Dt=null,ph(t);break}t=function(){Be!==2&&Be!==9||Xe!==e||(Be=7),na(e)},o.then(t,t);break e;case 3:Be=7;break e;case 4:Be=5;break e;case 7:_d(o)?(Be=0,Dt=null,ph(t)):(Be=0,Dt=null,en(e,t,o,7));break;case 5:var h=null;switch(Ne.tag){case 26:h=Ne.memoizedState;case 5:case 27:var y=Ne;if(!h||Fh(h)){Be=0,Dt=null;var w=y.sibling;if(w!==null)Ne=w;else{var z=y.return;z!==null?(Ne=z,_i(z)):Ne=null}break t}}Be=0,Dt=null,en(e,t,o,5);break;case 6:Be=0,Dt=null,en(e,t,o,6);break;case 8:$c(),Je=6;break e;default:throw Error(c(462))}}ng();break}catch(B){dh(e,B)}while(!0);return da=ol=null,C.H=l,C.A=i,Le=a,Ne!==null?0:(Xe=null,Te=0,Zs(),Je)}function ng(){for(;Ne!==null&&!ur();)mh(Ne)}function mh(e){var t=Vf(e.alternate,e,ya);e.memoizedProps=e.pendingProps,t===null?_i(e):Ne=t}function ph(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=kf(a,t,t.pendingProps,t.type,void 0,Te);break;case 11:t=kf(a,t,t.pendingProps,t.type.render,t.ref,Te);break;case 5:dc(t);default:Yf(a,t),t=Ne=pd(t,ya),t=Vf(a,t,ya)}e.memoizedProps=e.pendingProps,t===null?_i(e):Ne=t}function en(e,t,a,l){da=ol=null,dc(t),Ql=null,Fn=0;var i=t.return;try{if(K0(e,i,t,a,Te)){Je=1,gi(e,Lt(a,e.current)),Ne=null;return}}catch(o){if(i!==null)throw Ne=i,o;Je=1,gi(e,Lt(a,e.current)),Ne=null;return}t.flags&32768?(ze||l===1?e=!0:Jl||(Te&536870912)!==0?e=!1:(La=e=!0,(l===2||l===9||l===3||l===6)&&(l=Vt.current,l!==null&&l.tag===13&&(l.flags|=16384))),gh(t,e)):_i(t)}function _i(e){var t=e;do{if((t.flags&32768)!==0){gh(t,La);return}e=t.return;var a=J0(t.alternate,t,ya);if(a!==null){Ne=a;return}if(t=t.sibling,t!==null){Ne=t;return}Ne=t=e}while(t!==null);Je===0&&(Je=5)}function gh(e,t){do{var a=F0(e.alternate,e);if(a!==null){a.flags&=32767,Ne=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){Ne=e;return}Ne=e=a}while(e!==null);Je=6,Ne=null}function vh(e,t,a,l,i,o,h,y,w){e.cancelPendingCommit=null;do Ri();while(ft!==0);if((Le&6)!==0)throw Error(c(327));if(t!==null){if(t===e.current)throw Error(c(177));if(o=t.lanes|t.childLanes,o|=Hr,Up(e,a,o,h,y,w),e===Xe&&(Ne=Xe=null,Te=0),Pl=t,Ha=e,Wl=a,Qc=o,Zc=i,rh=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,cg(Pa,function(){return jh(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=C.T,C.T=null,i=Q.p,Q.p=2,h=Le,Le|=4;try{P0(e,t,a)}finally{Le=h,Q.p=i,C.T=l}}ft=1,xh(),yh(),bh()}}function xh(){if(ft===1){ft=0;var e=Ha,t=Pl,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=C.T,C.T=null;var l=Q.p;Q.p=2;var i=Le;Le|=4;try{eh(t,e);var o=co,h=sd(e.containerInfo),y=o.focusedElem,w=o.selectionRange;if(h!==y&&y&&y.ownerDocument&&nd(y.ownerDocument.documentElement,y)){if(w!==null&&kr(y)){var z=w.start,B=w.end;if(B===void 0&&(B=z),"selectionStart"in y)y.selectionStart=z,y.selectionEnd=Math.min(B,y.value.length);else{var Y=y.ownerDocument||document,O=Y&&Y.defaultView||window;if(O.getSelection){var k=O.getSelection(),he=y.textContent.length,de=Math.min(w.start,he),Ve=w.end===void 0?de:Math.min(w.end,he);!k.extend&&de>Ve&&(h=Ve,Ve=de,de=h);var E=ld(y,de),M=ld(y,Ve);if(E&&M&&(k.rangeCount!==1||k.anchorNode!==E.node||k.anchorOffset!==E.offset||k.focusNode!==M.node||k.focusOffset!==M.offset)){var D=Y.createRange();D.setStart(E.node,E.offset),k.removeAllRanges(),de>Ve?(k.addRange(D),k.extend(M.node,M.offset)):(D.setEnd(M.node,M.offset),k.addRange(D))}}}}for(Y=[],k=y;k=k.parentNode;)k.nodeType===1&&Y.push({element:k,left:k.scrollLeft,top:k.scrollTop});for(typeof y.focus=="function"&&y.focus(),y=0;y<Y.length;y++){var H=Y[y];H.element.scrollLeft=H.left,H.element.scrollTop=H.top}}Bi=!!ro,co=ro=null}finally{Le=i,Q.p=l,C.T=a}}e.current=t,ft=2}}function yh(){if(ft===2){ft=0;var e=Ha,t=Pl,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=C.T,C.T=null;var l=Q.p;Q.p=2;var i=Le;Le|=4;try{Ff(e,t.alternate,t)}finally{Le=i,Q.p=l,C.T=a}}ft=3}}function bh(){if(ft===4||ft===3){ft=0,dr();var e=Ha,t=Pl,a=Wl,l=rh;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?ft=5:(ft=0,Pl=Ha=null,Sh(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(qa=null),mr(a),t=t.stateNode,yt&&typeof yt.onCommitFiberRoot=="function")try{yt.onCommitFiberRoot(Wa,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=C.T,i=Q.p,Q.p=2,C.T=null;try{for(var o=e.onRecoverableError,h=0;h<l.length;h++){var y=l[h];o(y.value,{componentStack:y.stack})}}finally{C.T=t,Q.p=i}}(Wl&3)!==0&&Ri(),na(e),i=e.pendingLanes,(a&4194090)!==0&&(i&42)!==0?e===Kc?ss++:(ss=0,Kc=e):ss=0,is(0)}}function Sh(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,qn(t)))}function Ri(e){return xh(),yh(),bh(),jh()}function jh(){if(ft!==5)return!1;var e=Ha,t=Qc;Qc=0;var a=mr(Wl),l=C.T,i=Q.p;try{Q.p=32>a?32:a,C.T=null,a=Zc,Zc=null;var o=Ha,h=Wl;if(ft=0,Pl=Ha=null,Wl=0,(Le&6)!==0)throw Error(c(331));var y=Le;if(Le|=4,sh(o.current),ah(o,o.current,h,a),Le=y,is(0,!1),yt&&typeof yt.onPostCommitFiberRoot=="function")try{yt.onPostCommitFiberRoot(Wa,o)}catch{}return!0}finally{Q.p=i,C.T=l,Sh(e,t)}}function Nh(e,t,a){t=Lt(a,t),t=_c(e.stateNode,t,2),e=Ca(e,t,2),e!==null&&(Nn(e,2),na(e))}function Ye(e,t,a){if(e.tag===3)Nh(e,e,a);else for(;t!==null;){if(t.tag===3){Nh(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(qa===null||!qa.has(l))){e=Lt(a,e),a=Mf(2),l=Ca(t,a,2),l!==null&&(Tf(a,l,t,e),Nn(l,2),na(l));break}}t=t.return}}function Pc(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new eg;var i=new Set;l.set(t,i)}else i=l.get(t),i===void 0&&(i=new Set,l.set(t,i));i.has(a)||(Vc=!0,i.add(a),e=sg.bind(null,e,t,a),t.then(e,e))}function sg(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,Xe===e&&(Te&a)===a&&(Je===4||Je===3&&(Te&62914560)===Te&&300>mt()-Xc?(Le&2)===0&&Il(e,0):Gc|=a,Fl===Te&&(Fl=0)),na(e)}function wh(e,t){t===0&&(t=yu()),e=kl(e,t),e!==null&&(Nn(e,t),na(e))}function ig(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),wh(e,a)}function rg(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,i=e.memoizedState;i!==null&&(a=i.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(c(314))}l!==null&&l.delete(t),wh(e,a)}function cg(e,t){return Jt(e,t)}var Mi=null,tn=null,Wc=!1,Ti=!1,Ic=!1,gl=0;function na(e){e!==tn&&e.next===null&&(tn===null?Mi=tn=e:tn=tn.next=e),Ti=!0,Wc||(Wc=!0,ug())}function is(e,t){if(!Ic&&Ti){Ic=!0;do for(var a=!1,l=Mi;l!==null;){if(e!==0){var i=l.pendingLanes;if(i===0)var o=0;else{var h=l.suspendedLanes,y=l.pingedLanes;o=(1<<31-Rt(42|e)+1)-1,o&=i&~(h&~y),o=o&201326741?o&201326741|1:o?o|2:0}o!==0&&(a=!0,Th(l,o))}else o=Te,o=Os(l,l===Xe?o:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(o&3)===0||jn(l,o)||(a=!0,Th(l,o));l=l.next}while(a);Ic=!1}}function og(){_h()}function _h(){Ti=Wc=!1;var e=0;gl!==0&&(xg()&&(e=gl),gl=0);for(var t=mt(),a=null,l=Mi;l!==null;){var i=l.next,o=Rh(l,t);o===0?(l.next=null,a===null?Mi=i:a.next=i,i===null&&(tn=a)):(a=l,(e!==0||(o&3)!==0)&&(Ti=!0)),l=i}is(e)}function Rh(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes&-62914561;0<o;){var h=31-Rt(o),y=1<<h,w=i[h];w===-1?((y&a)===0||(y&l)!==0)&&(i[h]=kp(y,t)):w<=t&&(e.expiredLanes|=y),o&=~y}if(t=Xe,a=Te,a=Os(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(Be===2||Be===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&xn(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||jn(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&xn(l),mr(a)){case 2:case 8:a=bn;break;case 32:a=Pa;break;case 268435456:a=Fe;break;default:a=Pa}return l=Mh.bind(null,e),a=Jt(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&xn(l),e.callbackPriority=2,e.callbackNode=null,2}function Mh(e,t){if(ft!==0&&ft!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(Ri()&&e.callbackNode!==a)return null;var l=Te;return l=Os(e,e===Xe?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(oh(e,l,t),Rh(e,mt()),e.callbackNode!=null&&e.callbackNode===a?Mh.bind(null,e):null)}function Th(e,t){if(Ri())return null;oh(e,t,!0)}function ug(){bg(function(){(Le&6)!==0?Jt(bl,og):_h()})}function eo(){return gl===0&&(gl=xu()),gl}function Eh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:qs(""+e)}function Ch(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function dg(e,t,a,l,i){if(t==="submit"&&a&&a.stateNode===i){var o=Eh((i[St]||null).action),h=l.submitter;h&&(t=(t=h[St]||null)?Eh(t.formAction):h.getAttribute("formAction"),t!==null&&(o=t,h=null));var y=new Ys("action","action",null,l,i);e.push({event:y,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(gl!==0){var w=h?Ch(i,h):new FormData(i);bc(a,{pending:!0,data:w,method:i.method,action:o},null,w)}}else typeof o=="function"&&(y.preventDefault(),w=h?Ch(i,h):new FormData(i),bc(a,{pending:!0,data:w,method:i.method,action:o},o,w))},currentTarget:i}]})}}for(var to=0;to<qr.length;to++){var ao=qr[to],fg=ao.toLowerCase(),hg=ao[0].toUpperCase()+ao.slice(1);Ft(fg,"on"+hg)}Ft(cd,"onAnimationEnd"),Ft(od,"onAnimationIteration"),Ft(ud,"onAnimationStart"),Ft("dblclick","onDoubleClick"),Ft("focusin","onFocus"),Ft("focusout","onBlur"),Ft(C0,"onTransitionRun"),Ft(D0,"onTransitionStart"),Ft(A0,"onTransitionCancel"),Ft(dd,"onTransitionEnd"),_l("onMouseEnter",["mouseout","mouseover"]),_l("onMouseLeave",["mouseout","mouseover"]),_l("onPointerEnter",["pointerout","pointerover"]),_l("onPointerLeave",["pointerout","pointerover"]),el("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),el("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),el("onBeforeInput",["compositionend","keypress","textInput","paste"]),el("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),el("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),el("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var rs="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),mg=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(rs));function Dh(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],i=l.event;l=l.listeners;e:{var o=void 0;if(t)for(var h=l.length-1;0<=h;h--){var y=l[h],w=y.instance,z=y.currentTarget;if(y=y.listener,w!==o&&i.isPropagationStopped())break e;o=y,i.currentTarget=z;try{o(i)}catch(B){pi(B)}i.currentTarget=null,o=w}else for(h=0;h<l.length;h++){if(y=l[h],w=y.instance,z=y.currentTarget,y=y.listener,w!==o&&i.isPropagationStopped())break e;o=y,i.currentTarget=z;try{o(i)}catch(B){pi(B)}i.currentTarget=null,o=w}}}}function we(e,t){var a=t[pr];a===void 0&&(a=t[pr]=new Set);var l=e+"__bubble";a.has(l)||(Ah(t,e,2,!1),a.add(l))}function lo(e,t,a){var l=0;t&&(l|=4),Ah(a,e,l,t)}var Ei="_reactListening"+Math.random().toString(36).slice(2);function no(e){if(!e[Ei]){e[Ei]=!0,wu.forEach(function(a){a!=="selectionchange"&&(mg.has(a)||lo(a,!1,e),lo(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ei]||(t[Ei]=!0,lo("selectionchange",!1,t))}}function Ah(e,t,a,l){switch(am(t)){case 2:var i=Vg;break;case 8:i=Gg;break;default:i=yo}a=i.bind(null,t,a,e),i=void 0,!Rr||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),l?i!==void 0?e.addEventListener(t,a,{capture:!0,passive:i}):e.addEventListener(t,a,!0):i!==void 0?e.addEventListener(t,a,{passive:i}):e.addEventListener(t,a,!1)}function so(e,t,a,l,i){var o=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var h=l.tag;if(h===3||h===4){var y=l.stateNode.containerInfo;if(y===i)break;if(h===4)for(h=l.return;h!==null;){var w=h.tag;if((w===3||w===4)&&h.stateNode.containerInfo===i)return;h=h.return}for(;y!==null;){if(h=jl(y),h===null)return;if(w=h.tag,w===5||w===6||w===26||w===27){l=o=h;continue e}y=y.parentNode}}l=l.return}Bu(function(){var z=o,B=wr(a),Y=[];e:{var O=fd.get(e);if(O!==void 0){var k=Ys,he=e;switch(e){case"keypress":if(Vs(a)===0)break e;case"keydown":case"keyup":k=c0;break;case"focusin":he="focus",k=Cr;break;case"focusout":he="blur",k=Cr;break;case"beforeblur":case"afterblur":k=Cr;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":k=Vu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":k=Fp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":k=d0;break;case cd:case od:case ud:k=Ip;break;case dd:k=h0;break;case"scroll":case"scrollend":k=$p;break;case"wheel":k=p0;break;case"copy":case"cut":case"paste":k=t0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":k=Yu;break;case"toggle":case"beforetoggle":k=v0}var de=(t&4)!==0,Ve=!de&&(e==="scroll"||e==="scrollend"),E=de?O!==null?O+"Capture":null:O;de=[];for(var M=z,D;M!==null;){var H=M;if(D=H.stateNode,H=H.tag,H!==5&&H!==26&&H!==27||D===null||E===null||(H=Rn(M,E),H!=null&&de.push(cs(M,H,D))),Ve)break;M=M.return}0<de.length&&(O=new k(O,he,null,a,B),Y.push({event:O,listeners:de}))}}if((t&7)===0){e:{if(O=e==="mouseover"||e==="pointerover",k=e==="mouseout"||e==="pointerout",O&&a!==Nr&&(he=a.relatedTarget||a.fromElement)&&(jl(he)||he[Sl]))break e;if((k||O)&&(O=B.window===B?B:(O=B.ownerDocument)?O.defaultView||O.parentWindow:window,k?(he=a.relatedTarget||a.toElement,k=z,he=he?jl(he):null,he!==null&&(Ve=m(he),de=he.tag,he!==Ve||de!==5&&de!==27&&de!==6)&&(he=null)):(k=null,he=z),k!==he)){if(de=Vu,H="onMouseLeave",E="onMouseEnter",M="mouse",(e==="pointerout"||e==="pointerover")&&(de=Yu,H="onPointerLeave",E="onPointerEnter",M="pointer"),Ve=k==null?O:_n(k),D=he==null?O:_n(he),O=new de(H,M+"leave",k,a,B),O.target=Ve,O.relatedTarget=D,H=null,jl(B)===z&&(de=new de(E,M+"enter",he,a,B),de.target=D,de.relatedTarget=Ve,H=de),Ve=H,k&&he)t:{for(de=k,E=he,M=0,D=de;D;D=an(D))M++;for(D=0,H=E;H;H=an(H))D++;for(;0<M-D;)de=an(de),M--;for(;0<D-M;)E=an(E),D--;for(;M--;){if(de===E||E!==null&&de===E.alternate)break t;de=an(de),E=an(E)}de=null}else de=null;k!==null&&zh(Y,O,k,de,!1),he!==null&&Ve!==null&&zh(Y,Ve,he,de,!0)}}e:{if(O=z?_n(z):window,k=O.nodeName&&O.nodeName.toLowerCase(),k==="select"||k==="input"&&O.type==="file")var ae=Pu;else if(Ju(O))if(Wu)ae=M0;else{ae=_0;var Se=w0}else k=O.nodeName,!k||k.toLowerCase()!=="input"||O.type!=="checkbox"&&O.type!=="radio"?z&&jr(z.elementType)&&(ae=Pu):ae=R0;if(ae&&(ae=ae(e,z))){Fu(Y,ae,a,B);break e}Se&&Se(e,O,z),e==="focusout"&&z&&O.type==="number"&&z.memoizedProps.value!=null&&Sr(O,"number",O.value)}switch(Se=z?_n(z):window,e){case"focusin":(Ju(Se)||Se.contentEditable==="true")&&(Al=Se,Ur=z,On=null);break;case"focusout":On=Ur=Al=null;break;case"mousedown":Lr=!0;break;case"contextmenu":case"mouseup":case"dragend":Lr=!1,id(Y,a,B);break;case"selectionchange":if(E0)break;case"keydown":case"keyup":id(Y,a,B)}var ie;if(Ar)e:{switch(e){case"compositionstart":var fe="onCompositionStart";break e;case"compositionend":fe="onCompositionEnd";break e;case"compositionupdate":fe="onCompositionUpdate";break e}fe=void 0}else Dl?Ku(e,a)&&(fe="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(fe="onCompositionStart");fe&&(Xu&&a.locale!=="ko"&&(Dl||fe!=="onCompositionStart"?fe==="onCompositionEnd"&&Dl&&(ie=qu()):(Ra=B,Mr="value"in Ra?Ra.value:Ra.textContent,Dl=!0)),Se=Ci(z,fe),0<Se.length&&(fe=new Gu(fe,e,null,a,B),Y.push({event:fe,listeners:Se}),ie?fe.data=ie:(ie=$u(a),ie!==null&&(fe.data=ie)))),(ie=y0?b0(e,a):S0(e,a))&&(fe=Ci(z,"onBeforeInput"),0<fe.length&&(Se=new Gu("onBeforeInput","beforeinput",null,a,B),Y.push({event:Se,listeners:fe}),Se.data=ie)),dg(Y,e,z,a,B)}Dh(Y,t)})}function cs(e,t,a){return{instance:e,listener:t,currentTarget:a}}function Ci(e,t){for(var a=t+"Capture",l=[];e!==null;){var i=e,o=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||o===null||(i=Rn(e,a),i!=null&&l.unshift(cs(e,i,o)),i=Rn(e,t),i!=null&&l.push(cs(e,i,o))),e.tag===3)return l;e=e.return}return[]}function an(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function zh(e,t,a,l,i){for(var o=t._reactName,h=[];a!==null&&a!==l;){var y=a,w=y.alternate,z=y.stateNode;if(y=y.tag,w!==null&&w===l)break;y!==5&&y!==26&&y!==27||z===null||(w=z,i?(z=Rn(a,o),z!=null&&h.unshift(cs(a,z,w))):i||(z=Rn(a,o),z!=null&&h.push(cs(a,z,w)))),a=a.return}h.length!==0&&e.push({event:t,listeners:h})}var pg=/\r\n?/g,gg=/\u0000|\uFFFD/g;function Oh(e){return(typeof e=="string"?e:""+e).replace(pg,`
`).replace(gg,"")}function kh(e,t){return t=Oh(t),Oh(e)===t}function Di(){}function He(e,t,a,l,i,o){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||Tl(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&Tl(e,""+l);break;case"className":Us(e,"class",l);break;case"tabIndex":Us(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Us(e,a,l);break;case"style":Uu(e,l,o);break;case"data":if(t!=="object"){Us(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=qs(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof o=="function"&&(a==="formAction"?(t!=="input"&&He(e,t,"name",i.name,i,null),He(e,t,"formEncType",i.formEncType,i,null),He(e,t,"formMethod",i.formMethod,i,null),He(e,t,"formTarget",i.formTarget,i,null)):(He(e,t,"encType",i.encType,i,null),He(e,t,"method",i.method,i,null),He(e,t,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=qs(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=Di);break;case"onScroll":l!=null&&we("scroll",e);break;case"onScrollEnd":l!=null&&we("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(a=l.__html,a!=null){if(i.children!=null)throw Error(c(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=qs(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":we("beforetoggle",e),we("toggle",e),ks(e,"popover",l);break;case"xlinkActuate":ia(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":ia(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":ia(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":ia(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":ia(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":ia(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":ia(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":ia(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":ia(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":ks(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=Zp.get(a)||a,ks(e,a,l))}}function io(e,t,a,l,i,o){switch(a){case"style":Uu(e,l,o);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(a=l.__html,a!=null){if(i.children!=null)throw Error(c(60));e.innerHTML=a}}break;case"children":typeof l=="string"?Tl(e,l):(typeof l=="number"||typeof l=="bigint")&&Tl(e,""+l);break;case"onScroll":l!=null&&we("scroll",e);break;case"onScrollEnd":l!=null&&we("scrollend",e);break;case"onClick":l!=null&&(e.onclick=Di);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!_u.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(i=a.endsWith("Capture"),t=a.slice(2,i?a.length-7:void 0),o=e[St]||null,o=o!=null?o[a]:null,typeof o=="function"&&e.removeEventListener(t,o,i),typeof l=="function")){typeof o!="function"&&o!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,i);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):ks(e,a,l)}}}function ht(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":we("error",e),we("load",e);var l=!1,i=!1,o;for(o in a)if(a.hasOwnProperty(o)){var h=a[o];if(h!=null)switch(o){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:He(e,t,o,h,a,null)}}i&&He(e,t,"srcSet",a.srcSet,a,null),l&&He(e,t,"src",a.src,a,null);return;case"input":we("invalid",e);var y=o=h=i=null,w=null,z=null;for(l in a)if(a.hasOwnProperty(l)){var B=a[l];if(B!=null)switch(l){case"name":i=B;break;case"type":h=B;break;case"checked":w=B;break;case"defaultChecked":z=B;break;case"value":o=B;break;case"defaultValue":y=B;break;case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(c(137,t));break;default:He(e,t,l,B,a,null)}}Au(e,o,y,w,z,h,i,!1),Ls(e);return;case"select":we("invalid",e),l=h=o=null;for(i in a)if(a.hasOwnProperty(i)&&(y=a[i],y!=null))switch(i){case"value":o=y;break;case"defaultValue":h=y;break;case"multiple":l=y;default:He(e,t,i,y,a,null)}t=o,a=h,e.multiple=!!l,t!=null?Ml(e,!!l,t,!1):a!=null&&Ml(e,!!l,a,!0);return;case"textarea":we("invalid",e),o=i=l=null;for(h in a)if(a.hasOwnProperty(h)&&(y=a[h],y!=null))switch(h){case"value":l=y;break;case"defaultValue":i=y;break;case"children":o=y;break;case"dangerouslySetInnerHTML":if(y!=null)throw Error(c(91));break;default:He(e,t,h,y,a,null)}Ou(e,l,i,o),Ls(e);return;case"option":for(w in a)if(a.hasOwnProperty(w)&&(l=a[w],l!=null))switch(w){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:He(e,t,w,l,a,null)}return;case"dialog":we("beforetoggle",e),we("toggle",e),we("cancel",e),we("close",e);break;case"iframe":case"object":we("load",e);break;case"video":case"audio":for(l=0;l<rs.length;l++)we(rs[l],e);break;case"image":we("error",e),we("load",e);break;case"details":we("toggle",e);break;case"embed":case"source":case"link":we("error",e),we("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(z in a)if(a.hasOwnProperty(z)&&(l=a[z],l!=null))switch(z){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:He(e,t,z,l,a,null)}return;default:if(jr(t)){for(B in a)a.hasOwnProperty(B)&&(l=a[B],l!==void 0&&io(e,t,B,l,a,void 0));return}}for(y in a)a.hasOwnProperty(y)&&(l=a[y],l!=null&&He(e,t,y,l,a,null))}function vg(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,o=null,h=null,y=null,w=null,z=null,B=null;for(k in a){var Y=a[k];if(a.hasOwnProperty(k)&&Y!=null)switch(k){case"checked":break;case"value":break;case"defaultValue":w=Y;default:l.hasOwnProperty(k)||He(e,t,k,null,l,Y)}}for(var O in l){var k=l[O];if(Y=a[O],l.hasOwnProperty(O)&&(k!=null||Y!=null))switch(O){case"type":o=k;break;case"name":i=k;break;case"checked":z=k;break;case"defaultChecked":B=k;break;case"value":h=k;break;case"defaultValue":y=k;break;case"children":case"dangerouslySetInnerHTML":if(k!=null)throw Error(c(137,t));break;default:k!==Y&&He(e,t,O,k,l,Y)}}br(e,h,y,w,z,B,o,i);return;case"select":k=h=y=O=null;for(o in a)if(w=a[o],a.hasOwnProperty(o)&&w!=null)switch(o){case"value":break;case"multiple":k=w;default:l.hasOwnProperty(o)||He(e,t,o,null,l,w)}for(i in l)if(o=l[i],w=a[i],l.hasOwnProperty(i)&&(o!=null||w!=null))switch(i){case"value":O=o;break;case"defaultValue":y=o;break;case"multiple":h=o;default:o!==w&&He(e,t,i,o,l,w)}t=y,a=h,l=k,O!=null?Ml(e,!!a,O,!1):!!l!=!!a&&(t!=null?Ml(e,!!a,t,!0):Ml(e,!!a,a?[]:"",!1));return;case"textarea":k=O=null;for(y in a)if(i=a[y],a.hasOwnProperty(y)&&i!=null&&!l.hasOwnProperty(y))switch(y){case"value":break;case"children":break;default:He(e,t,y,null,l,i)}for(h in l)if(i=l[h],o=a[h],l.hasOwnProperty(h)&&(i!=null||o!=null))switch(h){case"value":O=i;break;case"defaultValue":k=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(c(91));break;default:i!==o&&He(e,t,h,i,l,o)}zu(e,O,k);return;case"option":for(var he in a)if(O=a[he],a.hasOwnProperty(he)&&O!=null&&!l.hasOwnProperty(he))switch(he){case"selected":e.selected=!1;break;default:He(e,t,he,null,l,O)}for(w in l)if(O=l[w],k=a[w],l.hasOwnProperty(w)&&O!==k&&(O!=null||k!=null))switch(w){case"selected":e.selected=O&&typeof O!="function"&&typeof O!="symbol";break;default:He(e,t,w,O,l,k)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var de in a)O=a[de],a.hasOwnProperty(de)&&O!=null&&!l.hasOwnProperty(de)&&He(e,t,de,null,l,O);for(z in l)if(O=l[z],k=a[z],l.hasOwnProperty(z)&&O!==k&&(O!=null||k!=null))switch(z){case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(c(137,t));break;default:He(e,t,z,O,l,k)}return;default:if(jr(t)){for(var Ve in a)O=a[Ve],a.hasOwnProperty(Ve)&&O!==void 0&&!l.hasOwnProperty(Ve)&&io(e,t,Ve,void 0,l,O);for(B in l)O=l[B],k=a[B],!l.hasOwnProperty(B)||O===k||O===void 0&&k===void 0||io(e,t,B,O,l,k);return}}for(var E in a)O=a[E],a.hasOwnProperty(E)&&O!=null&&!l.hasOwnProperty(E)&&He(e,t,E,null,l,O);for(Y in l)O=l[Y],k=a[Y],!l.hasOwnProperty(Y)||O===k||O==null&&k==null||He(e,t,Y,O,l,k)}var ro=null,co=null;function Ai(e){return e.nodeType===9?e:e.ownerDocument}function Uh(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Lh(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function oo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var uo=null;function xg(){var e=window.event;return e&&e.type==="popstate"?e===uo?!1:(uo=e,!0):(uo=null,!1)}var Bh=typeof setTimeout=="function"?setTimeout:void 0,yg=typeof clearTimeout=="function"?clearTimeout:void 0,qh=typeof Promise=="function"?Promise:void 0,bg=typeof queueMicrotask=="function"?queueMicrotask:typeof qh<"u"?function(e){return qh.resolve(null).then(e).catch(Sg)}:Bh;function Sg(e){setTimeout(function(){throw e})}function Ga(e){return e==="head"}function Hh(e,t){var a=t,l=0,i=0;do{var o=a.nextSibling;if(e.removeChild(a),o&&o.nodeType===8)if(a=o.data,a==="/$"){if(0<l&&8>l){a=l;var h=e.ownerDocument;if(a&1&&os(h.documentElement),a&2&&os(h.body),a&4)for(a=h.head,os(a),h=a.firstChild;h;){var y=h.nextSibling,w=h.nodeName;h[wn]||w==="SCRIPT"||w==="STYLE"||w==="LINK"&&h.rel.toLowerCase()==="stylesheet"||a.removeChild(h),h=y}}if(i===0){e.removeChild(o),vs(t);return}i--}else a==="$"||a==="$?"||a==="$!"?i++:l=a.charCodeAt(0)-48;else l=0;a=o}while(a);vs(t)}function fo(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":fo(a),gr(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function jg(e,t,a,l){for(;e.nodeType===1;){var i=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[wn])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(o=e.getAttribute("rel"),o==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(o!==i.rel||e.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute("title")!==(i.title==null?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(o=e.getAttribute("src"),(o!==(i.src==null?null:i.src)||e.getAttribute("type")!==(i.type==null?null:i.type)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&o&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var o=i.name==null?null:""+i.name;if(i.type==="hidden"&&e.getAttribute("name")===o)return e}else return e;if(e=Wt(e.nextSibling),e===null)break}return null}function Ng(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=Wt(e.nextSibling),e===null))return null;return e}function ho(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function wg(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Wt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var mo=null;function Vh(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function Gh(e,t,a){switch(t=Ai(a),e){case"html":if(e=t.documentElement,!e)throw Error(c(452));return e;case"head":if(e=t.head,!e)throw Error(c(453));return e;case"body":if(e=t.body,!e)throw Error(c(454));return e;default:throw Error(c(451))}}function os(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);gr(e)}var Yt=new Map,Yh=new Set;function zi(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var ba=Q.d;Q.d={f:_g,r:Rg,D:Mg,C:Tg,L:Eg,m:Cg,X:Ag,S:Dg,M:zg};function _g(){var e=ba.f(),t=wi();return e||t}function Rg(e){var t=Nl(e);t!==null&&t.tag===5&&t.type==="form"?of(t):ba.r(e)}var ln=typeof document>"u"?null:document;function Xh(e,t,a){var l=ln;if(l&&typeof t=="string"&&t){var i=Ut(t);i='link[rel="'+e+'"][href="'+i+'"]',typeof a=="string"&&(i+='[crossorigin="'+a+'"]'),Yh.has(i)||(Yh.add(i),e={rel:e,crossOrigin:a,href:t},l.querySelector(i)===null&&(t=l.createElement("link"),ht(t,"link",e),rt(t),l.head.appendChild(t)))}}function Mg(e){ba.D(e),Xh("dns-prefetch",e,null)}function Tg(e,t){ba.C(e,t),Xh("preconnect",e,t)}function Eg(e,t,a){ba.L(e,t,a);var l=ln;if(l&&e&&t){var i='link[rel="preload"][as="'+Ut(t)+'"]';t==="image"&&a&&a.imageSrcSet?(i+='[imagesrcset="'+Ut(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(i+='[imagesizes="'+Ut(a.imageSizes)+'"]')):i+='[href="'+Ut(e)+'"]';var o=i;switch(t){case"style":o=nn(e);break;case"script":o=sn(e)}Yt.has(o)||(e=x({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),Yt.set(o,e),l.querySelector(i)!==null||t==="style"&&l.querySelector(us(o))||t==="script"&&l.querySelector(ds(o))||(t=l.createElement("link"),ht(t,"link",e),rt(t),l.head.appendChild(t)))}}function Cg(e,t){ba.m(e,t);var a=ln;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",i='link[rel="modulepreload"][as="'+Ut(l)+'"][href="'+Ut(e)+'"]',o=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=sn(e)}if(!Yt.has(o)&&(e=x({rel:"modulepreload",href:e},t),Yt.set(o,e),a.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(ds(o)))return}l=a.createElement("link"),ht(l,"link",e),rt(l),a.head.appendChild(l)}}}function Dg(e,t,a){ba.S(e,t,a);var l=ln;if(l&&e){var i=wl(l).hoistableStyles,o=nn(e);t=t||"default";var h=i.get(o);if(!h){var y={loading:0,preload:null};if(h=l.querySelector(us(o)))y.loading=5;else{e=x({rel:"stylesheet",href:e,"data-precedence":t},a),(a=Yt.get(o))&&po(e,a);var w=h=l.createElement("link");rt(w),ht(w,"link",e),w._p=new Promise(function(z,B){w.onload=z,w.onerror=B}),w.addEventListener("load",function(){y.loading|=1}),w.addEventListener("error",function(){y.loading|=2}),y.loading|=4,Oi(h,t,l)}h={type:"stylesheet",instance:h,count:1,state:y},i.set(o,h)}}}function Ag(e,t){ba.X(e,t);var a=ln;if(a&&e){var l=wl(a).hoistableScripts,i=sn(e),o=l.get(i);o||(o=a.querySelector(ds(i)),o||(e=x({src:e,async:!0},t),(t=Yt.get(i))&&go(e,t),o=a.createElement("script"),rt(o),ht(o,"link",e),a.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},l.set(i,o))}}function zg(e,t){ba.M(e,t);var a=ln;if(a&&e){var l=wl(a).hoistableScripts,i=sn(e),o=l.get(i);o||(o=a.querySelector(ds(i)),o||(e=x({src:e,async:!0,type:"module"},t),(t=Yt.get(i))&&go(e,t),o=a.createElement("script"),rt(o),ht(o,"link",e),a.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},l.set(i,o))}}function Qh(e,t,a,l){var i=(i=I.current)?zi(i):null;if(!i)throw Error(c(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=nn(a.href),a=wl(i).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=nn(a.href);var o=wl(i).hoistableStyles,h=o.get(e);if(h||(i=i.ownerDocument||i,h={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},o.set(e,h),(o=i.querySelector(us(e)))&&!o._p&&(h.instance=o,h.state.loading=5),Yt.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Yt.set(e,a),o||Og(i,e,a,h.state))),t&&l===null)throw Error(c(528,""));return h}if(t&&l!==null)throw Error(c(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=sn(a),a=wl(i).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,e))}}function nn(e){return'href="'+Ut(e)+'"'}function us(e){return'link[rel="stylesheet"]['+e+"]"}function Zh(e){return x({},e,{"data-precedence":e.precedence,precedence:null})}function Og(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),ht(t,"link",a),rt(t),e.head.appendChild(t))}function sn(e){return'[src="'+Ut(e)+'"]'}function ds(e){return"script[async]"+e}function Kh(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+Ut(a.href)+'"]');if(l)return t.instance=l,rt(l),l;var i=x({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),rt(l),ht(l,"style",i),Oi(l,a.precedence,e),t.instance=l;case"stylesheet":i=nn(a.href);var o=e.querySelector(us(i));if(o)return t.state.loading|=4,t.instance=o,rt(o),o;l=Zh(a),(i=Yt.get(i))&&po(l,i),o=(e.ownerDocument||e).createElement("link"),rt(o);var h=o;return h._p=new Promise(function(y,w){h.onload=y,h.onerror=w}),ht(o,"link",l),t.state.loading|=4,Oi(o,a.precedence,e),t.instance=o;case"script":return o=sn(a.src),(i=e.querySelector(ds(o)))?(t.instance=i,rt(i),i):(l=a,(i=Yt.get(o))&&(l=x({},a),go(l,i)),e=e.ownerDocument||e,i=e.createElement("script"),rt(i),ht(i,"link",l),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(c(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,Oi(l,a.precedence,e));return t.instance}function Oi(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,o=i,h=0;h<l.length;h++){var y=l[h];if(y.dataset.precedence===t)o=y;else if(o!==i)break}o?o.parentNode.insertBefore(e,o.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function po(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function go(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var ki=null;function $h(e,t,a){if(ki===null){var l=new Map,i=ki=new Map;i.set(a,l)}else i=ki,l=i.get(a),l||(l=new Map,i.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),i=0;i<a.length;i++){var o=a[i];if(!(o[wn]||o[pt]||e==="link"&&o.getAttribute("rel")==="stylesheet")&&o.namespaceURI!=="http://www.w3.org/2000/svg"){var h=o.getAttribute(t)||"";h=e+h;var y=l.get(h);y?y.push(o):l.set(h,[o])}}return l}function Jh(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function kg(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Fh(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var fs=null;function Ug(){}function Lg(e,t,a){if(fs===null)throw Error(c(475));var l=fs;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var i=nn(a.href),o=e.querySelector(us(i));if(o){e=o._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=Ui.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=o,rt(o);return}o=e.ownerDocument||e,a=Zh(a),(i=Yt.get(i))&&po(a,i),o=o.createElement("link"),rt(o);var h=o;h._p=new Promise(function(y,w){h.onload=y,h.onerror=w}),ht(o,"link",a),t.instance=o}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=Ui.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function Bg(){if(fs===null)throw Error(c(475));var e=fs;return e.stylesheets&&e.count===0&&vo(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&vo(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function Ui(){if(this.count--,this.count===0){if(this.stylesheets)vo(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Li=null;function vo(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Li=new Map,t.forEach(qg,e),Li=null,Ui.call(e))}function qg(e,t){if(!(t.state.loading&4)){var a=Li.get(e);if(a)var l=a.get(null);else{a=new Map,Li.set(e,a);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<i.length;o++){var h=i[o];(h.nodeName==="LINK"||h.getAttribute("media")!=="not all")&&(a.set(h.dataset.precedence,h),l=h)}l&&a.set(null,l)}i=t.instance,h=i.getAttribute("data-precedence"),o=a.get(h)||l,o===l&&a.set(null,i),a.set(h,i),this.count++,l=Ui.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),o?o.parentNode.insertBefore(i,o.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var hs={$$typeof:q,Provider:null,Consumer:null,_currentValue:U,_currentValue2:U,_threadCount:0};function Hg(e,t,a,l,i,o,h,y){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=fr(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=fr(0),this.hiddenUpdates=fr(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=o,this.onRecoverableError=h,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=y,this.incompleteTransitions=new Map}function Ph(e,t,a,l,i,o,h,y,w,z,B,Y){return e=new Hg(e,t,a,h,y,w,z,Y),t=1,o===!0&&(t|=24),o=Tt(3,null,null,t),e.current=o,o.stateNode=e,t=Pr(),t.refCount++,e.pooledCache=t,t.refCount++,o.memoizedState={element:l,isDehydrated:a,cache:t},tc(o),e}function Wh(e){return e?(e=Ul,e):Ul}function Ih(e,t,a,l,i,o){i=Wh(i),l.context===null?l.context=i:l.pendingContext=i,l=Ea(t),l.payload={element:a},o=o===void 0?null:o,o!==null&&(l.callback=o),a=Ca(e,l,t),a!==null&&(zt(a,e,t),Yn(a,e,t))}function em(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function xo(e,t){em(e,t),(e=e.alternate)&&em(e,t)}function tm(e){if(e.tag===13){var t=kl(e,67108864);t!==null&&zt(t,e,67108864),xo(e,67108864)}}var Bi=!0;function Vg(e,t,a,l){var i=C.T;C.T=null;var o=Q.p;try{Q.p=2,yo(e,t,a,l)}finally{Q.p=o,C.T=i}}function Gg(e,t,a,l){var i=C.T;C.T=null;var o=Q.p;try{Q.p=8,yo(e,t,a,l)}finally{Q.p=o,C.T=i}}function yo(e,t,a,l){if(Bi){var i=bo(l);if(i===null)so(e,t,l,qi,a),lm(e,l);else if(Xg(i,e,t,a,l))l.stopPropagation();else if(lm(e,l),t&4&&-1<Yg.indexOf(e)){for(;i!==null;){var o=Nl(i);if(o!==null)switch(o.tag){case 3:if(o=o.stateNode,o.current.memoizedState.isDehydrated){var h=Ia(o.pendingLanes);if(h!==0){var y=o;for(y.pendingLanes|=2,y.entangledLanes|=2;h;){var w=1<<31-Rt(h);y.entanglements[1]|=w,h&=~w}na(o),(Le&6)===0&&(ji=mt()+500,is(0))}}break;case 13:y=kl(o,2),y!==null&&zt(y,o,2),wi(),xo(o,2)}if(o=bo(l),o===null&&so(e,t,l,qi,a),o===i)break;i=o}i!==null&&l.stopPropagation()}else so(e,t,l,null,a)}}function bo(e){return e=wr(e),So(e)}var qi=null;function So(e){if(qi=null,e=jl(e),e!==null){var t=m(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=f(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return qi=e,null}function am(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(yn()){case bl:return 2;case bn:return 8;case Pa:case Ge:return 32;case Fe:return 268435456;default:return 32}default:return 32}}var jo=!1,Ya=null,Xa=null,Qa=null,ms=new Map,ps=new Map,Za=[],Yg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function lm(e,t){switch(e){case"focusin":case"focusout":Ya=null;break;case"dragenter":case"dragleave":Xa=null;break;case"mouseover":case"mouseout":Qa=null;break;case"pointerover":case"pointerout":ms.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ps.delete(t.pointerId)}}function gs(e,t,a,l,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:o,targetContainers:[i]},t!==null&&(t=Nl(t),t!==null&&tm(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Xg(e,t,a,l,i){switch(t){case"focusin":return Ya=gs(Ya,e,t,a,l,i),!0;case"dragenter":return Xa=gs(Xa,e,t,a,l,i),!0;case"mouseover":return Qa=gs(Qa,e,t,a,l,i),!0;case"pointerover":var o=i.pointerId;return ms.set(o,gs(ms.get(o)||null,e,t,a,l,i)),!0;case"gotpointercapture":return o=i.pointerId,ps.set(o,gs(ps.get(o)||null,e,t,a,l,i)),!0}return!1}function nm(e){var t=jl(e.target);if(t!==null){var a=m(t);if(a!==null){if(t=a.tag,t===13){if(t=f(a),t!==null){e.blockedOn=t,Lp(e.priority,function(){if(a.tag===13){var l=At();l=hr(l);var i=kl(a,l);i!==null&&zt(i,a,l),xo(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Hi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=bo(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);Nr=l,a.target.dispatchEvent(l),Nr=null}else return t=Nl(a),t!==null&&tm(t),e.blockedOn=a,!1;t.shift()}return!0}function sm(e,t,a){Hi(e)&&a.delete(t)}function Qg(){jo=!1,Ya!==null&&Hi(Ya)&&(Ya=null),Xa!==null&&Hi(Xa)&&(Xa=null),Qa!==null&&Hi(Qa)&&(Qa=null),ms.forEach(sm),ps.forEach(sm)}function Vi(e,t){e.blockedOn===t&&(e.blockedOn=null,jo||(jo=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,Qg)))}var Gi=null;function im(e){Gi!==e&&(Gi=e,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){Gi===e&&(Gi=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],i=e[t+2];if(typeof l!="function"){if(So(l||a)===null)continue;break}var o=Nl(a);o!==null&&(e.splice(t,3),t-=3,bc(o,{pending:!0,data:i,method:a.method,action:l},l,i))}}))}function vs(e){function t(w){return Vi(w,e)}Ya!==null&&Vi(Ya,e),Xa!==null&&Vi(Xa,e),Qa!==null&&Vi(Qa,e),ms.forEach(t),ps.forEach(t);for(var a=0;a<Za.length;a++){var l=Za[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Za.length&&(a=Za[0],a.blockedOn===null);)nm(a),a.blockedOn===null&&Za.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var i=a[l],o=a[l+1],h=i[St]||null;if(typeof o=="function")h||im(a);else if(h){var y=null;if(o&&o.hasAttribute("formAction")){if(i=o,h=o[St]||null)y=h.formAction;else if(So(i)!==null)continue}else y=h.action;typeof y=="function"?a[l+1]=y:(a.splice(l,3),l-=3),im(a)}}}function No(e){this._internalRoot=e}Yi.prototype.render=No.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(c(409));var a=t.current,l=At();Ih(a,l,e,t,null,null)},Yi.prototype.unmount=No.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Ih(e.current,2,null,e,null,null),wi(),t[Sl]=null}};function Yi(e){this._internalRoot=e}Yi.prototype.unstable_scheduleHydration=function(e){if(e){var t=ju();e={blockedOn:null,target:e,priority:t};for(var a=0;a<Za.length&&t!==0&&t<Za[a].priority;a++);Za.splice(a,0,e),a===0&&nm(e)}};var rm=u.version;if(rm!=="19.1.0")throw Error(c(527,rm,"19.1.0"));Q.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(c(188)):(e=Object.keys(e).join(","),Error(c(268,e)));return e=p(t),e=e!==null?g(e):null,e=e===null?null:e.stateNode,e};var Zg={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:C,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Xi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Xi.isDisabled&&Xi.supportsFiber)try{Wa=Xi.inject(Zg),yt=Xi}catch{}}return ys.createRoot=function(e,t){if(!d(e))throw Error(c(299));var a=!1,l="",i=Nf,o=wf,h=_f,y=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(i=t.onUncaughtError),t.onCaughtError!==void 0&&(o=t.onCaughtError),t.onRecoverableError!==void 0&&(h=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(y=t.unstable_transitionCallbacks)),t=Ph(e,1,!1,null,null,a,l,i,o,h,y,null),e[Sl]=t.current,no(e),new No(t)},ys.hydrateRoot=function(e,t,a){if(!d(e))throw Error(c(299));var l=!1,i="",o=Nf,h=wf,y=_f,w=null,z=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(i=a.identifierPrefix),a.onUncaughtError!==void 0&&(o=a.onUncaughtError),a.onCaughtError!==void 0&&(h=a.onCaughtError),a.onRecoverableError!==void 0&&(y=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(w=a.unstable_transitionCallbacks),a.formState!==void 0&&(z=a.formState)),t=Ph(e,1,!0,t,a??null,l,i,o,h,y,w,z),t.context=Wh(null),a=t.current,l=At(),l=hr(l),i=Ea(l),i.callback=null,Ca(a,i,l),a=l,t.current.lanes=a,Nn(t,a),na(t),e[Sl]=t.current,no(e),new Yi(t)},ys.version="19.1.0",ys}var vm;function tv(){if(vm)return Ro.exports;vm=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(u){console.error(u)}}return n(),Ro.exports=ev(),Ro.exports}var av=tv();const lv=$m(av);var nv="Invariant failed";function Na(n,u){if(!n)throw new Error(nv)}const un=new WeakMap,Fi=new WeakMap,Wi={current:[]};let Co=!1,ws=0;const Ss=new Set,Qi=new Map;function Fm(n){const u=Array.from(n).sort((r,c)=>r instanceof dn&&r.options.deps.includes(c)?1:c instanceof dn&&c.options.deps.includes(r)?-1:0);for(const r of u){if(Wi.current.includes(r))continue;Wi.current.push(r),r.recompute();const c=Fi.get(r);if(c)for(const d of c){const m=un.get(d);m&&Fm(m)}}}function sv(n){n.listeners.forEach(u=>u({prevVal:n.prevState,currentVal:n.state}))}function iv(n){n.listeners.forEach(u=>u({prevVal:n.prevState,currentVal:n.state}))}function Pm(n){if(ws>0&&!Qi.has(n)&&Qi.set(n,n.prevState),Ss.add(n),!(ws>0)&&!Co)try{for(Co=!0;Ss.size>0;){const u=Array.from(Ss);Ss.clear();for(const r of u){const c=Qi.get(r)??r.prevState;r.prevState=c,sv(r)}for(const r of u){const c=un.get(r);c&&(Wi.current.push(r),Fm(c))}for(const r of u){const c=un.get(r);if(c)for(const d of c)iv(d)}}}finally{Co=!1,Wi.current=[],Qi.clear()}}function xm(n){ws++;try{n()}finally{if(ws--,ws===0){const u=Array.from(Ss)[0];u&&Pm(u)}}}function rv(n){return typeof n=="function"}class Zo{constructor(u,r){this.listeners=new Set,this.subscribe=c=>{var d,m;this.listeners.add(c);const f=(m=(d=this.options)==null?void 0:d.onSubscribe)==null?void 0:m.call(d,c,this);return()=>{this.listeners.delete(c),f?.()}},this.prevState=u,this.state=u,this.options=r}setState(u){var r,c,d;this.prevState=this.state,(r=this.options)!=null&&r.updateFn?this.state=this.options.updateFn(this.prevState)(u):rv(u)?this.state=u(this.prevState):this.state=u,(d=(c=this.options)==null?void 0:c.onUpdate)==null||d.call(c),Pm(this)}}class dn{constructor(u){this.listeners=new Set,this._subscriptions=[],this.lastSeenDepValues=[],this.getDepVals=()=>{const r=[],c=[];for(const d of this.options.deps)r.push(d.prevState),c.push(d.state);return this.lastSeenDepValues=c,{prevDepVals:r,currDepVals:c,prevVal:this.prevState??void 0}},this.recompute=()=>{var r,c;this.prevState=this.state;const{prevDepVals:d,currDepVals:m,prevVal:f}=this.getDepVals();this.state=this.options.fn({prevDepVals:d,currDepVals:m,prevVal:f}),(c=(r=this.options).onUpdate)==null||c.call(r)},this.checkIfRecalculationNeededDeeply=()=>{for(const m of this.options.deps)m instanceof dn&&m.checkIfRecalculationNeededDeeply();let r=!1;const c=this.lastSeenDepValues,{currDepVals:d}=this.getDepVals();for(let m=0;m<d.length;m++)if(d[m]!==c[m]){r=!0;break}r&&this.recompute()},this.mount=()=>(this.registerOnGraph(),this.checkIfRecalculationNeededDeeply(),()=>{this.unregisterFromGraph();for(const r of this._subscriptions)r()}),this.subscribe=r=>{var c,d;this.listeners.add(r);const m=(d=(c=this.options).onSubscribe)==null?void 0:d.call(c,r,this);return()=>{this.listeners.delete(r),m?.()}},this.options=u,this.state=u.fn({prevDepVals:void 0,prevVal:void 0,currDepVals:this.getDepVals().currDepVals})}registerOnGraph(u=this.options.deps){for(const r of u)if(r instanceof dn)r.registerOnGraph(),this.registerOnGraph(r.options.deps);else if(r instanceof Zo){let c=un.get(r);c||(c=new Set,un.set(r,c)),c.add(this);let d=Fi.get(this);d||(d=new Set,Fi.set(this,d)),d.add(r)}}unregisterFromGraph(u=this.options.deps){for(const r of u)if(r instanceof dn)this.unregisterFromGraph(r.options.deps);else if(r instanceof Zo){const c=un.get(r);c&&c.delete(this);const d=Fi.get(this);d&&d.delete(r)}}}const Fa="__TSR_index",ym="popstate",bm="beforeunload";function Wm(n){let u=n.getLocation();const r=new Set,c=f=>{u=n.getLocation(),r.forEach(v=>v({location:u,action:f}))},d=f=>{n.notifyOnIndexChange??!0?c(f):u=n.getLocation()},m=async({task:f,navigateOpts:v,...p})=>{var g,x;if(v?.ignoreBlocker??!1){f();return}const j=((g=n.getBlockers)==null?void 0:g.call(n))??[],T=p.type==="PUSH"||p.type==="REPLACE";if(typeof document<"u"&&j.length&&T)for(const R of j){const N=_s(p.path,p.state);if(await R.blockerFn({currentLocation:u,nextLocation:N,action:p.type})){(x=n.onBlocked)==null||x.call(n);return}}f()};return{get location(){return u},get length(){return n.getLength()},subscribers:r,subscribe:f=>(r.add(f),()=>{r.delete(f)}),push:(f,v,p)=>{const g=u.state[Fa];v=Ko(g+1,v),m({task:()=>{n.pushState(f,v),c({type:"PUSH"})},navigateOpts:p,type:"PUSH",path:f,state:v})},replace:(f,v,p)=>{const g=u.state[Fa];v=Ko(g,v),m({task:()=>{n.replaceState(f,v),c({type:"REPLACE"})},navigateOpts:p,type:"REPLACE",path:f,state:v})},go:(f,v)=>{m({task:()=>{n.go(f),d({type:"GO",index:f})},navigateOpts:v,type:"GO"})},back:f=>{m({task:()=>{n.back(f?.ignoreBlocker??!1),d({type:"BACK"})},navigateOpts:f,type:"BACK"})},forward:f=>{m({task:()=>{n.forward(f?.ignoreBlocker??!1),d({type:"FORWARD"})},navigateOpts:f,type:"FORWARD"})},canGoBack:()=>u.state[Fa]!==0,createHref:f=>n.createHref(f),block:f=>{var v;if(!n.setBlockers)return()=>{};const p=((v=n.getBlockers)==null?void 0:v.call(n))??[];return n.setBlockers([...p,f]),()=>{var g,x;const b=((g=n.getBlockers)==null?void 0:g.call(n))??[];(x=n.setBlockers)==null||x.call(n,b.filter(j=>j!==f))}},flush:()=>{var f;return(f=n.flush)==null?void 0:f.call(n)},destroy:()=>{var f;return(f=n.destroy)==null?void 0:f.call(n)},notify:c}}function Ko(n,u){u||(u={});const r=lu();return{...u,key:r,__TSR_key:r,[Fa]:n}}function cv(n){var u,r;const c=typeof document<"u"?window:void 0,d=c.history.pushState,m=c.history.replaceState;let f=[];const v=()=>f,p=te=>f=te,g=te=>te,x=()=>_s(`${c.location.pathname}${c.location.search}${c.location.hash}`,c.history.state);if(!((u=c.history.state)!=null&&u.__TSR_key)&&!((r=c.history.state)!=null&&r.key)){const te=lu();c.history.replaceState({[Fa]:0,key:te,__TSR_key:te},"")}let b=x(),j,T=!1,R=!1,N=!1,S=!1;const A=()=>b;let X,q;const P=()=>{X&&(oe._ignoreSubscribers=!0,(X.isPush?c.history.pushState:c.history.replaceState)(X.state,"",X.href),oe._ignoreSubscribers=!1,X=void 0,q=void 0,j=void 0)},ee=(te,$,W)=>{const me=g($);q||(j=b),b=_s($,W),X={href:me,state:W,isPush:X?.isPush||te==="push"},q||(q=Promise.resolve().then(()=>P()))},re=te=>{b=x(),oe.notify({type:te})},K=async()=>{if(R){R=!1;return}const te=x(),$=te.state[Fa]-b.state[Fa],W=$===1,me=$===-1,be=!W&&!me||T;T=!1;const ge=be?"GO":me?"BACK":"FORWARD",C=be?{type:"GO",index:$}:{type:me?"BACK":"FORWARD"};if(N)N=!1;else{const Q=v();if(typeof document<"u"&&Q.length){for(const U of Q)if(await U.blockerFn({currentLocation:b,nextLocation:te,action:ge})){R=!0,c.history.go(1),oe.notify(C);return}}}b=x(),oe.notify(C)},V=te=>{if(S){S=!1;return}let $=!1;const W=v();if(typeof document<"u"&&W.length)for(const me of W){const be=me.enableBeforeUnload??!0;if(be===!0){$=!0;break}if(typeof be=="function"&&be()===!0){$=!0;break}}if($)return te.preventDefault(),te.returnValue=""},oe=Wm({getLocation:A,getLength:()=>c.history.length,pushState:(te,$)=>ee("push",te,$),replaceState:(te,$)=>ee("replace",te,$),back:te=>(te&&(N=!0),S=!0,c.history.back()),forward:te=>{te&&(N=!0),S=!0,c.history.forward()},go:te=>{T=!0,c.history.go(te)},createHref:te=>g(te),flush:P,destroy:()=>{c.history.pushState=d,c.history.replaceState=m,c.removeEventListener(bm,V,{capture:!0}),c.removeEventListener(ym,K)},onBlocked:()=>{j&&b!==j&&(b=j)},getBlockers:v,setBlockers:p,notifyOnIndexChange:!1});return c.addEventListener(bm,V,{capture:!0}),c.addEventListener(ym,K),c.history.pushState=function(...te){const $=d.apply(c.history,te);return oe._ignoreSubscribers||re("PUSH"),$},c.history.replaceState=function(...te){const $=m.apply(c.history,te);return oe._ignoreSubscribers||re("REPLACE"),$},oe}function ov(n={initialEntries:["/"]}){const u=n.initialEntries;let r=n.initialIndex?Math.min(Math.max(n.initialIndex,0),u.length-1):u.length-1;const c=u.map((m,f)=>Ko(f,void 0));return Wm({getLocation:()=>_s(u[r],c[r]),getLength:()=>u.length,pushState:(m,f)=>{r<u.length-1&&(u.splice(r+1),c.splice(r+1)),c.push(f),u.push(m),r=Math.max(u.length-1,0)},replaceState:(m,f)=>{c[r]=f,u[r]=m},back:()=>{r=Math.max(r-1,0)},forward:()=>{r=Math.min(r+1,u.length-1)},go:m=>{r=Math.min(Math.max(r+m,0),u.length-1)},createHref:m=>m})}function _s(n,u){const r=n.indexOf("#"),c=n.indexOf("?"),d=lu();return{href:n,pathname:n.substring(0,r>0?c>0?Math.min(r,c):r:c>0?c:n.length),hash:r>-1?n.substring(r):"",search:c>-1?n.slice(c,r===-1?void 0:r):"",state:u||{[Fa]:0,key:d,__TSR_key:d}}}function lu(){return(Math.random()+1).toString(36).substring(7)}function $o(n){return n[n.length-1]}function uv(n){return typeof n=="function"}function xl(n,u){return uv(n)?n(u):n}function Ii(n,u){return u.reduce((r,c)=>(r[c]=n[c],r),{})}function Xt(n,u){if(n===u)return n;const r=u,c=Nm(n)&&Nm(r);if(c||Sm(n)&&Sm(r)){const d=c?n:Object.keys(n).concat(Object.getOwnPropertySymbols(n)),m=d.length,f=c?r:Object.keys(r).concat(Object.getOwnPropertySymbols(r)),v=f.length,p=c?[]:{};let g=0;for(let x=0;x<v;x++){const b=c?x:f[x];(!c&&d.includes(b)||c)&&n[b]===void 0&&r[b]===void 0?(p[b]=void 0,g++):(p[b]=Xt(n[b],r[b]),p[b]===n[b]&&n[b]!==void 0&&g++)}return m===v&&g===m?n:p}return r}function Sm(n){return Jo(n)&&Object.getOwnPropertyNames(n).length===Object.keys(n).length}function Jo(n){if(!jm(n))return!1;const u=n.constructor;if(typeof u>"u")return!0;const r=u.prototype;return!(!jm(r)||!r.hasOwnProperty("isPrototypeOf"))}function jm(n){return Object.prototype.toString.call(n)==="[object Object]"}function Nm(n){return Array.isArray(n)&&n.length===Object.keys(n).length}function wm(n,u){let r=Object.keys(n);return u&&(r=r.filter(c=>n[c]!==void 0)),r}function fn(n,u,r){if(n===u)return!0;if(typeof n!=typeof u)return!1;if(Jo(n)&&Jo(u)){const c=r?.ignoreUndefined??!0,d=wm(n,c),m=wm(u,c);return!r?.partial&&d.length!==m.length?!1:m.every(f=>fn(n[f],u[f],r))}return Array.isArray(n)&&Array.isArray(u)?n.length!==u.length?!1:!n.some((c,d)=>!fn(c,u[d],r)):!1}function cn(n){let u,r;const c=new Promise((d,m)=>{u=d,r=m});return c.status="pending",c.resolve=d=>{c.status="resolved",c.value=d,u(d),n?.(d)},c.reject=d=>{c.status="rejected",r(d)},c}function ja(n){return sr(n.filter(u=>u!==void 0).join("/"))}function sr(n){return n.replace(/\/{2,}/g,"/")}function nu(n){return n==="/"?n:n.replace(/^\/{1,}/,"")}function hn(n){return n==="/"?n:n.replace(/\/{1,}$/,"")}function Do(n){return hn(nu(n))}function er(n,u){return n?.endsWith("/")&&n!=="/"&&n!==`${u}/`?n.slice(0,-1):n}function dv(n,u,r){return er(n,r)===er(u,r)}function fv({basepath:n,base:u,to:r,trailingSlash:c="never",caseSensitive:d}){var m,f;u=tr(n,u,d),r=tr(n,r,d);let v=mn(u);const p=mn(r);v.length>1&&((m=$o(v))==null?void 0:m.value)==="/"&&v.pop(),p.forEach((b,j)=>{b.value==="/"?j?j===p.length-1&&v.push(b):v=[b]:b.value===".."?v.pop():b.value==="."||v.push(b)}),v.length>1&&(((f=$o(v))==null?void 0:f.value)==="/"?c==="never"&&v.pop():c==="always"&&v.push({type:"pathname",value:"/"}));const g=v.map(b=>{if(b.type==="param"){const j=b.value.substring(1);if(b.prefixSegment&&b.suffixSegment)return`${b.prefixSegment}{$${j}}${b.suffixSegment}`;if(b.prefixSegment)return`${b.prefixSegment}{$${j}}`;if(b.suffixSegment)return`{$${j}}${b.suffixSegment}`}if(b.type==="wildcard"){if(b.prefixSegment&&b.suffixSegment)return`${b.prefixSegment}{$}${b.suffixSegment}`;if(b.prefixSegment)return`${b.prefixSegment}{$}`;if(b.suffixSegment)return`{$}${b.suffixSegment}`}return b.value}),x=ja([n,...g]);return sr(x)}const hv=/^\$.{1,}$/,mv=/^(.*?)\{(\$[a-zA-Z_$][a-zA-Z0-9_$]*)\}(.*)$/,pv=/^\$$/,gv=/^(.*?)\{\$\}(.*)$/;function mn(n){if(!n)return[];n=sr(n);const u=[];if(n.slice(0,1)==="/"&&(n=n.substring(1),u.push({type:"pathname",value:"/"})),!n)return u;const r=n.split("/").filter(Boolean);return u.push(...r.map(c=>{const d=c.match(gv);if(d){const f=d[1],v=d[2];return{type:"wildcard",value:"$",prefixSegment:f||void 0,suffixSegment:v||void 0}}const m=c.match(mv);if(m){const f=m[1],v=m[2],p=m[3];return{type:"param",value:""+v,prefixSegment:f||void 0,suffixSegment:p||void 0}}return hv.test(c)?{type:"param",value:"$"+c.substring(1),prefixSegment:void 0,suffixSegment:void 0}:pv.test(c)?{type:"wildcard",value:"$",prefixSegment:void 0,suffixSegment:void 0}:{type:"pathname",value:c.includes("%25")?c.split("%25").map(f=>decodeURI(f)).join("%25"):decodeURI(c)}})),n.slice(-1)==="/"&&(n=n.substring(1),u.push({type:"pathname",value:"/"})),u}function Ao({path:n,params:u,leaveWildcards:r,leaveParams:c,decodeCharMap:d}){const m=mn(n);function f(x){const b=u[x],j=typeof b=="string";return["*","_splat"].includes(x)?j?encodeURI(b):b:j?vv(b,d):b}let v=!1;const p={},g=ja(m.map(x=>{if(x.type==="wildcard"){p._splat=u._splat;const b=x.prefixSegment||"",j=x.suffixSegment||"";if(!("_splat"in u))return v=!0,r?`${b}${x.value}${j}`:b||j?`${b}${j}`:void 0;const T=f("_splat");return r?`${b}${x.value}${T??""}${j}`:`${b}${T}${j}`}if(x.type==="param"){const b=x.value.substring(1);!v&&!(b in u)&&(v=!0),p[b]=u[b];const j=x.prefixSegment||"",T=x.suffixSegment||"";if(c){const R=f(x.value);return`${j}${x.value}${R??""}${T}`}return`${j}${f(b)??"undefined"}${T}`}return x.value}));return{usedParams:p,interpolatedPath:g,isMissingParams:v}}function vv(n,u){let r=encodeURIComponent(n);if(u)for(const[c,d]of u)r=r.replaceAll(c,d);return r}function Fo(n,u,r){const c=xv(n,u,r);if(!(r.to&&!c))return c??{}}function tr(n,u,r=!1){const c=r?n:n.toLowerCase(),d=r?u:u.toLowerCase();switch(!0){case c==="/":return u;case d===c:return"";case u.length<n.length:return u;case d[c.length]!=="/":return u;case d.startsWith(c):return u.slice(n.length);default:return u}}function xv(n,u,r){if(n!=="/"&&!u.startsWith(n))return;u=tr(n,u,r.caseSensitive);const c=tr(n,`${r.to??"$"}`,r.caseSensitive),d=mn(u),m=mn(c);u.startsWith("/")||d.unshift({type:"pathname",value:"/"}),c.startsWith("/")||m.unshift({type:"pathname",value:"/"});const f={};return(()=>{var p;for(let g=0;g<Math.max(d.length,m.length);g++){const x=d[g],b=m[g],j=g>=d.length-1,T=g>=m.length-1;if(b){if(b.type==="wildcard"){const R=d.slice(g);let N;if(b.prefixSegment||b.suffixSegment){if(!x)return!1;const S=b.prefixSegment||"",A=b.suffixSegment||"",X=x.value;if("prefixSegment"in b&&!X.startsWith(S)||"suffixSegment"in b&&!((p=d[d.length-1])!=null&&p.value.endsWith(A)))return!1;let q=decodeURI(ja(R.map(P=>P.value)));S&&q.startsWith(S)&&(q=q.slice(S.length)),A&&q.endsWith(A)&&(q=q.slice(0,q.length-A.length)),N=q}else N=decodeURI(ja(R.map(S=>S.value)));return f["*"]=N,f._splat=N,!0}if(b.type==="pathname"){if(b.value==="/"&&!x?.value)return!0;if(x){if(r.caseSensitive){if(b.value!==x.value)return!1}else if(b.value.toLowerCase()!==x.value.toLowerCase())return!1}}if(!x)return!1;if(b.type==="param"){if(x.value==="/")return!1;let R;if(b.prefixSegment||b.suffixSegment){const N=b.prefixSegment||"",S=b.suffixSegment||"",A=x.value;if(N&&!A.startsWith(N)||S&&!A.endsWith(S))return!1;let X=A;N&&X.startsWith(N)&&(X=X.slice(N.length)),S&&X.endsWith(S)&&(X=X.slice(0,X.length-S.length)),R=decodeURIComponent(X)}else R=decodeURIComponent(x.value);f[b.value.substring(1)]=R}}if(!j&&T)return f["**"]=ja(d.slice(g+1).map(R=>R.value)),!!r.fuzzy&&b?.value!=="/"}return!0})()?f:void 0}function Qt(n){return!!n?.isNotFound}function yv(){try{if(typeof window<"u"&&typeof window.sessionStorage=="object")return window.sessionStorage}catch{return}}const ar="tsr-scroll-restoration-v1_3",bv=(n,u)=>{let r;return(...c)=>{r||(r=setTimeout(()=>{n(...c),r=null},u))}};function Sv(){const n=yv();if(!n)return;const u=n.getItem(ar);let r=u?JSON.parse(u):{};return{state:r,set:c=>(r=xl(c,r)||r,n.setItem(ar,JSON.stringify(r)))}}const zo=Sv(),Po=n=>n.state.__TSR_key||n.href;function jv(n){const u=[];let r;for(;r=n.parentNode;)u.unshift(`${n.tagName}:nth-child(${[].indexOf.call(r.children,n)+1})`),n=r;return`${u.join(" > ")}`.toLowerCase()}let lr=!1;function Im(n,u,r,c,d){var m;let f;try{f=JSON.parse(sessionStorage.getItem(n)||"{}")}catch(g){console.error(g);return}const v=u||((m=window.history.state)==null?void 0:m.key),p=f[v];lr=!0,(()=>{if(c&&p){for(const x in p){const b=p[x];if(x==="window")window.scrollTo({top:b.scrollY,left:b.scrollX,behavior:r});else if(x){const j=document.querySelector(x);j&&(j.scrollLeft=b.scrollX,j.scrollTop=b.scrollY)}}return}const g=window.location.hash.split("#")[1];if(g){const x=(window.history.state||{}).__hashScrollIntoViewOptions??!0;if(x){const b=document.getElementById(g);b&&b.scrollIntoView(x)}return}["window",...d?.filter(x=>x!=="window")??[]].forEach(x=>{const b=x==="window"?window:typeof x=="function"?x():document.querySelector(x);b&&b.scrollTo({top:0,left:0,behavior:r})})})(),lr=!1}function Nv(n,u){if(zo===void 0||((n.options.scrollRestoration??!1)&&(n.isScrollRestoring=!0),typeof document>"u"||n.isScrollRestorationSetup))return;n.isScrollRestorationSetup=!0,lr=!1;const c=n.options.getScrollRestorationKey||Po;window.history.scrollRestoration="manual";const d=m=>{if(lr||!n.isScrollRestoring)return;let f="";if(m.target===document||m.target===window)f="window";else{const p=m.target.getAttribute("data-scroll-restoration-id");p?f=`[data-scroll-restoration-id="${p}"]`:f=jv(m.target)}const v=c(n.state.location);zo.set(p=>{const g=p[v]=p[v]||{},x=g[f]=g[f]||{};if(f==="window")x.scrollX=window.scrollX||0,x.scrollY=window.scrollY||0;else if(f){const b=document.querySelector(f);b&&(x.scrollX=b.scrollLeft||0,x.scrollY=b.scrollTop||0)}return p})};typeof document<"u"&&document.addEventListener("scroll",bv(d,100),!0),n.subscribe("onRendered",m=>{const f=c(m.toLocation);if(!n.resetNextScroll){n.resetNextScroll=!0;return}Im(ar,f,n.options.scrollRestorationBehavior||void 0,n.isScrollRestoring||void 0,n.options.scrollToTopSelectors||void 0),n.isScrollRestoring&&zo.set(v=>(v[f]=v[f]||{},v))})}function wv(n){if(typeof document<"u"&&document.querySelector){const u=n.state.location.state.__hashScrollIntoViewOptions??!0;if(u&&n.state.location.hash!==""){const r=document.getElementById(n.state.location.hash);r&&r.scrollIntoView(u)}}}function _v(n,u){const r=Object.entries(n).flatMap(([d,m])=>Array.isArray(m)?m.map(f=>[d,String(f)]):[[d,String(m)]]);return""+new URLSearchParams(r).toString()}function Oo(n){return n?n==="false"?!1:n==="true"?!0:+n*0===0&&+n+""===n?+n:n:""}function Rv(n,u){const r=n;return[...new URLSearchParams(r).entries()].reduce((m,[f,v])=>{const p=m[f];return p==null?m[f]=Oo(v):m[f]=Array.isArray(p)?[...p,Oo(v)]:[p,Oo(v)],m},{})}const Mv=Ev(JSON.parse),Tv=Cv(JSON.stringify,JSON.parse);function Ev(n){return u=>{u.substring(0,1)==="?"&&(u=u.substring(1));const r=Rv(u);for(const c in r){const d=r[c];if(typeof d=="string")try{r[c]=n(d)}catch{}}return r}}function Cv(n,u){function r(c){if(typeof c=="object"&&c!==null)try{return n(c)}catch{}else if(typeof c=="string"&&typeof u=="function")try{return u(c),n(c)}catch{}return c}return c=>{c={...c},Object.keys(c).forEach(m=>{const f=c[m];typeof f>"u"||f===void 0?delete c[m]:c[m]=r(f)});const d=_v(c).toString();return d?`?${d}`:""}}const Zt="__root__";function Dv(n){if(n.statusCode=n.statusCode||n.code||307,!n.reloadDocument)try{new URL(`${n.href}`),n.reloadDocument=!0}catch{}const u=new Headers(n.headers||{});n.href&&u.get("Location")===null&&u.set("Location",n.href);const r=new Response(null,{status:n.statusCode,headers:u});if(r.options=n,n.throw)throw r;return r}function sa(n){return n instanceof Response&&!!n.options}function yl(n){const u=n.resolvedLocation,r=n.location,c=u?.pathname!==r.pathname,d=u?.href!==r.href,m=u?.hash!==r.hash;return{fromLocation:u,toLocation:r,pathChanged:c,hrefChanged:d,hashChanged:m}}class Av{constructor(u){this.tempLocationKey=`${Math.round(Math.random()*1e7)}`,this.resetNextScroll=!0,this.shouldViewTransition=void 0,this.isViewTransitionTypesSupported=void 0,this.subscribers=new Set,this.isScrollRestoring=!1,this.isScrollRestorationSetup=!1,this.startTransition=r=>r(),this.update=r=>{var c;r.notFoundRoute&&console.warn("The notFoundRoute API is deprecated and will be removed in the next major version. See https://tanstack.com/router/v1/docs/framework/react/guide/not-found-errors#migrating-from-notfoundroute for more info.");const d=this.options;this.options={...this.options,...r},this.isServer=this.options.isServer??typeof document>"u",this.pathParamsDecodeCharMap=this.options.pathParamsAllowedCharacters?new Map(this.options.pathParamsAllowedCharacters.map(m=>[encodeURIComponent(m),m])):void 0,(!this.basepath||r.basepath&&r.basepath!==d.basepath)&&(r.basepath===void 0||r.basepath===""||r.basepath==="/"?this.basepath="/":this.basepath=`/${Do(r.basepath)}`),(!this.history||this.options.history&&this.options.history!==this.history)&&(this.history=this.options.history??(this.isServer?ov({initialEntries:[this.basepath||"/"]}):cv()),this.latestLocation=this.parseLocation()),this.options.routeTree!==this.routeTree&&(this.routeTree=this.options.routeTree,this.buildRouteTree()),this.__store||(this.__store=new Zo(Ov(this.latestLocation),{onUpdate:()=>{this.__store.state={...this.state,cachedMatches:this.state.cachedMatches.filter(m=>!["redirected"].includes(m.status))}}}),Nv(this)),typeof window<"u"&&"CSS"in window&&typeof((c=window.CSS)==null?void 0:c.supports)=="function"&&(this.isViewTransitionTypesSupported=window.CSS.supports("selector(:active-view-transition-type(a)"))},this.buildRouteTree=()=>{const{routesById:r,routesByPath:c,flatRoutes:d}=kv({routeTree:this.routeTree,initRoute:(f,v)=>{f.init({originalIndex:v})}});this.routesById=r,this.routesByPath=c,this.flatRoutes=d;const m=this.options.notFoundRoute;m&&(m.init({originalIndex:99999999999}),this.routesById[m.id]=m)},this.subscribe=(r,c)=>{const d={eventType:r,fn:c};return this.subscribers.add(d),()=>{this.subscribers.delete(d)}},this.emit=r=>{this.subscribers.forEach(c=>{c.eventType===r.type&&c.fn(r)})},this.parseLocation=(r,c)=>{const d=({pathname:p,search:g,hash:x,state:b})=>{const j=this.options.parseSearch(g),T=this.options.stringifySearch(j);return{pathname:p,searchStr:T,search:Xt(r?.search,j),hash:x.split("#").reverse()[0]??"",href:`${p}${T}${x}`,state:Xt(r?.state,b)}},m=d(c??this.history.location),{__tempLocation:f,__tempKey:v}=m.state;if(f&&(!v||v===this.tempLocationKey)){const p=d(f);return p.state.key=m.state.key,p.state.__TSR_key=m.state.__TSR_key,delete p.state.__tempLocation,{...p,maskedLocation:m}}return m},this.resolvePathWithBase=(r,c)=>fv({basepath:this.basepath,base:r,to:sr(c),trailingSlash:this.options.trailingSlash,caseSensitive:this.options.caseSensitive}),this.matchRoutes=(r,c,d)=>typeof r=="string"?this.matchRoutesInternal({pathname:r,search:c},d):this.matchRoutesInternal(r,c),this.getMatchedRoutes=(r,c)=>Uv({pathname:r,routePathname:c,basepath:this.basepath,caseSensitive:this.options.caseSensitive,routesByPath:this.routesByPath,routesById:this.routesById,flatRoutes:this.flatRoutes}),this.cancelMatch=r=>{const c=this.getMatch(r);c&&(c.abortController.abort(),this.updateMatch(r,d=>(clearTimeout(d.pendingTimeout),{...d,pendingTimeout:void 0})))},this.cancelMatches=()=>{var r;(r=this.state.pendingMatches)==null||r.forEach(c=>{this.cancelMatch(c.id)})},this.buildLocation=r=>{const c=(m={})=>{var f;const v=m._fromLocation||this.latestLocation,p=this.matchRoutes(v,{_buildLocation:!0}),g=$o(p);let x=g.fullPath;const b=m.to?this.resolvePathWithBase(x,`${m.to}`):this.resolvePathWithBase(x,"."),j=!!m.to&&!this.comparePaths(m.to.toString(),x)&&!this.comparePaths(b,x);m.unsafeRelative==="path"?x=v.pathname:j&&m.from&&(x=m.from);const T=g.search,R={...g.params},N=m.to?this.resolvePathWithBase(x,`${m.to}`):this.resolvePathWithBase(x,".");let S=(m.params??!0)===!0?R:{...R,...xl(m.params,R)};const A=this.matchRoutes(N,{},{_buildLocation:!0}).map(V=>this.looseRoutesById[V.routeId]);Object.keys(S).length>0&&A.map(V=>{var oe;return((oe=V.options.params)==null?void 0:oe.stringify)??V.options.stringifyParams}).filter(Boolean).forEach(V=>{S={...S,...V(S)}});const X=Ao({path:N,params:S??{},leaveWildcards:!1,leaveParams:r.leaveParams,decodeCharMap:this.pathParamsDecodeCharMap}).interpolatedPath;let q=T;if(r._includeValidateSearch&&((f=this.options.search)!=null&&f.strict)){let V={};A.forEach(oe=>{try{oe.options.validateSearch&&(V={...V,...Wo(oe.options.validateSearch,{...V,...q})??{}})}catch{}}),q=V}q=Lv({search:q,dest:m,destRoutes:A,_includeValidateSearch:r._includeValidateSearch}),q=Xt(T,q);const P=this.options.stringifySearch(q),ee=m.hash===!0?v.hash:m.hash?xl(m.hash,v.hash):void 0,re=ee?`#${ee}`:"";let K=m.state===!0?v.state:m.state?xl(m.state,v.state):{};return K=Xt(v.state,K),{pathname:X,search:q,searchStr:P,state:K,hash:ee??"",href:`${X}${P}${re}`,unmaskOnReload:m.unmaskOnReload}},d=(m={},f)=>{var v;const p=c(m);let g=f?c(f):void 0;if(!g){let x={};const b=(v=this.options.routeMasks)==null?void 0:v.find(j=>{const T=Fo(this.basepath,p.pathname,{to:j.from,caseSensitive:!1,fuzzy:!1});return T?(x=T,!0):!1});if(b){const{from:j,...T}=b;f={...Ii(r,["from"]),...T,params:x},g=c(f)}}if(g){const x=c(f);p.maskedLocation=x}return p};return r.mask?d(r,{...Ii(r,["from"]),...r.mask}):d(r)},this.commitLocation=({viewTransition:r,ignoreBlocker:c,...d})=>{const m=()=>{const p=["key","__TSR_key","__TSR_index","__hashScrollIntoViewOptions"];p.forEach(x=>{d.state[x]=this.latestLocation.state[x]});const g=fn(d.state,this.latestLocation.state);return p.forEach(x=>{delete d.state[x]}),g},f=this.latestLocation.href===d.href,v=this.commitLocationPromise;if(this.commitLocationPromise=cn(()=>{v?.resolve()}),f&&m())this.load();else{let{maskedLocation:p,hashScrollIntoView:g,...x}=d;p&&(x={...p,state:{...p.state,__tempKey:void 0,__tempLocation:{...x,search:x.searchStr,state:{...x.state,__tempKey:void 0,__tempLocation:void 0,__TSR_key:void 0,key:void 0}}}},(x.unmaskOnReload??this.options.unmaskOnReload??!1)&&(x.state.__tempKey=this.tempLocationKey)),x.state.__hashScrollIntoViewOptions=g??this.options.defaultHashScrollIntoView??!0,this.shouldViewTransition=r,this.history[d.replace?"replace":"push"](x.href,x.state,{ignoreBlocker:c})}return this.resetNextScroll=d.resetScroll??!0,this.history.subscribers.size||this.load(),this.commitLocationPromise},this.buildAndCommitLocation=({replace:r,resetScroll:c,hashScrollIntoView:d,viewTransition:m,ignoreBlocker:f,href:v,...p}={})=>{if(v){const x=this.history.location.state.__TSR_index,b=_s(v,{__TSR_index:r?x:x+1});p.to=b.pathname,p.search=this.options.parseSearch(b.search),p.hash=b.hash.slice(1)}const g=this.buildLocation({...p,_includeValidateSearch:!0});return this.commitLocation({...g,viewTransition:m,replace:r,resetScroll:c,hashScrollIntoView:d,ignoreBlocker:f})},this.navigate=({to:r,reloadDocument:c,href:d,...m})=>{if(!c&&d)try{new URL(`${d}`),c=!0}catch{}if(c){if(!d){const f=this.buildLocation({to:r,...m});d=this.history.createHref(f.href)}m.replace?window.location.replace(d):window.location.href=d;return}return this.buildAndCommitLocation({...m,href:d,to:r,_isNavigate:!0})},this.beforeLoad=()=>{if(this.cancelMatches(),this.latestLocation=this.parseLocation(this.latestLocation),this.isServer){const c=this.buildLocation({to:this.latestLocation.pathname,search:!0,params:!0,hash:!0,state:!0,_includeValidateSearch:!0});if(Do(this.latestLocation.href)!==Do(c.href))throw Dv({href:c.href})}const r=this.matchRoutes(this.latestLocation);this.__store.setState(c=>({...c,status:"pending",isLoading:!0,location:this.latestLocation,pendingMatches:r,cachedMatches:c.cachedMatches.filter(d=>!r.find(m=>m.id===d.id))}))},this.load=async r=>{let c,d,m;for(m=new Promise(f=>{this.startTransition(async()=>{var v;try{this.beforeLoad();const p=this.latestLocation,g=this.state.resolvedLocation;this.state.redirect||this.emit({type:"onBeforeNavigate",...yl({resolvedLocation:g,location:p})}),this.emit({type:"onBeforeLoad",...yl({resolvedLocation:g,location:p})}),await this.loadMatches({sync:r?.sync,matches:this.state.pendingMatches,location:p,onReady:async()=>{this.startViewTransition(async()=>{let x,b,j;xm(()=>{this.__store.setState(T=>{const R=T.matches,N=T.pendingMatches||T.matches;return x=R.filter(S=>!N.find(A=>A.id===S.id)),b=N.filter(S=>!R.find(A=>A.id===S.id)),j=R.filter(S=>N.find(A=>A.id===S.id)),{...T,isLoading:!1,loadedAt:Date.now(),matches:N,pendingMatches:void 0,cachedMatches:[...T.cachedMatches,...x.filter(S=>S.status!=="error")]}}),this.clearExpiredCache()}),[[x,"onLeave"],[b,"onEnter"],[j,"onStay"]].forEach(([T,R])=>{T.forEach(N=>{var S,A;(A=(S=this.looseRoutesById[N.routeId].options)[R])==null||A.call(S,N)})})})}})}catch(p){sa(p)?(c=p,this.isServer||this.navigate({...c.options,replace:!0,ignoreBlocker:!0})):Qt(p)&&(d=p),this.__store.setState(g=>({...g,statusCode:c?c.status:d?404:g.matches.some(x=>x.status==="error")?500:200,redirect:c}))}this.latestLoadPromise===m&&((v=this.commitLocationPromise)==null||v.resolve(),this.latestLoadPromise=void 0,this.commitLocationPromise=void 0),f()})}),this.latestLoadPromise=m,await m;this.latestLoadPromise&&m!==this.latestLoadPromise;)await this.latestLoadPromise;this.hasNotFoundMatch()&&this.__store.setState(f=>({...f,statusCode:404}))},this.startViewTransition=r=>{const c=this.shouldViewTransition??this.options.defaultViewTransition;if(delete this.shouldViewTransition,c&&typeof document<"u"&&"startViewTransition"in document&&typeof document.startViewTransition=="function"){let d;if(typeof c=="object"&&this.isViewTransitionTypesSupported){const m=this.latestLocation,f=this.state.resolvedLocation,v=typeof c.types=="function"?c.types(yl({resolvedLocation:f,location:m})):c.types;d={update:r,types:v}}else d=r;document.startViewTransition(d)}else r()},this.updateMatch=(r,c)=>{var d;let m;const f=(d=this.state.pendingMatches)==null?void 0:d.find(x=>x.id===r),v=this.state.matches.find(x=>x.id===r),p=this.state.cachedMatches.find(x=>x.id===r),g=f?"pendingMatches":v?"matches":p?"cachedMatches":"";return g&&this.__store.setState(x=>{var b;return{...x,[g]:(b=x[g])==null?void 0:b.map(j=>j.id===r?m=c(j):j)}}),m},this.getMatch=r=>[...this.state.cachedMatches,...this.state.pendingMatches??[],...this.state.matches].find(c=>c.id===r),this.loadMatches=async({location:r,matches:c,preload:d,onReady:m,updateMatch:f=this.updateMatch,sync:v})=>{let p,g=!1;const x=async()=>{g||(g=!0,await m?.())},b=R=>!!(d&&!this.state.matches.find(N=>N.id===R));!this.isServer&&this.state.matches.find(R=>R._forcePending)&&x();const j=(R,N)=>{var S,A,X;if(sa(N)||Qt(N)){if(sa(N)&&N.redirectHandled&&!N.options.reloadDocument)throw N;if((S=R.beforeLoadPromise)==null||S.resolve(),(A=R.loaderPromise)==null||A.resolve(),f(R.id,q=>({...q,status:sa(N)?"redirected":Qt(N)?"notFound":"error",isFetching:!1,error:N,beforeLoadPromise:void 0,loaderPromise:void 0})),N.routeId||(N.routeId=R.routeId),(X=R.loadPromise)==null||X.resolve(),sa(N))throw g=!0,N.options._fromLocation=r,N.redirectHandled=!0,N=this.resolveRedirect(N),N;if(Qt(N))throw this._handleNotFound(c,N,{updateMatch:f}),N}},T=R=>{const N=this.getMatch(R);return!!(!this.isServer&&N._dehydrated||this.isServer&&N.ssr===!1)};try{await new Promise((R,N)=>{(async()=>{var S,A,X,q;try{const P=(K,V,oe)=>{var te,$;const{id:W,routeId:me}=c[K],be=this.looseRoutesById[me];if(V instanceof Promise)throw V;V.routerCode=oe,p=p??K,j(this.getMatch(W),V);try{($=(te=be.options).onError)==null||$.call(te,V)}catch(ge){V=ge,j(this.getMatch(W),V)}f(W,ge=>{var C,Q;return(C=ge.beforeLoadPromise)==null||C.resolve(),(Q=ge.loadPromise)==null||Q.resolve(),{...ge,error:V,status:"error",isFetching:!1,updatedAt:Date.now(),abortController:new AbortController,beforeLoadPromise:void 0}})};for(const[K,{id:V,routeId:oe}]of c.entries()){const te=this.getMatch(V),$=(S=c[K-1])==null?void 0:S.id,W=$?this.getMatch($):void 0,me=this.looseRoutesById[oe],be=me.options.pendingMs??this.options.defaultPendingMs;if(this.isServer){let U;if(this.isShell())U=V===Zt;else{const ue=this.options.defaultSsr??!0;if(W?.ssr===!1)U=!1;else{let _;if(me.options.ssr===void 0)_=ue;else if(typeof me.options.ssr=="function"){let L=function(se,I){return I?{status:"error",error:I}:{status:"success",value:se}};const{search:J,params:Z}=this.getMatch(V),F={search:L(J,te.searchError),params:L(Z,te.paramsError),location:r,matches:c.map(se=>({index:se.index,pathname:se.pathname,fullPath:se.fullPath,staticData:se.staticData,id:se.id,routeId:se.routeId,search:L(se.search,se.searchError),params:L(se.params,se.paramsError),ssr:se.ssr}))};_=await me.options.ssr(F)??ue}else _=me.options.ssr;_===!0&&W?.ssr==="data-only"?U="data-only":U=_}}f(V,ue=>({...ue,ssr:U}))}if(T(V))continue;const ge=!!(m&&!this.isServer&&!b(V)&&(me.options.loader||me.options.beforeLoad||_m(me))&&typeof be=="number"&&be!==1/0&&(me.options.pendingComponent??((A=this.options)==null?void 0:A.defaultPendingComponent)));let C=!0;const Q=()=>{if(ge&&this.getMatch(V).pendingTimeout===void 0){const U=setTimeout(()=>{try{x()}catch{}},be);f(V,ue=>({...ue,pendingTimeout:U}))}};if(te.beforeLoadPromise||te.loaderPromise){Q(),await te.beforeLoadPromise;const U=this.getMatch(V);U.status==="error"?C=!0:U.preload&&(U.status==="redirected"||U.status==="notFound")&&j(U,U.error)}if(C){try{f(V,_e=>{const Ce=_e.loadPromise;return{..._e,loadPromise:cn(()=>{Ce?.resolve()}),beforeLoadPromise:cn()}});const{paramsError:U,searchError:ue}=this.getMatch(V);U&&P(K,U,"PARSE_PARAMS"),ue&&P(K,ue,"VALIDATE_SEARCH"),Q();const _=new AbortController,L=W?.context??this.options.context??{};f(V,_e=>({..._e,isFetching:"beforeLoad",fetchCount:_e.fetchCount+1,abortController:_,context:{...L,..._e.__routeContext}}));const{search:J,params:Z,context:F,cause:se}=this.getMatch(V),I=b(V),ce={search:J,abortController:_,params:Z,preload:I,context:F,location:r,navigate:_e=>this.navigate({..._e,_fromLocation:r}),buildLocation:this.buildLocation,cause:I?"preload":se,matches:c},pe=await((q=(X=me.options).beforeLoad)==null?void 0:q.call(X,ce));(sa(pe)||Qt(pe))&&P(K,pe,"BEFORE_LOAD"),f(V,_e=>({..._e,__beforeLoadContext:pe,context:{...L,..._e.__routeContext,...pe},abortController:_}))}catch(U){P(K,U,"BEFORE_LOAD")}f(V,U=>{var ue;return(ue=U.beforeLoadPromise)==null||ue.resolve(),{...U,beforeLoadPromise:void 0,isFetching:!1}})}}const ee=c.slice(0,p),re=[];ee.forEach(({id:K,routeId:V},oe)=>{re.push((async()=>{let te=!1,$=!1;const W=this.looseRoutesById[V],me=async()=>{var C,Q,U,ue,_,L;const J=this.getMatch(K);if(!J)return;const Z={matches:c,match:J,params:J.params,loaderData:J.loaderData},F=await((Q=(C=W.options).head)==null?void 0:Q.call(C,Z)),se=F?.meta,I=F?.links,ce=F?.scripts,pe=F?.styles,_e=await((ue=(U=W.options).scripts)==null?void 0:ue.call(U,Z)),Ce=await((L=(_=W.options).headers)==null?void 0:L.call(_,Z));return{meta:se,links:I,headScripts:ce,headers:Ce,scripts:_e,styles:pe}},be=async()=>{const C=this.getMatch(K);C.minPendingPromise&&await C.minPendingPromise},ge=this.getMatch(K);if(T(K)){if(this.isServer){const C=await me();return f(K,Q=>({...Q,...C})),this.getMatch(K)}}else if(ge.loaderPromise){if(ge.status==="success"&&!v&&!ge.preload)return this.getMatch(K);await ge.loaderPromise;const C=this.getMatch(K);C.error&&j(C,C.error)}else{const C=re[oe-1],Q=()=>{const{params:I,loaderDeps:ce,abortController:pe,context:_e,cause:Ce}=this.getMatch(K),et=b(K);return{params:I,deps:ce,preload:!!et,parentMatchPromise:C,abortController:pe,context:_e,location:r,navigate:Ke=>this.navigate({...Ke,_fromLocation:r}),cause:et?"preload":Ce,route:W}},U=Date.now()-this.getMatch(K).updatedAt,ue=b(K),_=ue?W.options.preloadStaleTime??this.options.defaultPreloadStaleTime??3e4:W.options.staleTime??this.options.defaultStaleTime??0,L=W.options.shouldReload,J=typeof L=="function"?L(Q()):L;f(K,I=>({...I,loaderPromise:cn(),preload:!!ue&&!this.state.matches.find(ce=>ce.id===K)}));const Z=async()=>{var I,ce,pe,_e;try{try{(!this.isServer||this.isServer&&this.getMatch(K).ssr===!0)&&this.loadRouteChunk(W),f(K,Ke=>({...Ke,isFetching:"loader"}));const Ce=await((ce=(I=W.options).loader)==null?void 0:ce.call(I,Q()));j(this.getMatch(K),Ce),f(K,Ke=>({...Ke,loaderData:Ce})),await W._lazyPromise;const et=await me();await be(),await W._componentsPromise,f(K,Ke=>({...Ke,error:void 0,status:"success",isFetching:!1,updatedAt:Date.now(),...et}))}catch(Ce){let et=Ce;await be(),j(this.getMatch(K),Ce);try{(_e=(pe=W.options).onError)==null||_e.call(pe,Ce)}catch(Jt){et=Jt,j(this.getMatch(K),Jt)}const Ke=await me();f(K,Jt=>({...Jt,error:et,status:"error",isFetching:!1,...Ke}))}}catch(Ce){const et=await me();f(K,Ke=>({...Ke,loaderPromise:void 0,...et})),j(this.getMatch(K),Ce)}},{status:F,invalid:se}=this.getMatch(K);if(te=F==="success"&&(se||(J??U>_)),!(ue&&W.options.preload===!1))if(te&&!v)$=!0,(async()=>{try{await Z();const{loaderPromise:I,loadPromise:ce}=this.getMatch(K);I?.resolve(),ce?.resolve(),f(K,pe=>({...pe,loaderPromise:void 0}))}catch(I){sa(I)&&await this.navigate(I.options)}})();else if(F!=="success"||te&&v)await Z();else{const I=await me();f(K,ce=>({...ce,...I}))}}if(!$){const{loaderPromise:C,loadPromise:Q}=this.getMatch(K);C?.resolve(),Q?.resolve()}return f(K,C=>(clearTimeout(C.pendingTimeout),{...C,isFetching:$?C.isFetching:!1,loaderPromise:$?C.loaderPromise:void 0,invalid:!1,pendingTimeout:void 0,_dehydrated:void 0})),this.getMatch(K)})())}),await Promise.all(re),R()}catch(P){N(P)}})()}),await x()}catch(R){if(sa(R)||Qt(R))throw Qt(R)&&!d&&await x(),R}return c},this.invalidate=r=>{const c=d=>{var m;return((m=r?.filter)==null?void 0:m.call(r,d))??!0?{...d,invalid:!0,...r?.forcePending||d.status==="error"?{status:"pending",error:void 0}:{}}:d};return this.__store.setState(d=>{var m;return{...d,matches:d.matches.map(c),cachedMatches:d.cachedMatches.map(c),pendingMatches:(m=d.pendingMatches)==null?void 0:m.map(c)}}),this.shouldViewTransition=!1,this.load({sync:r?.sync})},this.resolveRedirect=r=>(r.options.href||(r.options.href=this.buildLocation(r.options).href,r.headers.set("Location",r.options.href)),r.headers.get("Location")||r.headers.set("Location",r.options.href),r),this.clearCache=r=>{const c=r?.filter;c!==void 0?this.__store.setState(d=>({...d,cachedMatches:d.cachedMatches.filter(m=>!c(m))})):this.__store.setState(d=>({...d,cachedMatches:[]}))},this.clearExpiredCache=()=>{const r=c=>{const d=this.looseRoutesById[c.routeId];if(!d.options.loader)return!0;const m=(c.preload?d.options.preloadGcTime??this.options.defaultPreloadGcTime:d.options.gcTime??this.options.defaultGcTime)??5*60*1e3;return!(c.status!=="error"&&Date.now()-c.updatedAt<m)};this.clearCache({filter:r})},this.loadRouteChunk=r=>(r._lazyPromise===void 0&&(r.lazyFn?r._lazyPromise=r.lazyFn().then(c=>{const{id:d,...m}=c.options;Object.assign(r.options,m)}):r._lazyPromise=Promise.resolve()),r._componentsPromise===void 0&&(r._componentsPromise=r._lazyPromise.then(()=>Promise.all(ep.map(async c=>{const d=r.options[c];d?.preload&&await d.preload()})))),r._componentsPromise),this.preloadRoute=async r=>{const c=this.buildLocation(r);let d=this.matchRoutes(c,{throwOnError:!0,preload:!0,dest:r});const m=new Set([...this.state.matches,...this.state.pendingMatches??[]].map(v=>v.id)),f=new Set([...m,...this.state.cachedMatches.map(v=>v.id)]);xm(()=>{d.forEach(v=>{f.has(v.id)||this.__store.setState(p=>({...p,cachedMatches:[...p.cachedMatches,v]}))})});try{return d=await this.loadMatches({matches:d,location:c,preload:!0,updateMatch:(v,p)=>{m.has(v)?d=d.map(g=>g.id===v?p(g):g):this.updateMatch(v,p)}}),d}catch(v){if(sa(v))return v.options.reloadDocument?void 0:await this.preloadRoute({...v.options,_fromLocation:c});Qt(v)||console.error(v);return}},this.matchRoute=(r,c)=>{const d={...r,to:r.to?this.resolvePathWithBase(r.from||"",r.to):void 0,params:r.params||{},leaveParams:!0},m=this.buildLocation(d);if(c?.pending&&this.state.status!=="pending")return!1;const v=(c?.pending===void 0?!this.state.isLoading:c.pending)?this.latestLocation:this.state.resolvedLocation||this.state.location,p=Fo(this.basepath,v.pathname,{...c,to:m.pathname});return!p||r.params&&!fn(p,r.params,{partial:!0})?!1:p&&(c?.includeSearch??!0)?fn(v.search,m.search,{partial:!0})?p:!1:p},this._handleNotFound=(r,c,{updateMatch:d=this.updateMatch}={})=>{var m;const f=this.routesById[c.routeId??""]??this.routeTree,v={};for(const g of r)v[g.routeId]=g;!f.options.notFoundComponent&&((m=this.options)!=null&&m.defaultNotFoundComponent)&&(f.options.notFoundComponent=this.options.defaultNotFoundComponent),Na(f.options.notFoundComponent);const p=v[f.id];Na(p,"Could not find match for route: "+f.id),d(p.id,g=>({...g,status:"notFound",error:c,isFetching:!1})),c.routerCode==="BEFORE_LOAD"&&f.parentRoute&&(c.routeId=f.parentRoute.id,this._handleNotFound(r,c,{updateMatch:d}))},this.hasNotFoundMatch=()=>this.__store.state.matches.some(r=>r.status==="notFound"||r.globalNotFound),this.update({defaultPreloadDelay:50,defaultPendingMs:1e3,defaultPendingMinMs:500,context:void 0,...u,caseSensitive:u.caseSensitive??!1,notFoundMode:u.notFoundMode??"fuzzy",stringifySearch:u.stringifySearch??Tv,parseSearch:u.parseSearch??Mv}),typeof document<"u"&&(self.__TSR_ROUTER__=this)}isShell(){return this.options.isShell}get state(){return this.__store.state}get looseRoutesById(){return this.routesById}matchRoutesInternal(u,r){var c;const{foundRoute:d,matchedRoutes:m,routeParams:f}=this.getMatchedRoutes(u.pathname,(c=r?.dest)==null?void 0:c.to);let v=!1;(d?d.path!=="/"&&f["**"]:hn(u.pathname))&&(this.options.notFoundRoute?m.push(this.options.notFoundRoute):v=!0);const p=(()=>{if(v){if(this.options.notFoundMode!=="root")for(let j=m.length-1;j>=0;j--){const T=m[j];if(T.children)return T.id}return Zt}})(),g=m.map(j=>{var T;let R;const N=((T=j.options.params)==null?void 0:T.parse)??j.options.parseParams;if(N)try{const S=N(f);Object.assign(f,S)}catch(S){if(R=new zv(S.message,{cause:S}),r?.throwOnError)throw R;return R}}),x=[],b=j=>j?.id?j.context??this.options.context??{}:this.options.context??{};return m.forEach((j,T)=>{var R,N;const S=x[T-1],[A,X,q]=(()=>{const be=S?.search??u.search,ge=S?._strictSearch??{};try{const C=Wo(j.options.validateSearch,{...be})??{};return[{...be,...C},{...ge,...C},void 0]}catch(C){let Q=C;if(C instanceof nr||(Q=new nr(C.message,{cause:C})),r?.throwOnError)throw Q;return[be,{},Q]}})(),P=((N=(R=j.options).loaderDeps)==null?void 0:N.call(R,{search:A}))??"",ee=P?JSON.stringify(P):"",{usedParams:re,interpolatedPath:K}=Ao({path:j.fullPath,params:f,decodeCharMap:this.pathParamsDecodeCharMap}),V=Ao({path:j.id,params:f,leaveWildcards:!0,decodeCharMap:this.pathParamsDecodeCharMap}).interpolatedPath+ee,oe=this.getMatch(V),te=this.state.matches.find(be=>be.routeId===j.id),$=te?"stay":"enter";let W;if(oe)W={...oe,cause:$,params:te?Xt(te.params,f):f,_strictParams:re,search:Xt(te?te.search:oe.search,A),_strictSearch:X};else{const be=j.options.loader||j.options.beforeLoad||j.lazyFn||_m(j)?"pending":"success";W={id:V,index:T,routeId:j.id,params:te?Xt(te.params,f):f,_strictParams:re,pathname:ja([this.basepath,K]),updatedAt:Date.now(),search:te?Xt(te.search,A):A,_strictSearch:X,searchError:void 0,status:be,isFetching:!1,error:void 0,paramsError:g[T],__routeContext:{},__beforeLoadContext:void 0,context:{},abortController:new AbortController,fetchCount:0,cause:$,loaderDeps:te?Xt(te.loaderDeps,P):P,invalid:!1,preload:!1,links:void 0,scripts:void 0,headScripts:void 0,meta:void 0,staticData:j.options.staticData||{},loadPromise:cn(),fullPath:j.fullPath}}r?.preload||(W.globalNotFound=p===j.id),W.searchError=q;const me=b(S);W.context={...me,...W.__routeContext,...W.__beforeLoadContext},x.push(W)}),x.forEach((j,T)=>{var R,N;const S=this.looseRoutesById[j.routeId];if(!this.getMatch(j.id)&&r?._buildLocation!==!0){const X=x[T-1],q=b(X),P={deps:j.loaderDeps,params:j.params,context:q,location:u,navigate:ee=>this.navigate({...ee,_fromLocation:u}),buildLocation:this.buildLocation,cause:j.cause,abortController:j.abortController,preload:!!j.preload,matches:x};j.__routeContext=((N=(R=S.options).context)==null?void 0:N.call(R,P))??{},j.context={...q,...j.__routeContext,...j.__beforeLoadContext}}}),x}comparePaths(u,r){return u.replace(/(.+)\/$/,"$1")===r.replace(/(.+)\/$/,"$1")}}class nr extends Error{}class zv extends Error{}function Ov(n){return{loadedAt:0,isLoading:!1,isTransitioning:!1,status:"idle",resolvedLocation:void 0,location:n,matches:[],pendingMatches:[],cachedMatches:[],statusCode:200}}function Wo(n,u){if(n==null)return{};if("~standard"in n){const r=n["~standard"].validate(u);if(r instanceof Promise)throw new nr("Async validation not supported");if(r.issues)throw new nr(JSON.stringify(r.issues,void 0,2),{cause:r});return r.value}return"parse"in n?n.parse(u):typeof n=="function"?n(u):{}}const ep=["component","errorComponent","pendingComponent","notFoundComponent"];function _m(n){var u;for(const r of ep)if((u=n.options[r])!=null&&u.preload)return!0;return!1}function kv({routeTree:n,initRoute:u}){const r={},c={},d=p=>{p.forEach((g,x)=>{u?.(g,x);const b=r[g.id];if(Na(!b,`Duplicate routes found with id: ${String(g.id)}`),r[g.id]=g,!g.isRoot&&g.path){const T=hn(g.fullPath);(!c[T]||g.fullPath.endsWith("/"))&&(c[T]=g)}const j=g.children;j?.length&&d(j)})};d([n]);const m=[];Object.values(r).forEach((p,g)=>{var x;if(p.isRoot||!p.path)return;const b=nu(p.fullPath),j=mn(b);for(;j.length>1&&((x=j[0])==null?void 0:x.value)==="/";)j.shift();const T=j.map(R=>R.value==="/"?.75:R.type==="param"&&R.prefixSegment&&R.suffixSegment?.55:R.type==="param"&&R.prefixSegment?.52:R.type==="param"&&R.suffixSegment?.51:R.type==="param"?.5:R.type==="wildcard"&&R.prefixSegment&&R.suffixSegment?.3:R.type==="wildcard"&&R.prefixSegment?.27:R.type==="wildcard"&&R.suffixSegment?.26:R.type==="wildcard"?.25:1);m.push({child:p,trimmed:b,parsed:j,index:g,scores:T})});const v=m.sort((p,g)=>{const x=Math.min(p.scores.length,g.scores.length);for(let b=0;b<x;b++)if(p.scores[b]!==g.scores[b])return g.scores[b]-p.scores[b];if(p.scores.length!==g.scores.length)return g.scores.length-p.scores.length;for(let b=0;b<x;b++)if(p.parsed[b].value!==g.parsed[b].value)return p.parsed[b].value>g.parsed[b].value?1:-1;return p.index-g.index}).map((p,g)=>(p.child.rank=g,p.child));return{routesById:r,routesByPath:c,flatRoutes:v}}function Uv({pathname:n,routePathname:u,basepath:r,caseSensitive:c,routesByPath:d,routesById:m,flatRoutes:f}){let v={};const p=hn(n),g=T=>{var R;return Fo(r,p,{to:T.fullPath,caseSensitive:((R=T.options)==null?void 0:R.caseSensitive)??c,fuzzy:!0})};let x=u!==void 0?d[u]:void 0;x?v=g(x):x=f.find(T=>{const R=g(T);return R?(v=R,!0):!1});let b=x||m[Zt];const j=[b];for(;b.parentRoute;)b=b.parentRoute,j.unshift(b);return{matchedRoutes:j,routeParams:v,foundRoute:x}}function Lv({search:n,dest:u,destRoutes:r,_includeValidateSearch:c}){const d=r.reduce((v,p)=>{var g;const x=[];if("search"in p.options)(g=p.options.search)!=null&&g.middlewares&&x.push(...p.options.search.middlewares);else if(p.options.preSearchFilters||p.options.postSearchFilters){const b=({search:j,next:T})=>{let R=j;"preSearchFilters"in p.options&&p.options.preSearchFilters&&(R=p.options.preSearchFilters.reduce((S,A)=>A(S),j));const N=T(R);return"postSearchFilters"in p.options&&p.options.postSearchFilters?p.options.postSearchFilters.reduce((S,A)=>A(S),N):N};x.push(b)}if(c&&p.options.validateSearch){const b=({search:j,next:T})=>{const R=T(j);try{return{...R,...Wo(p.options.validateSearch,R)??{}}}catch{return R}};x.push(b)}return v.concat(x)},[])??[],m=({search:v})=>u.search?u.search===!0?v:xl(u.search,v):{};d.push(m);const f=(v,p)=>{if(v>=d.length)return p;const g=d[v];return g({search:p,next:b=>f(v+1,b)})};return f(0,n)}const Bv="Error preloading route! ☝️";class tp{constructor(u){if(this.init=r=>{var c,d;this.originalIndex=r.originalIndex;const m=this.options,f=!m?.path&&!m?.id;this.parentRoute=(d=(c=this.options).getParentRoute)==null?void 0:d.call(c),f?this._path=Zt:this.parentRoute||Na(!1);let v=f?Zt:m?.path;v&&v!=="/"&&(v=nu(v));const p=m?.id||v;let g=f?Zt:ja([this.parentRoute.id===Zt?"":this.parentRoute.id,p]);v===Zt&&(v="/"),g!==Zt&&(g=ja(["/",g]));const x=g===Zt?"/":ja([this.parentRoute.fullPath,v]);this._path=v,this._id=g,this._fullPath=x,this._to=x},this.clone=r=>{this._path=r._path,this._id=r._id,this._fullPath=r._fullPath,this._to=r._to,this.options.getParentRoute=r.options.getParentRoute,this.children=r.children},this.addChildren=r=>this._addFileChildren(r),this._addFileChildren=r=>(Array.isArray(r)&&(this.children=r),typeof r=="object"&&r!==null&&(this.children=Object.values(r)),this),this._addFileTypes=()=>this,this.updateLoader=r=>(Object.assign(this.options,r),this),this.update=r=>(Object.assign(this.options,r),this),this.lazy=r=>(this.lazyFn=r,this),this.options=u||{},this.isRoot=!u?.getParentRoute,u?.id&&u?.path)throw new Error("Route cannot have both an 'id' and a 'path' option.")}get to(){return this._to}get id(){return this._id}get path(){return this._path}get fullPath(){return this._fullPath}}class qv extends tp{constructor(u){super(u)}}function su(n){const u=n.errorComponent??ir;return s.jsx(Hv,{getResetKey:n.getResetKey,onCatch:n.onCatch,children:({error:r,reset:c})=>r?G.createElement(u,{error:r,reset:c}):n.children})}class Hv extends G.Component{constructor(){super(...arguments),this.state={error:null}}static getDerivedStateFromProps(u){return{resetKey:u.getResetKey()}}static getDerivedStateFromError(u){return{error:u}}reset(){this.setState({error:null})}componentDidUpdate(u,r){r.error&&r.resetKey!==this.state.resetKey&&this.reset()}componentDidCatch(u,r){this.props.onCatch&&this.props.onCatch(u,r)}render(){return this.props.children({error:this.state.resetKey!==this.props.getResetKey()?null:this.state.error,reset:()=>{this.reset()}})}}function ir({error:n}){const[u,r]=G.useState(!1);return s.jsxs("div",{style:{padding:".5rem",maxWidth:"100%"},children:[s.jsxs("div",{style:{display:"flex",alignItems:"center",gap:".5rem"},children:[s.jsx("strong",{style:{fontSize:"1rem"},children:"Something went wrong!"}),s.jsx("button",{style:{appearance:"none",fontSize:".6em",border:"1px solid currentColor",padding:".1rem .2rem",fontWeight:"bold",borderRadius:".25rem"},onClick:()=>r(c=>!c),children:u?"Hide Error":"Show Error"})]}),s.jsx("div",{style:{height:".25rem"}}),u?s.jsx("div",{children:s.jsx("pre",{style:{fontSize:".7em",border:"1px solid red",borderRadius:".25rem",padding:".3rem",color:"red",overflow:"auto"},children:n.message?s.jsx("code",{children:n.message}):null})}):null]})}function Vv({children:n,fallback:u=null}){return Gv()?s.jsx(Kt.Fragment,{children:n}):s.jsx(Kt.Fragment,{children:u})}function Gv(){return Kt.useSyncExternalStore(Yv,()=>!0,()=>!1)}function Yv(){return()=>{}}var ko={exports:{}},Uo={},Lo={exports:{}},Bo={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rm;function Xv(){if(Rm)return Bo;Rm=1;var n=Ts();function u(b,j){return b===j&&(b!==0||1/b===1/j)||b!==b&&j!==j}var r=typeof Object.is=="function"?Object.is:u,c=n.useState,d=n.useEffect,m=n.useLayoutEffect,f=n.useDebugValue;function v(b,j){var T=j(),R=c({inst:{value:T,getSnapshot:j}}),N=R[0].inst,S=R[1];return m(function(){N.value=T,N.getSnapshot=j,p(N)&&S({inst:N})},[b,T,j]),d(function(){return p(N)&&S({inst:N}),b(function(){p(N)&&S({inst:N})})},[b]),f(T),T}function p(b){var j=b.getSnapshot;b=b.value;try{var T=j();return!r(b,T)}catch{return!0}}function g(b,j){return j()}var x=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?g:v;return Bo.useSyncExternalStore=n.useSyncExternalStore!==void 0?n.useSyncExternalStore:x,Bo}var Mm;function Qv(){return Mm||(Mm=1,Lo.exports=Xv()),Lo.exports}/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Tm;function Zv(){if(Tm)return Uo;Tm=1;var n=Ts(),u=Qv();function r(g,x){return g===x&&(g!==0||1/g===1/x)||g!==g&&x!==x}var c=typeof Object.is=="function"?Object.is:r,d=u.useSyncExternalStore,m=n.useRef,f=n.useEffect,v=n.useMemo,p=n.useDebugValue;return Uo.useSyncExternalStoreWithSelector=function(g,x,b,j,T){var R=m(null);if(R.current===null){var N={hasValue:!1,value:null};R.current=N}else N=R.current;R=v(function(){function A(re){if(!X){if(X=!0,q=re,re=j(re),T!==void 0&&N.hasValue){var K=N.value;if(T(K,re))return P=K}return P=re}if(K=P,c(q,re))return K;var V=j(re);return T!==void 0&&T(K,V)?(q=re,K):(q=re,P=V)}var X=!1,q,P,ee=b===void 0?null:b;return[function(){return A(x())},ee===null?void 0:function(){return A(ee())}]},[x,b,j,T]);var S=d(g,R[0],R[1]);return f(function(){N.hasValue=!0,N.value=S},[S]),p(S),S},Uo}var Em;function Kv(){return Em||(Em=1,ko.exports=Zv()),ko.exports}var $v=Kv();function Jv(n,u=r=>r){return $v.useSyncExternalStoreWithSelector(n.subscribe,()=>n.state,()=>n.state,u,Fv)}function Fv(n,u){if(Object.is(n,u))return!0;if(typeof n!="object"||n===null||typeof u!="object"||u===null)return!1;if(n instanceof Map&&u instanceof Map){if(n.size!==u.size)return!1;for(const[c,d]of n)if(!u.has(c)||!Object.is(d,u.get(c)))return!1;return!0}if(n instanceof Set&&u instanceof Set){if(n.size!==u.size)return!1;for(const c of n)if(!u.has(c))return!1;return!0}if(n instanceof Date&&u instanceof Date)return n.getTime()===u.getTime();const r=Object.keys(n);if(r.length!==Object.keys(u).length)return!1;for(let c=0;c<r.length;c++)if(!Object.prototype.hasOwnProperty.call(u,r[c])||!Object.is(n[r[c]],u[r[c]]))return!1;return!0}const qo=G.createContext(null);function ap(){return typeof document>"u"?qo:window.__TSR_ROUTER_CONTEXT__?window.__TSR_ROUTER_CONTEXT__:(window.__TSR_ROUTER_CONTEXT__=qo,qo)}function It(n){const u=G.useContext(ap());return n?.warn,u}function xt(n){const u=It({warn:n?.router===void 0}),r=n?.router||u,c=G.useRef(void 0);return Jv(r.__store,d=>{if(n?.select){if(n.structuralSharing??r.options.defaultStructuralSharing){const m=Xt(c.current,n.select(d));return c.current=m,m}return n.select(d)}return d})}const rr=G.createContext(void 0),Pv=G.createContext(void 0);function $t(n){const u=G.useContext(n.from?Pv:rr);return xt({select:c=>{const d=c.matches.find(m=>n.from?n.from===m.routeId:m.id===u);if(Na(!((n.shouldThrow??!0)&&!d),`Could not find ${n.from?`an active match from "${n.from}"`:"a nearest match!"}`),d!==void 0)return n.select?n.select(d):d},structuralSharing:n.structuralSharing})}function iu(n){return $t({from:n.from,strict:n.strict,structuralSharing:n.structuralSharing,select:u=>n.select?n.select(u.loaderData):u.loaderData})}function ru(n){const{select:u,...r}=n;return $t({...r,select:c=>u?u(c.loaderDeps):c.loaderDeps})}function cu(n){return $t({from:n.from,strict:n.strict,shouldThrow:n.shouldThrow,structuralSharing:n.structuralSharing,select:u=>n.select?n.select(u.params):u.params})}function ou(n){return $t({from:n.from,strict:n.strict,shouldThrow:n.shouldThrow,structuralSharing:n.structuralSharing,select:u=>n.select?n.select(u.search):u.search})}function cr(n){const{navigate:u,state:r}=It(),c=$t({strict:!1,select:d=>d.index});return G.useCallback(d=>{const m=d.from??n?.from??r.matches[c].fullPath;return u({...d,from:m})},[n?.from,u])}var Wv=Jm();const js=typeof window<"u"?G.useLayoutEffect:G.useEffect;function Ho(n){const u=G.useRef({value:n,prev:null}),r=u.current.value;return n!==r&&(u.current={value:n,prev:r}),u.current.prev}function Iv(n,u,r={},c={}){const d=G.useRef(typeof IntersectionObserver=="function"),m=G.useRef(null);return G.useEffect(()=>{if(!(!n.current||!d.current||c.disabled))return m.current=new IntersectionObserver(([f])=>{u(f)},r),m.current.observe(n.current),()=>{var f;(f=m.current)==null||f.disconnect()}},[u,r,c.disabled,n]),m.current}function ex(n){const u=G.useRef(null);return G.useImperativeHandle(n,()=>u.current,[]),u}function tx(n,u){const r=It(),[c,d]=G.useState(!1),m=G.useRef(!1),f=ex(u),{activeProps:v=()=>({className:"active"}),inactiveProps:p=()=>({}),activeOptions:g,to:x,preload:b,preloadDelay:j,hashScrollIntoView:T,replace:R,startTransition:N,resetScroll:S,viewTransition:A,children:X,target:q,disabled:P,style:ee,className:re,onClick:K,onFocus:V,onMouseEnter:oe,onMouseLeave:te,onTouchStart:$,ignoreBlocker:W,...me}=n,{params:be,search:ge,hash:C,state:Q,mask:U,reloadDocument:ue,unsafeRelative:_,...L}=me,J=G.useMemo(()=>{try{return new URL(`${x}`),"external"}catch{}return"internal"},[x]),Z=xt({select:Ge=>Ge.location.search,structuralSharing:!0}),F=$t({strict:!1,select:Ge=>Ge.fullPath}),se=n.from??F;n={...n,from:se};const I=G.useMemo(()=>r.buildLocation(n),[r,n,Z]),ce=G.useMemo(()=>n.reloadDocument?!1:b??r.options.defaultPreload,[r.options.defaultPreload,b,n.reloadDocument]),pe=j??r.options.defaultPreloadDelay??0,_e=xt({select:Ge=>{if(g?.exact){if(!dv(Ge.location.pathname,I.pathname,r.basepath))return!1}else{const Fe=er(Ge.location.pathname,r.basepath).split("/");if(!er(I.pathname,r.basepath).split("/").every((Wa,yt)=>Wa===Fe[yt]))return!1}return(g?.includeSearch??!0)&&!fn(Ge.location.search,I.search,{partial:!g?.exact,ignoreUndefined:!g?.explicitUndefined})?!1:g?.includeHash?Ge.location.hash===I.hash:!0}}),Ce=G.useCallback(()=>{r.preloadRoute(n).catch(Ge=>{console.warn(Ge),console.warn(Bv)})},[n,r]),et=G.useCallback(Ge=>{Ge?.isIntersecting&&Ce()},[Ce]);if(Iv(f,et,{rootMargin:"100px"},{disabled:!!P||ce!=="viewport"}),js(()=>{m.current||!P&&ce==="render"&&(Ce(),m.current=!0)},[P,Ce,ce]),J==="external")return{...L,ref:f,type:J,href:x,...X&&{children:X},...q&&{target:q},...P&&{disabled:P},...ee&&{style:ee},...re&&{className:re},...K&&{onClick:K},...V&&{onFocus:V},...oe&&{onMouseEnter:oe},...te&&{onMouseLeave:te},...$&&{onTouchStart:$}};const Ke=Ge=>{if(!P&&!ax(Ge)&&!Ge.defaultPrevented&&(!q||q==="_self")&&Ge.button===0){Ge.preventDefault(),Wv.flushSync(()=>{d(!0)});const Fe=r.subscribe("onResolved",()=>{Fe(),d(!1)});return r.navigate({...n,replace:R,resetScroll:S,hashScrollIntoView:T,startTransition:N,viewTransition:A,ignoreBlocker:W})}},Jt=Ge=>{P||ce&&Ce()},xn=Jt,ur=Ge=>{if(P)return;const Fe=Ge.target||{};if(ce){if(Fe.preloadTimeout)return;pe?Fe.preloadTimeout=setTimeout(()=>{Fe.preloadTimeout=null,Ce()},pe):Ce()}},dr=Ge=>{if(P)return;const Fe=Ge.target||{};Fe.preloadTimeout&&(clearTimeout(Fe.preloadTimeout),Fe.preloadTimeout=null)},mt=Ge=>Fe=>{var Sn;(Sn=Fe.persist)==null||Sn.call(Fe),Ge.filter(Boolean).forEach(Ds=>{Fe.defaultPrevented||Ds(Fe)})},yn=_e?xl(v,{})??{}:{},bl=_e?{}:xl(p,{}),bn=[re,yn.className,bl.className].filter(Boolean).join(" "),Pa={...ee,...yn.style,...bl.style};return{...L,...yn,...bl,href:P?void 0:I.maskedLocation?r.history.createHref(I.maskedLocation.href):r.history.createHref(I.href),ref:f,onClick:mt([K,Ke]),onFocus:mt([V,Jt]),onMouseEnter:mt([oe,ur]),onMouseLeave:mt([te,dr]),onTouchStart:mt([$,xn]),disabled:!!P,target:q,...Object.keys(Pa).length&&{style:Pa},...bn&&{className:bn},...P&&{role:"link","aria-disabled":!0},..._e&&{"data-status":"active","aria-current":"page"},...c&&{"data-transitioning":"transitioning"}}}const uu=G.forwardRef((n,u)=>{const{_asChild:r,...c}=n,{type:d,ref:m,...f}=tx(c,u),v=typeof c.children=="function"?c.children({isActive:f["data-status"]==="active"}):c.children;return typeof r>"u"&&delete f.disabled,G.createElement(r||"a",{...f,ref:m},v)});function ax(n){return!!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey)}class lx extends tp{constructor(u){super(u),this.useMatch=r=>$t({select:r?.select,from:this.id,structuralSharing:r?.structuralSharing}),this.useRouteContext=r=>$t({...r,from:this.id,select:c=>r?.select?r.select(c.context):c.context}),this.useSearch=r=>ou({select:r?.select,structuralSharing:r?.structuralSharing,from:this.id}),this.useParams=r=>cu({select:r?.select,structuralSharing:r?.structuralSharing,from:this.id}),this.useLoaderDeps=r=>ru({...r,from:this.id}),this.useLoaderData=r=>iu({...r,from:this.id}),this.useNavigate=()=>cr({from:this.fullPath}),this.Link=Kt.forwardRef((r,c)=>s.jsx(uu,{ref:c,from:this.fullPath,...r})),this.$$typeof=Symbol.for("react.memo")}}function Es(n){return new lx(n)}class nx extends qv{constructor(u){super(u),this.useMatch=r=>$t({select:r?.select,from:this.id,structuralSharing:r?.structuralSharing}),this.useRouteContext=r=>$t({...r,from:this.id,select:c=>r?.select?r.select(c.context):c.context}),this.useSearch=r=>ou({select:r?.select,structuralSharing:r?.structuralSharing,from:this.id}),this.useParams=r=>cu({select:r?.select,structuralSharing:r?.structuralSharing,from:this.id}),this.useLoaderDeps=r=>ru({...r,from:this.id}),this.useLoaderData=r=>iu({...r,from:this.id}),this.useNavigate=()=>cr({from:this.fullPath}),this.Link=Kt.forwardRef((r,c)=>s.jsx(uu,{ref:c,from:this.fullPath,...r})),this.$$typeof=Symbol.for("react.memo")}}function sx(n){return new nx(n)}function Cm(n){return typeof n=="object"?new Dm(n,{silent:!0}).createRoute(n):new Dm(n,{silent:!0}).createRoute}class Dm{constructor(u,r){this.path=u,this.createRoute=c=>{this.silent;const d=Es(c);return d.isRoot=!1,d},this.silent=r?.silent}}class Am{constructor(u){this.useMatch=r=>$t({select:r?.select,from:this.options.id,structuralSharing:r?.structuralSharing}),this.useRouteContext=r=>$t({from:this.options.id,select:c=>r?.select?r.select(c.context):c.context}),this.useSearch=r=>ou({select:r?.select,structuralSharing:r?.structuralSharing,from:this.options.id}),this.useParams=r=>cu({select:r?.select,structuralSharing:r?.structuralSharing,from:this.options.id}),this.useLoaderDeps=r=>ru({...r,from:this.options.id}),this.useLoaderData=r=>iu({...r,from:this.options.id}),this.useNavigate=()=>{const r=It();return cr({from:r.routesById[this.options.id].fullPath})},this.options=u,this.$$typeof=Symbol.for("react.memo")}}function zm(n){return typeof n=="object"?new Am(n):u=>new Am({id:n,...u})}function ix(){const n=It(),u=G.useRef({router:n,mounted:!1}),[r,c]=G.useState(!1),{hasPendingMatches:d,isLoading:m}=xt({select:b=>({isLoading:b.isLoading,hasPendingMatches:b.matches.some(j=>j.status==="pending")}),structuralSharing:!0}),f=Ho(m),v=m||r||d,p=Ho(v),g=m||d,x=Ho(g);return n.isServer||(n.startTransition=b=>{c(!0),G.startTransition(()=>{b(),c(!1)})}),G.useEffect(()=>{const b=n.history.subscribe(n.load),j=n.buildLocation({to:n.latestLocation.pathname,search:!0,params:!0,hash:!0,state:!0,_includeValidateSearch:!0});return hn(n.latestLocation.href)!==hn(j.href)&&n.commitLocation({...j,replace:!0}),()=>{b()}},[n,n.history]),js(()=>{if(typeof window<"u"&&n.ssr||u.current.router===n&&u.current.mounted)return;u.current={router:n,mounted:!0},(async()=>{try{await n.load()}catch(j){console.error(j)}})()},[n]),js(()=>{f&&!m&&n.emit({type:"onLoad",...yl(n.state)})},[f,n,m]),js(()=>{x&&!g&&n.emit({type:"onBeforeRouteMount",...yl(n.state)})},[g,x,n]),js(()=>{p&&!v&&(n.emit({type:"onResolved",...yl(n.state)}),n.__store.setState(b=>({...b,status:"idle",resolvedLocation:b.location})),wv(n))},[v,p,n]),null}function rx(n){const u=xt({select:r=>`not-found-${r.location.pathname}-${r.status}`});return s.jsx(su,{getResetKey:()=>u,onCatch:(r,c)=>{var d;if(Qt(r))(d=n.onCatch)==null||d.call(n,r,c);else throw r},errorComponent:({error:r})=>{var c;if(Qt(r))return(c=n.fallback)==null?void 0:c.call(n,r);throw r},children:n.children})}function cx(){return s.jsx("p",{children:"Not Found"})}function on(n){return s.jsx(s.Fragment,{children:n.children})}function lp(n,u,r){return u.options.notFoundComponent?s.jsx(u.options.notFoundComponent,{data:r}):n.options.defaultNotFoundComponent?s.jsx(n.options.defaultNotFoundComponent,{data:r}):s.jsx(cx,{})}function ox({children:n}){return typeof document<"u"?null:s.jsx("script",{className:"$tsr",dangerouslySetInnerHTML:{__html:[n].filter(Boolean).join(`
`)}})}function ux(){const n=It(),r=(n.options.getScrollRestorationKey||Po)(n.latestLocation),c=r!==Po(n.latestLocation)?r:null;return!n.isScrollRestoring||!n.isServer?null:s.jsx(ox,{children:`(${Im.toString()})(${JSON.stringify(ar)},${JSON.stringify(c)}, undefined, true)`})}const np=G.memo(function({matchId:u}){var r,c;const d=It(),m=xt({select:q=>{const P=q.matches.find(ee=>ee.id===u);return Na(P),Ii(P,["routeId","ssr","_displayPending"])},structuralSharing:!0}),f=d.routesById[m.routeId],v=f.options.pendingComponent??d.options.defaultPendingComponent,p=v?s.jsx(v,{}):null,g=f.options.errorComponent??d.options.defaultErrorComponent,x=f.options.onCatch??d.options.defaultOnCatch,b=f.isRoot?f.options.notFoundComponent??((r=d.options.notFoundRoute)==null?void 0:r.options.component):f.options.notFoundComponent,j=m.ssr===!1||m.ssr==="data-only",T=(!f.isRoot||f.options.wrapInSuspense||j)&&(f.options.wrapInSuspense??v??(((c=f.options.errorComponent)==null?void 0:c.preload)||j))?G.Suspense:on,R=g?su:on,N=b?rx:on,S=xt({select:q=>q.loadedAt}),A=xt({select:q=>{var P;const ee=q.matches.findIndex(re=>re.id===u);return(P=q.matches[ee-1])==null?void 0:P.routeId}}),X=f.isRoot?f.options.shellComponent??on:on;return s.jsxs(X,{children:[s.jsx(rr.Provider,{value:u,children:s.jsx(T,{fallback:p,children:s.jsx(R,{getResetKey:()=>S,errorComponent:g||ir,onCatch:(q,P)=>{if(Qt(q))throw q;x?.(q,P)},children:s.jsx(N,{fallback:q=>{if(!b||q.routeId&&q.routeId!==m.routeId||!q.routeId&&!f.isRoot)throw q;return G.createElement(b,q)},children:j||m._displayPending?s.jsx(Vv,{fallback:p,children:s.jsx(Om,{matchId:u})}):s.jsx(Om,{matchId:u})})})})}),A===Zt&&d.options.scrollRestoration?s.jsxs(s.Fragment,{children:[s.jsx(dx,{}),s.jsx(ux,{})]}):null]})});function dx(){const n=It(),u=G.useRef(void 0);return s.jsx("script",{suppressHydrationWarning:!0,ref:r=>{r&&(u.current===void 0||u.current.href!==n.latestLocation.href)&&(n.emit({type:"onRendered",...yl(n.state)}),u.current=n.latestLocation)}},n.latestLocation.state.__TSR_key)}const Om=G.memo(function({matchId:u}){var r,c,d,m,f;const v=It(),{match:p,key:g,routeId:x}=xt({select:T=>{const R=T.matches.findIndex(P=>P.id===u),N=T.matches[R],S=N.routeId,A=v.routesById[S].options.remountDeps??v.options.defaultRemountDeps,X=A?.({routeId:S,loaderDeps:N.loaderDeps,params:N._strictParams,search:N._strictSearch});return{key:X?JSON.stringify(X):void 0,routeId:S,match:Ii(N,["id","status","error","_forcePending","_displayPending"])}},structuralSharing:!0}),b=v.routesById[x],j=G.useMemo(()=>{const T=b.options.component??v.options.defaultComponent;return T?s.jsx(T,{},g):s.jsx(sp,{})},[g,b.options.component,v.options.defaultComponent]);if(p._displayPending)throw(r=v.getMatch(p.id))==null?void 0:r.displayPendingPromise;if(p._forcePending)throw(c=v.getMatch(p.id))==null?void 0:c.minPendingPromise;if(p.status==="pending"){const T=b.options.pendingMinMs??v.options.defaultPendingMinMs;if(T&&!((d=v.getMatch(p.id))!=null&&d.minPendingPromise)&&!v.isServer){const R=cn();Promise.resolve().then(()=>{v.updateMatch(p.id,N=>({...N,minPendingPromise:R}))}),setTimeout(()=>{R.resolve(),v.updateMatch(p.id,N=>({...N,minPendingPromise:void 0}))},T)}throw(m=v.getMatch(p.id))==null?void 0:m.loadPromise}if(p.status==="notFound")return Na(Qt(p.error)),lp(v,b,p.error);if(p.status==="redirected")throw Na(sa(p.error)),(f=v.getMatch(p.id))==null?void 0:f.loadPromise;if(p.status==="error"){if(v.isServer){const T=(b.options.errorComponent??v.options.defaultErrorComponent)||ir;return s.jsx(T,{error:p.error,reset:void 0,info:{componentStack:""}})}throw p.error}return j}),sp=G.memo(function(){const u=It(),r=G.useContext(rr),c=xt({select:g=>{var x;return(x=g.matches.find(b=>b.id===r))==null?void 0:x.routeId}}),d=u.routesById[c],m=xt({select:g=>{const b=g.matches.find(j=>j.id===r);return Na(b),b.globalNotFound}}),f=xt({select:g=>{var x;const b=g.matches,j=b.findIndex(T=>T.id===r);return(x=b[j+1])==null?void 0:x.id}}),v=u.options.defaultPendingComponent?s.jsx(u.options.defaultPendingComponent,{}):null;if(m)return lp(u,d,void 0);if(!f)return null;const p=s.jsx(np,{matchId:f});return r===Zt?s.jsx(G.Suspense,{fallback:v,children:p}):p});function fx(){const n=It(),u=n.options.defaultPendingComponent?s.jsx(n.options.defaultPendingComponent,{}):null,r=n.isServer||typeof document<"u"&&n.ssr?on:G.Suspense,c=s.jsxs(r,{fallback:u,children:[s.jsx(ix,{}),s.jsx(hx,{})]});return n.options.InnerWrap?s.jsx(n.options.InnerWrap,{children:c}):c}function hx(){const n=xt({select:r=>{var c;return(c=r.matches[0])==null?void 0:c.id}}),u=xt({select:r=>r.loadedAt});return s.jsx(rr.Provider,{value:n,children:s.jsx(su,{getResetKey:()=>u,errorComponent:ir,onCatch:r=>{r.message||r.toString()},children:n?s.jsx(np,{matchId:n}):null})})}const mx=n=>new px(n);class px extends Av{constructor(u){super(u)}}typeof globalThis<"u"?(globalThis.createFileRoute=Cm,globalThis.createLazyFileRoute=zm):typeof window<"u"&&(window.createFileRoute=Cm,window.createFileRoute=zm);function gx({router:n,children:u,...r}){Object.keys(r).length>0&&n.update({...n.options,...r,context:{...n.options.context,...r.context}});const c=ap(),d=s.jsx(c.Provider,{value:n,children:u});return n.options.Wrap?s.jsx(n.options.Wrap,{children:d}):d}function vx({router:n,...u}){return s.jsx(gx,{router:n,...u,children:s.jsx(fx,{})})}function ip(n){var u,r,c="";if(typeof n=="string"||typeof n=="number")c+=n;else if(typeof n=="object")if(Array.isArray(n)){var d=n.length;for(u=0;u<d;u++)n[u]&&(r=ip(n[u]))&&(c&&(c+=" "),c+=r)}else for(r in n)n[r]&&(c&&(c+=" "),c+=r);return c}function xx(){for(var n,u,r=0,c="",d=arguments.length;r<d;r++)(n=arguments[r])&&(u=ip(n))&&(c&&(c+=" "),c+=u);return c}const du="-",yx=n=>{const u=Sx(n),{conflictingClassGroups:r,conflictingClassGroupModifiers:c}=n;return{getClassGroupId:f=>{const v=f.split(du);return v[0]===""&&v.length!==1&&v.shift(),rp(v,u)||bx(f)},getConflictingClassGroupIds:(f,v)=>{const p=r[f]||[];return v&&c[f]?[...p,...c[f]]:p}}},rp=(n,u)=>{if(n.length===0)return u.classGroupId;const r=n[0],c=u.nextPart.get(r),d=c?rp(n.slice(1),c):void 0;if(d)return d;if(u.validators.length===0)return;const m=n.join(du);return u.validators.find(({validator:f})=>f(m))?.classGroupId},km=/^\[(.+)\]$/,bx=n=>{if(km.test(n)){const u=km.exec(n)[1],r=u?.substring(0,u.indexOf(":"));if(r)return"arbitrary.."+r}},Sx=n=>{const{theme:u,classGroups:r}=n,c={nextPart:new Map,validators:[]};for(const d in r)Io(r[d],c,d,u);return c},Io=(n,u,r,c)=>{n.forEach(d=>{if(typeof d=="string"){const m=d===""?u:Um(u,d);m.classGroupId=r;return}if(typeof d=="function"){if(jx(d)){Io(d(c),u,r,c);return}u.validators.push({validator:d,classGroupId:r});return}Object.entries(d).forEach(([m,f])=>{Io(f,Um(u,m),r,c)})})},Um=(n,u)=>{let r=n;return u.split(du).forEach(c=>{r.nextPart.has(c)||r.nextPart.set(c,{nextPart:new Map,validators:[]}),r=r.nextPart.get(c)}),r},jx=n=>n.isThemeGetter,Nx=n=>{if(n<1)return{get:()=>{},set:()=>{}};let u=0,r=new Map,c=new Map;const d=(m,f)=>{r.set(m,f),u++,u>n&&(u=0,c=r,r=new Map)};return{get(m){let f=r.get(m);if(f!==void 0)return f;if((f=c.get(m))!==void 0)return d(m,f),f},set(m,f){r.has(m)?r.set(m,f):d(m,f)}}},eu="!",tu=":",wx=tu.length,_x=n=>{const{prefix:u,experimentalParseClassName:r}=n;let c=d=>{const m=[];let f=0,v=0,p=0,g;for(let R=0;R<d.length;R++){let N=d[R];if(f===0&&v===0){if(N===tu){m.push(d.slice(p,R)),p=R+wx;continue}if(N==="/"){g=R;continue}}N==="["?f++:N==="]"?f--:N==="("?v++:N===")"&&v--}const x=m.length===0?d:d.substring(p),b=Rx(x),j=b!==x,T=g&&g>p?g-p:void 0;return{modifiers:m,hasImportantModifier:j,baseClassName:b,maybePostfixModifierPosition:T}};if(u){const d=u+tu,m=c;c=f=>f.startsWith(d)?m(f.substring(d.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:f,maybePostfixModifierPosition:void 0}}if(r){const d=c;c=m=>r({className:m,parseClassName:d})}return c},Rx=n=>n.endsWith(eu)?n.substring(0,n.length-1):n.startsWith(eu)?n.substring(1):n,Mx=n=>{const u=Object.fromEntries(n.orderSensitiveModifiers.map(c=>[c,!0]));return c=>{if(c.length<=1)return c;const d=[];let m=[];return c.forEach(f=>{f[0]==="["||u[f]?(d.push(...m.sort(),f),m=[]):m.push(f)}),d.push(...m.sort()),d}},Tx=n=>({cache:Nx(n.cacheSize),parseClassName:_x(n),sortModifiers:Mx(n),...yx(n)}),Ex=/\s+/,Cx=(n,u)=>{const{parseClassName:r,getClassGroupId:c,getConflictingClassGroupIds:d,sortModifiers:m}=u,f=[],v=n.trim().split(Ex);let p="";for(let g=v.length-1;g>=0;g-=1){const x=v[g],{isExternal:b,modifiers:j,hasImportantModifier:T,baseClassName:R,maybePostfixModifierPosition:N}=r(x);if(b){p=x+(p.length>0?" "+p:p);continue}let S=!!N,A=c(S?R.substring(0,N):R);if(!A){if(!S){p=x+(p.length>0?" "+p:p);continue}if(A=c(R),!A){p=x+(p.length>0?" "+p:p);continue}S=!1}const X=m(j).join(":"),q=T?X+eu:X,P=q+A;if(f.includes(P))continue;f.push(P);const ee=d(A,S);for(let re=0;re<ee.length;++re){const K=ee[re];f.push(q+K)}p=x+(p.length>0?" "+p:p)}return p};function Dx(){let n=0,u,r,c="";for(;n<arguments.length;)(u=arguments[n++])&&(r=cp(u))&&(c&&(c+=" "),c+=r);return c}const cp=n=>{if(typeof n=="string")return n;let u,r="";for(let c=0;c<n.length;c++)n[c]&&(u=cp(n[c]))&&(r&&(r+=" "),r+=u);return r};function Ax(n,...u){let r,c,d,m=f;function f(p){const g=u.reduce((x,b)=>b(x),n());return r=Tx(g),c=r.cache.get,d=r.cache.set,m=v,v(p)}function v(p){const g=c(p);if(g)return g;const x=Cx(p,r);return d(p,x),x}return function(){return m(Dx.apply(null,arguments))}}const st=n=>{const u=r=>r[n]||[];return u.isThemeGetter=!0,u},op=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,up=/^\((?:(\w[\w-]*):)?(.+)\)$/i,zx=/^\d+\/\d+$/,Ox=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,kx=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Ux=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,Lx=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Bx=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,rn=n=>zx.test(n),ye=n=>!!n&&!Number.isNaN(Number(n)),$a=n=>!!n&&Number.isInteger(Number(n)),Vo=n=>n.endsWith("%")&&ye(n.slice(0,-1)),Sa=n=>Ox.test(n),qx=()=>!0,Hx=n=>kx.test(n)&&!Ux.test(n),dp=()=>!1,Vx=n=>Lx.test(n),Gx=n=>Bx.test(n),Yx=n=>!le(n)&&!ne(n),Xx=n=>pn(n,mp,dp),le=n=>op.test(n),vl=n=>pn(n,pp,Hx),Go=n=>pn(n,Jx,ye),Lm=n=>pn(n,fp,dp),Qx=n=>pn(n,hp,Gx),Zi=n=>pn(n,gp,Vx),ne=n=>up.test(n),bs=n=>gn(n,pp),Zx=n=>gn(n,Fx),Bm=n=>gn(n,fp),Kx=n=>gn(n,mp),$x=n=>gn(n,hp),Ki=n=>gn(n,gp,!0),pn=(n,u,r)=>{const c=op.exec(n);return c?c[1]?u(c[1]):r(c[2]):!1},gn=(n,u,r=!1)=>{const c=up.exec(n);return c?c[1]?u(c[1]):r:!1},fp=n=>n==="position"||n==="percentage",hp=n=>n==="image"||n==="url",mp=n=>n==="length"||n==="size"||n==="bg-size",pp=n=>n==="length",Jx=n=>n==="number",Fx=n=>n==="family-name",gp=n=>n==="shadow",Px=()=>{const n=st("color"),u=st("font"),r=st("text"),c=st("font-weight"),d=st("tracking"),m=st("leading"),f=st("breakpoint"),v=st("container"),p=st("spacing"),g=st("radius"),x=st("shadow"),b=st("inset-shadow"),j=st("text-shadow"),T=st("drop-shadow"),R=st("blur"),N=st("perspective"),S=st("aspect"),A=st("ease"),X=st("animate"),q=()=>["auto","avoid","all","avoid-page","page","left","right","column"],P=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],ee=()=>[...P(),ne,le],re=()=>["auto","hidden","clip","visible","scroll"],K=()=>["auto","contain","none"],V=()=>[ne,le,p],oe=()=>[rn,"full","auto",...V()],te=()=>[$a,"none","subgrid",ne,le],$=()=>["auto",{span:["full",$a,ne,le]},$a,ne,le],W=()=>[$a,"auto",ne,le],me=()=>["auto","min","max","fr",ne,le],be=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],ge=()=>["start","end","center","stretch","center-safe","end-safe"],C=()=>["auto",...V()],Q=()=>[rn,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...V()],U=()=>[n,ne,le],ue=()=>[...P(),Bm,Lm,{position:[ne,le]}],_=()=>["no-repeat",{repeat:["","x","y","space","round"]}],L=()=>["auto","cover","contain",Kx,Xx,{size:[ne,le]}],J=()=>[Vo,bs,vl],Z=()=>["","none","full",g,ne,le],F=()=>["",ye,bs,vl],se=()=>["solid","dashed","dotted","double"],I=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ce=()=>[ye,Vo,Bm,Lm],pe=()=>["","none",R,ne,le],_e=()=>["none",ye,ne,le],Ce=()=>["none",ye,ne,le],et=()=>[ye,ne,le],Ke=()=>[rn,"full",...V()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Sa],breakpoint:[Sa],color:[qx],container:[Sa],"drop-shadow":[Sa],ease:["in","out","in-out"],font:[Yx],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Sa],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Sa],shadow:[Sa],spacing:["px",ye],text:[Sa],"text-shadow":[Sa],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",rn,le,ne,S]}],container:["container"],columns:[{columns:[ye,le,ne,v]}],"break-after":[{"break-after":q()}],"break-before":[{"break-before":q()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:ee()}],overflow:[{overflow:re()}],"overflow-x":[{"overflow-x":re()}],"overflow-y":[{"overflow-y":re()}],overscroll:[{overscroll:K()}],"overscroll-x":[{"overscroll-x":K()}],"overscroll-y":[{"overscroll-y":K()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:oe()}],"inset-x":[{"inset-x":oe()}],"inset-y":[{"inset-y":oe()}],start:[{start:oe()}],end:[{end:oe()}],top:[{top:oe()}],right:[{right:oe()}],bottom:[{bottom:oe()}],left:[{left:oe()}],visibility:["visible","invisible","collapse"],z:[{z:[$a,"auto",ne,le]}],basis:[{basis:[rn,"full","auto",v,...V()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ye,rn,"auto","initial","none",le]}],grow:[{grow:["",ye,ne,le]}],shrink:[{shrink:["",ye,ne,le]}],order:[{order:[$a,"first","last","none",ne,le]}],"grid-cols":[{"grid-cols":te()}],"col-start-end":[{col:$()}],"col-start":[{"col-start":W()}],"col-end":[{"col-end":W()}],"grid-rows":[{"grid-rows":te()}],"row-start-end":[{row:$()}],"row-start":[{"row-start":W()}],"row-end":[{"row-end":W()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":me()}],"auto-rows":[{"auto-rows":me()}],gap:[{gap:V()}],"gap-x":[{"gap-x":V()}],"gap-y":[{"gap-y":V()}],"justify-content":[{justify:[...be(),"normal"]}],"justify-items":[{"justify-items":[...ge(),"normal"]}],"justify-self":[{"justify-self":["auto",...ge()]}],"align-content":[{content:["normal",...be()]}],"align-items":[{items:[...ge(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...ge(),{baseline:["","last"]}]}],"place-content":[{"place-content":be()}],"place-items":[{"place-items":[...ge(),"baseline"]}],"place-self":[{"place-self":["auto",...ge()]}],p:[{p:V()}],px:[{px:V()}],py:[{py:V()}],ps:[{ps:V()}],pe:[{pe:V()}],pt:[{pt:V()}],pr:[{pr:V()}],pb:[{pb:V()}],pl:[{pl:V()}],m:[{m:C()}],mx:[{mx:C()}],my:[{my:C()}],ms:[{ms:C()}],me:[{me:C()}],mt:[{mt:C()}],mr:[{mr:C()}],mb:[{mb:C()}],ml:[{ml:C()}],"space-x":[{"space-x":V()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":V()}],"space-y-reverse":["space-y-reverse"],size:[{size:Q()}],w:[{w:[v,"screen",...Q()]}],"min-w":[{"min-w":[v,"screen","none",...Q()]}],"max-w":[{"max-w":[v,"screen","none","prose",{screen:[f]},...Q()]}],h:[{h:["screen","lh",...Q()]}],"min-h":[{"min-h":["screen","lh","none",...Q()]}],"max-h":[{"max-h":["screen","lh",...Q()]}],"font-size":[{text:["base",r,bs,vl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[c,ne,Go]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Vo,le]}],"font-family":[{font:[Zx,le,u]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[d,ne,le]}],"line-clamp":[{"line-clamp":[ye,"none",ne,Go]}],leading:[{leading:[m,...V()]}],"list-image":[{"list-image":["none",ne,le]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ne,le]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:U()}],"text-color":[{text:U()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...se(),"wavy"]}],"text-decoration-thickness":[{decoration:[ye,"from-font","auto",ne,vl]}],"text-decoration-color":[{decoration:U()}],"underline-offset":[{"underline-offset":[ye,"auto",ne,le]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:V()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ne,le]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ne,le]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ue()}],"bg-repeat":[{bg:_()}],"bg-size":[{bg:L()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},$a,ne,le],radial:["",ne,le],conic:[$a,ne,le]},$x,Qx]}],"bg-color":[{bg:U()}],"gradient-from-pos":[{from:J()}],"gradient-via-pos":[{via:J()}],"gradient-to-pos":[{to:J()}],"gradient-from":[{from:U()}],"gradient-via":[{via:U()}],"gradient-to":[{to:U()}],rounded:[{rounded:Z()}],"rounded-s":[{"rounded-s":Z()}],"rounded-e":[{"rounded-e":Z()}],"rounded-t":[{"rounded-t":Z()}],"rounded-r":[{"rounded-r":Z()}],"rounded-b":[{"rounded-b":Z()}],"rounded-l":[{"rounded-l":Z()}],"rounded-ss":[{"rounded-ss":Z()}],"rounded-se":[{"rounded-se":Z()}],"rounded-ee":[{"rounded-ee":Z()}],"rounded-es":[{"rounded-es":Z()}],"rounded-tl":[{"rounded-tl":Z()}],"rounded-tr":[{"rounded-tr":Z()}],"rounded-br":[{"rounded-br":Z()}],"rounded-bl":[{"rounded-bl":Z()}],"border-w":[{border:F()}],"border-w-x":[{"border-x":F()}],"border-w-y":[{"border-y":F()}],"border-w-s":[{"border-s":F()}],"border-w-e":[{"border-e":F()}],"border-w-t":[{"border-t":F()}],"border-w-r":[{"border-r":F()}],"border-w-b":[{"border-b":F()}],"border-w-l":[{"border-l":F()}],"divide-x":[{"divide-x":F()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":F()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...se(),"hidden","none"]}],"divide-style":[{divide:[...se(),"hidden","none"]}],"border-color":[{border:U()}],"border-color-x":[{"border-x":U()}],"border-color-y":[{"border-y":U()}],"border-color-s":[{"border-s":U()}],"border-color-e":[{"border-e":U()}],"border-color-t":[{"border-t":U()}],"border-color-r":[{"border-r":U()}],"border-color-b":[{"border-b":U()}],"border-color-l":[{"border-l":U()}],"divide-color":[{divide:U()}],"outline-style":[{outline:[...se(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ye,ne,le]}],"outline-w":[{outline:["",ye,bs,vl]}],"outline-color":[{outline:U()}],shadow:[{shadow:["","none",x,Ki,Zi]}],"shadow-color":[{shadow:U()}],"inset-shadow":[{"inset-shadow":["none",b,Ki,Zi]}],"inset-shadow-color":[{"inset-shadow":U()}],"ring-w":[{ring:F()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:U()}],"ring-offset-w":[{"ring-offset":[ye,vl]}],"ring-offset-color":[{"ring-offset":U()}],"inset-ring-w":[{"inset-ring":F()}],"inset-ring-color":[{"inset-ring":U()}],"text-shadow":[{"text-shadow":["none",j,Ki,Zi]}],"text-shadow-color":[{"text-shadow":U()}],opacity:[{opacity:[ye,ne,le]}],"mix-blend":[{"mix-blend":[...I(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":I()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ye]}],"mask-image-linear-from-pos":[{"mask-linear-from":ce()}],"mask-image-linear-to-pos":[{"mask-linear-to":ce()}],"mask-image-linear-from-color":[{"mask-linear-from":U()}],"mask-image-linear-to-color":[{"mask-linear-to":U()}],"mask-image-t-from-pos":[{"mask-t-from":ce()}],"mask-image-t-to-pos":[{"mask-t-to":ce()}],"mask-image-t-from-color":[{"mask-t-from":U()}],"mask-image-t-to-color":[{"mask-t-to":U()}],"mask-image-r-from-pos":[{"mask-r-from":ce()}],"mask-image-r-to-pos":[{"mask-r-to":ce()}],"mask-image-r-from-color":[{"mask-r-from":U()}],"mask-image-r-to-color":[{"mask-r-to":U()}],"mask-image-b-from-pos":[{"mask-b-from":ce()}],"mask-image-b-to-pos":[{"mask-b-to":ce()}],"mask-image-b-from-color":[{"mask-b-from":U()}],"mask-image-b-to-color":[{"mask-b-to":U()}],"mask-image-l-from-pos":[{"mask-l-from":ce()}],"mask-image-l-to-pos":[{"mask-l-to":ce()}],"mask-image-l-from-color":[{"mask-l-from":U()}],"mask-image-l-to-color":[{"mask-l-to":U()}],"mask-image-x-from-pos":[{"mask-x-from":ce()}],"mask-image-x-to-pos":[{"mask-x-to":ce()}],"mask-image-x-from-color":[{"mask-x-from":U()}],"mask-image-x-to-color":[{"mask-x-to":U()}],"mask-image-y-from-pos":[{"mask-y-from":ce()}],"mask-image-y-to-pos":[{"mask-y-to":ce()}],"mask-image-y-from-color":[{"mask-y-from":U()}],"mask-image-y-to-color":[{"mask-y-to":U()}],"mask-image-radial":[{"mask-radial":[ne,le]}],"mask-image-radial-from-pos":[{"mask-radial-from":ce()}],"mask-image-radial-to-pos":[{"mask-radial-to":ce()}],"mask-image-radial-from-color":[{"mask-radial-from":U()}],"mask-image-radial-to-color":[{"mask-radial-to":U()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":P()}],"mask-image-conic-pos":[{"mask-conic":[ye]}],"mask-image-conic-from-pos":[{"mask-conic-from":ce()}],"mask-image-conic-to-pos":[{"mask-conic-to":ce()}],"mask-image-conic-from-color":[{"mask-conic-from":U()}],"mask-image-conic-to-color":[{"mask-conic-to":U()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ue()}],"mask-repeat":[{mask:_()}],"mask-size":[{mask:L()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ne,le]}],filter:[{filter:["","none",ne,le]}],blur:[{blur:pe()}],brightness:[{brightness:[ye,ne,le]}],contrast:[{contrast:[ye,ne,le]}],"drop-shadow":[{"drop-shadow":["","none",T,Ki,Zi]}],"drop-shadow-color":[{"drop-shadow":U()}],grayscale:[{grayscale:["",ye,ne,le]}],"hue-rotate":[{"hue-rotate":[ye,ne,le]}],invert:[{invert:["",ye,ne,le]}],saturate:[{saturate:[ye,ne,le]}],sepia:[{sepia:["",ye,ne,le]}],"backdrop-filter":[{"backdrop-filter":["","none",ne,le]}],"backdrop-blur":[{"backdrop-blur":pe()}],"backdrop-brightness":[{"backdrop-brightness":[ye,ne,le]}],"backdrop-contrast":[{"backdrop-contrast":[ye,ne,le]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ye,ne,le]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ye,ne,le]}],"backdrop-invert":[{"backdrop-invert":["",ye,ne,le]}],"backdrop-opacity":[{"backdrop-opacity":[ye,ne,le]}],"backdrop-saturate":[{"backdrop-saturate":[ye,ne,le]}],"backdrop-sepia":[{"backdrop-sepia":["",ye,ne,le]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":V()}],"border-spacing-x":[{"border-spacing-x":V()}],"border-spacing-y":[{"border-spacing-y":V()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ne,le]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ye,"initial",ne,le]}],ease:[{ease:["linear","initial",A,ne,le]}],delay:[{delay:[ye,ne,le]}],animate:[{animate:["none",X,ne,le]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[N,ne,le]}],"perspective-origin":[{"perspective-origin":ee()}],rotate:[{rotate:_e()}],"rotate-x":[{"rotate-x":_e()}],"rotate-y":[{"rotate-y":_e()}],"rotate-z":[{"rotate-z":_e()}],scale:[{scale:Ce()}],"scale-x":[{"scale-x":Ce()}],"scale-y":[{"scale-y":Ce()}],"scale-z":[{"scale-z":Ce()}],"scale-3d":["scale-3d"],skew:[{skew:et()}],"skew-x":[{"skew-x":et()}],"skew-y":[{"skew-y":et()}],transform:[{transform:[ne,le,"","none","gpu","cpu"]}],"transform-origin":[{origin:ee()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ke()}],"translate-x":[{"translate-x":Ke()}],"translate-y":[{"translate-y":Ke()}],"translate-z":[{"translate-z":Ke()}],"translate-none":["translate-none"],accent:[{accent:U()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:U()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ne,le]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":V()}],"scroll-mx":[{"scroll-mx":V()}],"scroll-my":[{"scroll-my":V()}],"scroll-ms":[{"scroll-ms":V()}],"scroll-me":[{"scroll-me":V()}],"scroll-mt":[{"scroll-mt":V()}],"scroll-mr":[{"scroll-mr":V()}],"scroll-mb":[{"scroll-mb":V()}],"scroll-ml":[{"scroll-ml":V()}],"scroll-p":[{"scroll-p":V()}],"scroll-px":[{"scroll-px":V()}],"scroll-py":[{"scroll-py":V()}],"scroll-ps":[{"scroll-ps":V()}],"scroll-pe":[{"scroll-pe":V()}],"scroll-pt":[{"scroll-pt":V()}],"scroll-pr":[{"scroll-pr":V()}],"scroll-pb":[{"scroll-pb":V()}],"scroll-pl":[{"scroll-pl":V()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ne,le]}],fill:[{fill:["none",...U()]}],"stroke-w":[{stroke:[ye,bs,vl,Go]}],stroke:[{stroke:["none",...U()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Wx=Ax(Px);function Me(...n){return Wx(xx(n))}const vp=G.forwardRef(({className:n,items:u,collapsed:r=!1,onCollapse:c,variant:d="default",...m},f)=>{const p=xt().location.pathname,g={default:"bg-sidebar border-r border-sidebar-border",glass:"glass-subtle border-r border-white/10",modern:"bg-gradient-to-b from-sidebar to-sidebar/80 shadow-xl"};return s.jsxs("div",{ref:f,className:Me("flex flex-col h-full transition-all duration-300 ease-in-out",r?"w-16":"w-64",g[d],n),...m,children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-sidebar-border",children:[!r&&s.jsx("h1",{className:"text-xl font-bold text-sidebar-foreground",children:"Apprenticeship Tracker"}),c&&s.jsx("button",{onClick:()=>c(!r),className:"p-2 rounded-lg hover:bg-sidebar-accent transition-colors",children:s.jsx("svg",{className:Me("w-4 h-4 transition-transform duration-200",r?"rotate-180":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 19l-7-7 7-7m8 14l-7-7 7-7"})})})]}),s.jsx("nav",{className:"flex-1 p-4 space-y-2",children:u.map(x=>{const b=p===x.href;return s.jsxs(uu,{to:x.href,className:Me("flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200",b?"bg-sidebar-primary text-sidebar-primary-foreground shadow-md":"text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",r&&"justify-center"),children:[s.jsx("span",{className:"flex-shrink-0",children:x.icon}),!r&&s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"flex-1",children:x.label}),x.badge&&s.jsx("span",{className:"bg-sidebar-primary text-sidebar-primary-foreground text-xs rounded-full px-2 py-1 min-w-[1.5rem] text-center",children:x.badge})]})]},x.href)})}),s.jsx("div",{className:"p-4 border-t border-sidebar-border",children:s.jsxs("div",{className:Me("flex items-center space-x-3",r&&"justify-center"),children:[s.jsx("div",{className:"w-8 h-8 rounded-full bg-sidebar-primary flex items-center justify-center",children:s.jsx("span",{className:"text-sm font-medium text-sidebar-primary-foreground",children:"U"})}),!r&&s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-sm font-medium text-sidebar-foreground truncate",children:"User Name"}),s.jsx("p",{className:"text-xs text-sidebar-foreground/70 truncate",children:"Admin"})]})]})})]})});vp.displayName="Sidebar";const xp=G.forwardRef(({className:n,title:u="Dashboard",subtitle:r,actions:c,variant:d="default",showBreadcrumb:m=!1,breadcrumbItems:f=[],...v},p)=>{const g={default:"bg-background border-b border-border",glass:"glass-subtle border-b border-white/10",gradient:"gradient-aurora text-white"};return s.jsx("div",{ref:p,className:Me("sticky top-0 z-40 px-6 py-4 backdrop-blur-sm",g[d],n),...v,children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex-1 min-w-0",children:[m&&f.length>0&&s.jsx("nav",{className:"flex items-center space-x-2 text-sm text-muted-foreground mb-1",children:f.map((x,b)=>s.jsxs(G.Fragment,{children:[x.href?s.jsx("a",{href:x.href,className:"hover:text-foreground transition-colors",children:x.label}):s.jsx("span",{className:"text-foreground",children:x.label}),b<f.length-1&&s.jsx("span",{className:"text-muted-foreground/50",children:"/"})]},b))}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-foreground text-gradient",children:u}),r&&s.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:r})]})]}),c&&s.jsx("div",{className:"flex items-center space-x-2",children:c})]})})});xp.displayName="Header";const Ix=G.forwardRef(({className:n,variant:u="default",size:r="md",...c},d)=>{const m={default:"bg-primary text-primary-foreground hover:bg-primary/90",glass:"btn-glass",ghost:"hover:bg-accent hover:text-accent-foreground"},f={sm:"h-8 px-3 text-xs",md:"h-10 px-4 text-sm",lg:"h-12 px-6 text-base"};return s.jsx("button",{ref:d,className:Me("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",m[u],f[r],n),...c})});Ix.displayName="HeaderAction";const ey=G.forwardRef(({className:n,stats:u,...r},c)=>s.jsx("div",{ref:c,className:Me("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6",n),...r,children:u.map((d,m)=>s.jsx("div",{className:"glass-card p-4 rounded-xl hover:shadow-lg transition-all duration-200",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:d.label}),s.jsx("p",{className:"text-2xl font-bold text-foreground",children:d.value})]}),d.change!==void 0&&s.jsxs("div",{className:Me("flex items-center space-x-1 text-sm",d.variant==="success"&&"text-green-600",d.variant==="warning"&&"text-yellow-600",d.variant==="error"&&"text-red-600",!d.variant&&"text-muted-foreground"),children:[s.jsx("span",{children:d.change>0?"↗":d.change<0?"↘":"→"}),s.jsxs("span",{children:[Math.abs(d.change),"%"]})]})]})},m))}));ey.displayName="HeaderStats";const yp=G.forwardRef(({className:n,count:u=0,variant:r="default",...c},d)=>{const m={default:"hover:bg-accent hover:text-accent-foreground",glass:"btn-glass"};return s.jsxs("button",{ref:d,className:Me("relative p-2 rounded-lg transition-all duration-200",m[r],n),...c,children:[s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"})}),u>0&&s.jsx("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:u>99?"99+":u})]})});yp.displayName="NotificationBell";const bp=G.forwardRef(({className:n,variant:u="default",...r},c)=>{const[d,m]=G.useState(!1);G.useEffect(()=>{const p=document.documentElement.classList.contains("dark");m(p)},[]);const f=()=>{document.documentElement.classList.toggle("dark"),m(!d)},v={default:"hover:bg-accent hover:text-accent-foreground",glass:"btn-glass"};return s.jsx("button",{ref:c,onClick:f,className:Me("p-2 rounded-lg transition-all duration-200",v[u],n),...r,children:d?s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})}):s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})})})});bp.displayName="ThemeToggle";const Sp=G.createContext(void 0),jp=()=>{const n=G.useContext(Sp);if(!n)throw new Error("useToast must be used within a ToastProvider");return n},ty=({children:n})=>{const[u,r]=G.useState([]),c=G.useCallback(m=>{const f=Math.random().toString(36).substr(2,9),v={...m,id:f};r(p=>[...p,v]),setTimeout(()=>{r(p=>p.filter(g=>g.id!==f))},m.duration||5e3)},[]),d=G.useCallback(m=>{r(f=>f.filter(v=>v.id!==m))},[]);return s.jsxs(Sp.Provider,{value:{toasts:u,addToast:c,removeToast:d},children:[n,s.jsx(ay,{})]})},ay=()=>{const{toasts:n}=jp();return s.jsx("div",{className:"fixed top-4 right-4 z-50 flex flex-col space-y-2 max-w-sm",children:n.map(u=>s.jsx(ly,{toast:u},u.id))})},ly=({toast:n})=>{const{removeToast:u}=jp(),r={default:"bg-card border-border",success:"bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300",error:"bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-300",warning:"bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-300",info:"bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-300"},c={default:"ℹ️",success:"✅",error:"❌",warning:"⚠️",info:"ℹ️"};return s.jsx("div",{className:Me("glass-card p-4 border rounded-xl shadow-lg animate-slide-down",r[n.variant||"default"]),children:s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"flex-shrink-0 text-lg",children:c[n.variant||"default"]}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-sm font-medium",children:n.title}),n.description&&s.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:n.description})]}),s.jsxs("button",{onClick:()=>u(n.id),className:"flex-shrink-0 text-muted-foreground hover:text-foreground transition-colors",children:[s.jsx("span",{className:"sr-only",children:"Close"}),"✕"]})]})})};function ny(){const u=xt().location.pathname,[r,c]=G.useState(!1),d=[{label:"Dashboard",href:"/",icon:s.jsxs("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"}),s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 5a2 2 0 012-2h4a2 2 0 012 2v4H8V5z"})]})},{label:"Apprentices",href:"/apprentices",icon:s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"})}),badge:24},{label:"Reviews",href:"/reviews",icon:s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),badge:3},{label:"Exams",href:"/exams",icon:s.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})}),badge:7}],m=()=>{switch(u){case"/":return"Dashboard";case"/apprentices":return"Apprentices";case"/reviews":return"Reviews";case"/exams":return"Exams";default:return"Dashboard"}},f=()=>{switch(u){case"/":return"Overview of apprenticeship program metrics and key performance indicators";case"/apprentices":return"Manage and track apprentice progress and information";case"/reviews":return"Schedule and manage performance reviews";case"/exams":return"Track exam schedules and results";default:return""}};return s.jsx(ty,{children:s.jsxs("div",{className:"min-h-screen bg-background flex",children:[s.jsx(vp,{items:d,collapsed:r,onCollapse:c,variant:"modern"}),s.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[s.jsx(xp,{title:m(),subtitle:f(),variant:"glass",actions:s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(yp,{count:5,variant:"glass"}),s.jsx(bp,{variant:"glass"})]})}),s.jsx("main",{className:"flex-1 overflow-auto",children:s.jsx("div",{className:"container-modern section-padding",children:s.jsx("div",{className:"animate-fade-in",children:s.jsx(sp,{})})})})]}),!1]})})}const Re=G.forwardRef(({className:n,variant:u="default",hover:r=!0,...c},d)=>{const m="rounded-2xl text-card-foreground transition-all duration-300",f={default:"border bg-card shadow-md",glass:"glass-card border-white/10",gradient:"gradient-cosmic text-white shadow-xl",modern:"card-modern shadow-float",neumorphism:"neomorphism border-none"},v=r?"hover:shadow-lg hover:scale-[1.02]":"";return s.jsx("div",{ref:d,className:Me(m,f[u],v,n),...c})});Re.displayName="Card";const Oe=G.forwardRef(({className:n,...u},r)=>s.jsx("div",{ref:r,className:Me("flex flex-col space-y-1.5 p-6",n),...u}));Oe.displayName="CardHeader";const ke=G.forwardRef(({className:n,...u},r)=>s.jsx("h3",{ref:r,className:Me("font-semibold leading-none tracking-tight",n),...u}));ke.displayName="CardTitle";const sy=G.forwardRef(({className:n,...u},r)=>s.jsx("p",{ref:r,className:Me("text-sm text-muted-foreground",n),...u}));sy.displayName="CardDescription";const Ee=G.forwardRef(({className:n,...u},r)=>s.jsx("div",{ref:r,className:Me("p-6 pt-0",n),...u}));Ee.displayName="CardContent";const iy=G.forwardRef(({className:n,...u},r)=>s.jsx("div",{ref:r,className:Me("flex items-center p-6 pt-0",n),...u}));iy.displayName="CardFooter";const Ns=G.forwardRef(({className:n,value:u=0,max:r=100,variant:c="default",size:d="md",showValue:m=!1,animated:f=!0,...v},p)=>{const g=Math.min(Math.max(u/r*100,0),100),x={sm:"h-2",md:"h-3",lg:"h-4"},b={default:"bg-secondary",gradient:"bg-gradient-to-r from-primary to-secondary",glass:"glass-subtle",neon:"bg-primary shadow-neon"},j={default:"bg-primary",gradient:"gradient-primary",glass:"glass bg-primary/50",neon:"bg-primary shadow-neon-sm"};return s.jsxs("div",{ref:p,className:Me("relative w-full rounded-full overflow-hidden",x[d],b[c],n),...v,children:[s.jsx("div",{className:Me("h-full transition-all duration-500 ease-out rounded-full",j[c],f&&"animate-pulse"),style:{width:`${g}%`}}),m&&s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:s.jsxs("span",{className:"text-xs font-medium text-white drop-shadow-sm",children:[Math.round(g),"%"]})})]})});Ns.displayName="Progress";const Np=G.forwardRef(({className:n,value:u=0,max:r=100,size:c=120,strokeWidth:d=8,variant:m="default",showValue:f=!0,...v},p)=>{const g=Math.min(Math.max(u/r*100,0),100),x=(c-d)/2,b=2*Math.PI*x,j=b,T=b-g/100*b,R={default:"stroke-primary",gradient:"stroke-primary",neon:"stroke-primary drop-shadow-glow"};return s.jsxs("div",{ref:p,className:Me("relative flex items-center justify-center",n),style:{width:c,height:c},...v,children:[s.jsxs("svg",{className:"transform -rotate-90",width:c,height:c,children:[s.jsx("circle",{cx:c/2,cy:c/2,r:x,stroke:"currentColor",strokeWidth:d,fill:"none",className:"text-muted opacity-20"}),s.jsx("circle",{cx:c/2,cy:c/2,r:x,stroke:"currentColor",strokeWidth:d,fill:"none",strokeDasharray:j,strokeDashoffset:T,strokeLinecap:"round",className:Me("transition-all duration-500 ease-out",R[m])})]}),f&&s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:s.jsxs("span",{className:"text-2xl font-bold text-foreground",children:[Math.round(g),"%"]})})]})});Np.displayName="CircularProgress";const ry=G.forwardRef(({className:n,steps:u,variant:r="default",...c},d)=>{const m={default:"",modern:"glass-subtle p-4 rounded-2xl",glass:"glass-card p-6"};return s.jsx("div",{ref:d,className:Me("flex items-center space-x-4",m[r],n),...c,children:u.map((f,v)=>s.jsxs(G.Fragment,{children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:Me("w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300",f.completed?"bg-primary text-primary-foreground":f.current?"bg-primary/20 text-primary border-2 border-primary":"bg-muted text-muted-foreground"),children:f.completed?"✓":v+1}),s.jsx("span",{className:Me("text-sm font-medium",f.completed?"text-primary":f.current?"text-foreground":"text-muted-foreground"),children:f.label})]}),v<u.length-1&&s.jsx("div",{className:Me("h-0.5 bg-muted rounded-full flex-1 transition-all duration-300",f.completed?"bg-primary":"")})]},v))})});ry.displayName="StepProgress";const Ie=G.forwardRef(({className:n,variant:u="shimmer",rounded:r="md",...c},d)=>{const m="bg-muted animate-pulse",f={default:"animate-pulse",shimmer:"loading-shimmer relative overflow-hidden",pulse:"loading-pulse",wave:"animate-bounce-in"},v={none:"rounded-none",sm:"rounded-sm",md:"rounded-md",lg:"rounded-lg",full:"rounded-full"};return s.jsx("div",{ref:d,className:Me(m,f[u],v[r],n),...c})});Ie.displayName="Skeleton";const Pi=G.forwardRef(({className:n,...u},r)=>s.jsxs("div",{ref:r,className:Me("rounded-2xl border bg-card p-6 space-y-4",n),...u,children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx(Ie,{className:"h-4 w-3/4"}),s.jsx(Ie,{className:"h-3 w-1/2"})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(Ie,{className:"h-3 w-full"}),s.jsx(Ie,{className:"h-3 w-5/6"}),s.jsx(Ie,{className:"h-3 w-2/3"})]}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx(Ie,{className:"h-8 w-16"}),s.jsx(Ie,{className:"h-8 w-20"})]})]}));Pi.displayName="SkeletonCard";const cy=G.forwardRef(({className:n,count:u=3,...r},c)=>s.jsx("div",{ref:c,className:Me("space-y-4",n),...r,children:Array.from({length:u}).map((d,m)=>s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx(Ie,{className:"h-10 w-10",rounded:"full"}),s.jsxs("div",{className:"space-y-2 flex-1",children:[s.jsx(Ie,{className:"h-4 w-full"}),s.jsx(Ie,{className:"h-3 w-3/4"})]})]},m))}));cy.displayName="SkeletonList";const oy=G.forwardRef(({className:n,...u},r)=>s.jsxs("div",{ref:r,className:Me("rounded-2xl border bg-card p-6 space-y-4",n),...u,children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx(Ie,{className:"h-6 w-1/3"}),s.jsx(Ie,{className:"h-4 w-1/2"})]}),s.jsx("div",{className:"space-y-2",children:s.jsxs("div",{className:"flex items-end space-x-2 h-32",children:[s.jsx(Ie,{className:"h-12 w-8"}),s.jsx(Ie,{className:"h-20 w-8"}),s.jsx(Ie,{className:"h-16 w-8"}),s.jsx(Ie,{className:"h-24 w-8"}),s.jsx(Ie,{className:"h-18 w-8"}),s.jsx(Ie,{className:"h-28 w-8"})]})})]}));oy.displayName="SkeletonChart";const uy=G.forwardRef(({className:n,rows:u=5,columns:r=4,...c},d)=>s.jsxs("div",{ref:d,className:Me("rounded-2xl border bg-card overflow-hidden",n),...c,children:[s.jsx("div",{className:"p-4 border-b bg-muted/50",children:s.jsx("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${r}, 1fr)`},children:Array.from({length:r}).map((m,f)=>s.jsx(Ie,{className:"h-4 w-full"},f))})}),s.jsx("div",{className:"p-4",children:s.jsx("div",{className:"space-y-3",children:Array.from({length:u}).map((m,f)=>s.jsx("div",{className:"grid gap-4",style:{gridTemplateColumns:`repeat(${r}, 1fr)`},children:Array.from({length:r}).map((v,p)=>s.jsx(Ie,{className:"h-4 w-full"},p))},f))})})]}));uy.displayName="SkeletonTable";const dy=({data:n,title:u,className:r})=>{const c=Math.max(...n.map(d=>d.value));return s.jsxs(Re,{className:r,children:[s.jsx(Oe,{children:s.jsx(ke,{children:u})}),s.jsx(Ee,{children:s.jsx("div",{className:"space-y-3",children:n.map((d,m)=>s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-20 text-sm text-gray-600 truncate",children:d.label}),s.jsxs("div",{className:"flex-1 flex items-center space-x-2",children:[s.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-3",children:s.jsx("div",{className:"h-3 rounded-full transition-all duration-300",style:{width:`${d.value/c*100}%`,backgroundColor:d.color||"#3B82F6"}})}),s.jsx("div",{className:"w-8 text-sm font-medium text-gray-800",children:d.value})]})]},m))})})]})},fy=({data:n,title:u,className:r})=>{const c=n.reduce((m,f)=>m+f.value,0),d=["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6","#06B6D4"];return s.jsxs(Re,{className:r,children:[s.jsx(Oe,{children:s.jsx(ke,{children:u})}),s.jsx(Ee,{children:s.jsx("div",{className:"flex items-center justify-center space-x-6",children:s.jsx("div",{className:"space-y-2",children:n.map((m,f)=>{const v=(m.value/c*100).toFixed(1);return s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-4 h-4 rounded-full",style:{backgroundColor:m.color||d[f%d.length]}}),s.jsx("span",{className:"text-sm text-gray-600",children:m.label}),s.jsxs("span",{className:"text-sm font-medium",children:[v,"%"]})]},f)})})})})]})},hy=({data:n,title:u,className:r})=>{const c=Math.max(...n.map(f=>f.value)),d=Math.min(...n.map(f=>f.value)),m=c-d;return s.jsxs(Re,{className:r,children:[s.jsx(Oe,{children:s.jsx(ke,{children:u})}),s.jsx(Ee,{children:s.jsxs("div",{className:"h-48 relative",children:[s.jsxs("svg",{width:"100%",height:"100%",viewBox:"0 0 400 150",className:"overflow-visible",children:[[0,1,2,3,4].map(f=>s.jsx("line",{x1:"0",y1:f*30,x2:"400",y2:f*30,stroke:"#e5e7eb",strokeWidth:"1"},f)),s.jsx("polyline",{fill:"none",stroke:"#3B82F6",strokeWidth:"2",points:n.map((f,v)=>{const p=v/(n.length-1)*400,g=150-(f.value-d)/m*150;return`${p},${g}`}).join(" ")}),n.map((f,v)=>{const p=v/(n.length-1)*400,g=150-(f.value-d)/m*150;return s.jsx("circle",{cx:p,cy:g,r:"3",fill:"#3B82F6"},v)})]}),s.jsx("div",{className:"absolute bottom-0 left-0 right-0 flex justify-between text-xs text-gray-500",children:n.map((f,v)=>s.jsx("span",{children:f.label},v))})]})})]})},$i=({title:n,value:u,change:r,changeType:c="neutral",icon:d,className:m})=>{const f=()=>{switch(c){case"positive":return"text-green-600";case"negative":return"text-red-600";default:return"text-gray-600"}};return s.jsx(Re,{className:m,children:s.jsx(Ee,{className:"p-6",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-500",children:n}),s.jsx("p",{className:"text-2xl font-bold",children:u}),r&&s.jsx("p",{className:`text-sm ${f()}`,children:r})]}),d&&s.jsx("div",{className:"text-gray-400",children:d})]})})})},qm=n=>{let u;const r=new Set,c=(g,x)=>{const b=typeof g=="function"?g(u):g;if(!Object.is(b,u)){const j=u;u=x??(typeof b!="object"||b===null)?b:Object.assign({},u,b),r.forEach(T=>T(u,j))}},d=()=>u,v={setState:c,getState:d,getInitialState:()=>p,subscribe:g=>(r.add(g),()=>r.delete(g))},p=u=n(c,d,v);return v},my=n=>n?qm(n):qm,py=n=>n;function gy(n,u=py){const r=Kt.useSyncExternalStore(n.subscribe,()=>u(n.getState()),()=>u(n.getInitialState()));return Kt.useDebugValue(r),r}const vy=n=>{const u=my(n),r=c=>gy(u,c);return Object.assign(r,u),r},fu=n=>vy,Hm={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1},Rs=new Map,Ji=n=>{const u=Rs.get(n);return u?Object.fromEntries(Object.entries(u.stores).map(([r,c])=>[r,c.getState()])):{}},xy=(n,u,r)=>{if(n===void 0)return{type:"untracked",connection:u.connect(r)};const c=Rs.get(r.name);if(c)return{type:"tracked",store:n,...c};const d={connection:u.connect(r),stores:{}};return Rs.set(r.name,d),{type:"tracked",store:n,...d}},yy=(n,u)=>{if(u===void 0)return;const r=Rs.get(n);r&&(delete r.stores[u],Object.keys(r.stores).length===0&&Rs.delete(n))},by=n=>{var u,r;if(!n)return;const c=n.split(`
`),d=c.findIndex(f=>f.includes("api.setState"));if(d<0)return;const m=((u=c[d+1])==null?void 0:u.trim())||"";return(r=/.+ (.+) .+/.exec(m))==null?void 0:r[1]},Sy=(n,u={})=>(r,c,d)=>{const{enabled:m,anonymousActionType:f,store:v,...p}=u;let g;try{g=(m??(Hm?"production":void 0)!=="production")&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!g)return n(r,c,d);const{connection:x,...b}=xy(v,g,p);let j=!0;d.setState=(N,S,A)=>{const X=r(N,S);if(!j)return X;const q=A===void 0?{type:f||by(new Error().stack)||"anonymous"}:typeof A=="string"?{type:A}:A;return v===void 0?(x?.send(q,c()),X):(x?.send({...q,type:`${v}/${q.type}`},{...Ji(p.name),[v]:d.getState()}),X)},d.devtools={cleanup:()=>{x&&typeof x.unsubscribe=="function"&&x.unsubscribe(),yy(p.name,v)}};const T=(...N)=>{const S=j;j=!1,r(...N),j=S},R=n(d.setState,c,d);if(b.type==="untracked"?x?.init(R):(b.stores[b.store]=d,x?.init(Object.fromEntries(Object.entries(b.stores).map(([N,S])=>[N,N===b.store?R:S.getState()])))),d.dispatchFromDevtools&&typeof d.dispatch=="function"){let N=!1;const S=d.dispatch;d.dispatch=(...A)=>{(Hm?"production":void 0)!=="production"&&A[0].type==="__setState"&&!N&&(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),N=!0),S(...A)}}return x.subscribe(N=>{var S;switch(N.type){case"ACTION":if(typeof N.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return Yo(N.payload,A=>{if(A.type==="__setState"){if(v===void 0){T(A.state);return}Object.keys(A.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const X=A.state[v];if(X==null)return;JSON.stringify(d.getState())!==JSON.stringify(X)&&T(X);return}d.dispatchFromDevtools&&typeof d.dispatch=="function"&&d.dispatch(A)});case"DISPATCH":switch(N.payload.type){case"RESET":return T(R),v===void 0?x?.init(d.getState()):x?.init(Ji(p.name));case"COMMIT":if(v===void 0){x?.init(d.getState());return}return x?.init(Ji(p.name));case"ROLLBACK":return Yo(N.state,A=>{if(v===void 0){T(A),x?.init(d.getState());return}T(A[v]),x?.init(Ji(p.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return Yo(N.state,A=>{if(v===void 0){T(A);return}JSON.stringify(d.getState())!==JSON.stringify(A[v])&&T(A[v])});case"IMPORT_STATE":{const{nextLiftedState:A}=N.payload,X=(S=A.computedStates.slice(-1)[0])==null?void 0:S.state;if(!X)return;T(v===void 0?X:X[v]),x?.send(null,A);return}case"PAUSE_RECORDING":return j=!j}return}}),R},hu=Sy,Yo=(n,u)=>{let r;try{r=JSON.parse(n)}catch(c){console.error("[zustand devtools middleware] Could not parse the received json",c)}r!==void 0&&u(r)},jy={stats:null,analytics:null,loading:!1,error:null},Ny=fu()(hu((n,u)=>({...jy,setStats:r=>n({stats:r}),setAnalytics:r=>n({analytics:r}),setLoading:r=>n({loading:r}),setError:r=>n({error:r}),fetchStats:async()=>{n({loading:!0,error:null});try{const r=await fetch("/api/dashboard/stats");if(!r.ok)throw new Error(`HTTP error! status: ${r.status}`);const c=await r.json();n({stats:c.data,loading:!1})}catch(r){const c=r instanceof Error?r.message:"Failed to fetch dashboard stats";console.error("Dashboard stats fetch error:",r),n({error:c,loading:!1})}},fetchAnalytics:async()=>{n({loading:!0,error:null});try{const r=await fetch("/api/analytics/dashboard");if(!r.ok)throw new Error(`HTTP error! status: ${r.status}`);const c=await r.json();n({analytics:c.data,loading:!1})}catch(r){const c=r instanceof Error?r.message:"Failed to fetch analytics data";console.error("Dashboard analytics fetch error:",r),n({error:c,loading:!1})}},refreshStats:async()=>{await u().fetchStats()},refreshAnalytics:async()=>{await u().fetchAnalytics()}}),{name:"dashboard-store"}));function wy(){const{stats:n,loading:u,error:r,fetchStats:c}=Ny();if(G.useEffect(()=>{c()},[c]),u)return s.jsxs("div",{className:"space-y-6",children:[s.jsx("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4",children:Array.from({length:8}).map((p,g)=>s.jsx(Pi,{},g))}),s.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[s.jsx(Pi,{}),s.jsx(Pi,{})]})]});if(r)return s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsxs(Re,{variant:"glass",className:"p-8 text-center",children:[s.jsx("div",{className:"text-6xl mb-4",children:"⚠️"}),s.jsx("h2",{className:"text-2xl font-bold text-destructive mb-2",children:"Error"}),s.jsx("p",{className:"text-muted-foreground",children:r})]})});if(!n)return s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsxs(Re,{variant:"glass",className:"p-8 text-center",children:[s.jsx("div",{className:"text-6xl mb-4",children:"📊"}),s.jsx("h2",{className:"text-2xl font-bold mb-2",children:"No Data Available"}),s.jsx("p",{className:"text-muted-foreground",children:"Please check back later"})]})});const d=[{title:"Total Apprentices",value:n.totalApprentices,description:"All registered apprentices",icon:"👥",variant:"default",trend:"+12%"},{title:"Active Apprentices",value:n.activeApprentices,description:"Currently enrolled",icon:"🎓",variant:"glass",trend:"+5%"},{title:"Completed Programs",value:n.completedApprentices,description:"Successfully finished",icon:"🏆",variant:"modern",trend:"+8%"},{title:"Average Progress",value:`${n.averageProgress}%`,description:"Overall completion rate",icon:"📈",variant:"glass",progress:n.averageProgress},{title:"Upcoming Reviews",value:n.upcomingReviews,description:"Scheduled this quarter",icon:"📋",variant:"default",trend:"+2%"},{title:"Upcoming Exams",value:n.upcomingExams,description:"This month",icon:"📝",variant:"modern",trend:"+3%"},{title:"Pass Rate",value:`${n.passRate}%`,description:"Last quarter average",icon:"✅",variant:"glass",progress:n.passRate},{title:"Overdue Reviews",value:n.overdueReviews,description:"Need attention",icon:"⚠️",variant:"default",urgent:!0}],m=[{label:"Technology",value:15,color:"#3B82F6"},{label:"Marketing",value:8,color:"#10B981"},{label:"Sales",value:12,color:"#F59E0B"},{label:"HR",value:5,color:"#EF4444"},{label:"Finance",value:7,color:"#8B5CF6"}],f=[{label:"Jan",value:65},{label:"Feb",value:72},{label:"Mar",value:78},{label:"Apr",value:75},{label:"May",value:82},{label:"Jun",value:88}],v=[{label:"JavaScript",value:25},{label:"React",value:18},{label:"Python",value:15},{label:"Node.js",value:12},{label:"Database",value:10},{label:"DevOps",value:8}];return s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{className:"relative overflow-hidden rounded-3xl gradient-aurora p-8 text-white",children:[s.jsxs("div",{className:"relative z-10",children:[s.jsx("h1",{className:"text-4xl font-bold mb-2",children:"Welcome back! 👋"}),s.jsx("p",{className:"text-xl opacity-90 mb-6",children:"Here's what's happening with your apprenticeship program today."}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-3 h-3 rounded-full bg-green-400 animate-pulse"}),s.jsx("span",{className:"text-sm",children:"All systems operational"})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-3 h-3 rounded-full bg-blue-400 animate-pulse"}),s.jsx("span",{className:"text-sm",children:"5 notifications"})]})]})]}),s.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm"})]}),s.jsx("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4",children:d.map((p,g)=>s.jsxs(Re,{variant:p.urgent?"default":p.variant,className:`hover-lift group ${p.urgent?"border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800":""}`,children:[s.jsx(Oe,{className:"pb-3",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-2xl",children:p.icon}),s.jsx(ke,{className:`text-sm font-medium ${p.urgent?"text-red-800 dark:text-red-300":"text-muted-foreground"}`,children:p.title})]}),p.trend&&s.jsx("div",{className:"text-xs text-green-600 dark:text-green-400 font-medium",children:p.trend})]})}),s.jsxs(Ee,{children:[s.jsx("div",{className:`text-3xl font-bold mb-2 ${p.urgent?"text-red-900 dark:text-red-300":"text-foreground"}`,children:p.value}),s.jsx("p",{className:`text-xs mb-3 ${p.urgent?"text-red-600 dark:text-red-400":"text-muted-foreground"}`,children:p.description}),p.progress&&s.jsx(Ns,{value:p.progress,variant:"gradient",className:"h-2",animated:!0})]})]},g))}),s.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[s.jsxs(Re,{variant:"glass",className:"lg:col-span-2",children:[s.jsx(Oe,{children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs(ke,{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-2xl",children:"📊"}),s.jsx("span",{children:"Recent Activity"})]}),s.jsx("button",{className:"btn-glass text-xs",children:"View All"})]})}),s.jsx(Ee,{children:s.jsx("div",{className:"space-y-4",children:[{name:"Emma Wilson",action:"completed Q3 review",time:"2 hours ago",type:"success",icon:"✅"},{name:"John Smith",action:"passed JavaScript exam",time:"1 day ago",type:"info",icon:"📝"},{name:"Sophie Taylor",action:"enrolled in new program",time:"3 days ago",type:"warning",icon:"🎓"},{name:"Michael Chen",action:"submitted final project",time:"5 days ago",type:"info",icon:"📋"}].map((p,g)=>s.jsxs("div",{className:"flex items-center space-x-4 p-3 rounded-xl hover:bg-muted/50 transition-colors group",children:[s.jsx("div",{className:"flex-shrink-0 text-2xl",children:p.icon}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsxs("p",{className:"text-sm font-medium text-foreground group-hover:text-primary transition-colors",children:[s.jsx("span",{className:"font-semibold",children:p.name})," ",p.action]}),s.jsx("p",{className:"text-xs text-muted-foreground",children:p.time})]}),s.jsx("div",{className:"flex-shrink-0",children:s.jsx("div",{className:`w-2 h-2 rounded-full ${p.type==="success"?"bg-green-500":p.type==="info"?"bg-blue-500":p.type==="warning"?"bg-yellow-500":"bg-gray-500"}`})})]},g))})})]}),s.jsxs(Re,{variant:"modern",children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-2xl",children:"🎯"}),s.jsx("span",{children:"Progress Overview"})]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"text-center",children:[s.jsx(Np,{value:n.averageProgress,size:120,variant:"gradient"}),s.jsx("p",{className:"text-sm text-muted-foreground mt-2",children:"Overall Progress"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"Completion Rate"}),s.jsxs("span",{className:"font-medium",children:[n.passRate,"%"]})]}),s.jsx(Ns,{value:n.passRate,variant:"gradient"})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"Engagement"}),s.jsx("span",{className:"font-medium",children:"87%"})]}),s.jsx(Ns,{value:87,variant:"default"})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"Satisfaction"}),s.jsx("span",{className:"font-medium",children:"92%"})]}),s.jsx(Ns,{value:92,variant:"gradient"})]})]})]})})]})]}),s.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[s.jsx(fy,{data:m,title:"Apprentices by Department",className:"lg:col-span-1"}),s.jsx(hy,{data:f,title:"Progress Trend (6 months)",className:"lg:col-span-2"})]}),s.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[s.jsx(dy,{data:v,title:"Skills Distribution"}),s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsx(ke,{children:"Key Metrics"})}),s.jsx(Ee,{children:s.jsxs("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2",children:[s.jsx($i,{title:"Completion Rate",value:`${n.passRate}%`,change:"+12% from last month",changeType:"positive",icon:s.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),s.jsx($i,{title:"Avg Score",value:"84.5",change:"+5.2 from last month",changeType:"positive",icon:s.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.196-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"})})}),s.jsx($i,{title:"Engagement",value:"92%",change:"+8% from last month",changeType:"positive",icon:s.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}),s.jsx($i,{title:"Retention",value:"96%",change:"+2% from last month",changeType:"positive",icon:s.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})})]})})]})]}),s.jsxs(Re,{variant:"glass",children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-2xl",children:"⏰"}),s.jsx("span",{children:"Upcoming Deadlines"})]})}),s.jsx(Ee,{children:s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[{title:"React Development Project",student:"John Smith",date:"Sep 1, 2024",status:"upcoming",priority:"high"},{title:"Digital Marketing Strategy",student:"Emma Wilson",date:"Aug 20, 2024",status:"upcoming",priority:"medium"},{title:"Q3 Performance Review",student:"Sophie Taylor",date:"Overdue",status:"overdue",priority:"urgent"}].map((p,g)=>s.jsxs("div",{className:`p-4 rounded-xl border-2 transition-all duration-200 hover:shadow-lg ${p.status==="overdue"?"border-red-200 bg-red-50 dark:bg-red-900/20":p.priority==="high"?"border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20":"border-border bg-card"}`,children:[s.jsxs("div",{className:"flex items-start justify-between mb-2",children:[s.jsx("h3",{className:"font-medium text-sm",children:p.title}),s.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${p.status==="overdue"?"bg-red-100 text-red-800":p.priority==="high"?"bg-yellow-100 text-yellow-800":"bg-blue-100 text-blue-800"}`,children:p.priority})]}),s.jsx("p",{className:"text-xs text-muted-foreground mb-2",children:p.student}),s.jsx("p",{className:`text-xs font-medium ${p.status==="overdue"?"text-red-600":"text-muted-foreground"}`,children:p.date})]},g))})})]})]})}const Ae=G.forwardRef(({className:n,variant:u="default",size:r="default",...c},d)=>{const m="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",f={default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},v={default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"};return s.jsx("button",{className:Me(m,f[u],v[r],n),ref:d,...c})});Ae.displayName="Button";const _y={apprentices:[],selectedApprentice:null,loading:!1,error:null,pagination:{page:1,pageSize:10,totalItems:0,totalPages:0},filters:{},sort:{field:"firstName",direction:"asc"}},wp=fu()(hu((n,u)=>({..._y,setApprentices:r=>n({apprentices:r}),setSelectedApprentice:r=>n({selectedApprentice:r}),addApprentice:r=>n(c=>({apprentices:[...c.apprentices,r]})),updateApprentice:(r,c)=>n(d=>({apprentices:d.apprentices.map(m=>m.id===r?{...m,...c}:m),selectedApprentice:d.selectedApprentice?.id===r?{...d.selectedApprentice,...c}:d.selectedApprentice})),removeApprentice:r=>n(c=>({apprentices:c.apprentices.filter(d=>d.id!==r),selectedApprentice:c.selectedApprentice?.id===r?null:c.selectedApprentice})),setLoading:r=>n({loading:r}),setError:r=>n({error:r}),setPagination:r=>n({pagination:r}),setFilters:r=>n({filters:r}),setSort:r=>n({sort:r}),resetFilters:()=>n({filters:{}}),fetchApprentices:async(r=1,c={},d)=>{n({loading:!0,error:null});try{const{pagination:m,sort:f}=u(),v=d||f,p=new URLSearchParams({page:r.toString(),pageSize:m.pageSize.toString(),sortField:v.field,sortDirection:v.direction,...Object.entries(c).reduce((b,[j,T])=>(T!=null&&(Array.isArray(T)?b[j]=T.join(","):typeof T=="object"&&T!==null?Object.entries(T).forEach(([R,N])=>{N!=null&&(b[`${j}.${R}`]=N.toString())}):b[j]=T.toString()),b),{})}),g=await fetch(`/api/apprentices?${p}`);if(!g.ok)throw new Error(`HTTP error! status: ${g.status}`);const x=await g.json();n({apprentices:x.data,pagination:x.pagination,filters:c,sort:v,loading:!1})}catch(m){const f=m instanceof Error?m.message:"Failed to fetch apprentices";console.error("Apprentices fetch error:",m),n({error:f,loading:!1})}},searchApprentices:async(r,c=1)=>{const d={...u().filters,searchTerm:r};await u().fetchApprentices(c,d)},fetchApprenticeById:async r=>{n({loading:!0,error:null});try{const c=await fetch(`/api/apprentices/${r}`);if(!c.ok)throw new Error(`HTTP error! status: ${c.status}`);const d=await c.json();n({selectedApprentice:d.data,loading:!1})}catch(c){const d=c instanceof Error?c.message:"Failed to fetch apprentice";console.error("Apprentice fetch error:",c),n({error:d,loading:!1})}},createApprentice:async r=>{n({loading:!0,error:null});try{const c=await fetch("/api/apprentices",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!c.ok)throw new Error(`HTTP error! status: ${c.status}`);const d=await c.json();n(m=>({apprentices:[...m.apprentices,d.data],loading:!1}))}catch(c){const d=c instanceof Error?c.message:"Failed to create apprentice";console.error("Apprentice create error:",c),n({error:d,loading:!1})}},updateApprenticeData:async(r,c)=>{n({loading:!0,error:null});try{const d=await fetch(`/api/apprentices/${r}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(c)});if(!d.ok)throw new Error(`HTTP error! status: ${d.status}`);const m=await d.json();u().updateApprentice(r,m.data),n({loading:!1})}catch(d){const m=d instanceof Error?d.message:"Failed to update apprentice";console.error("Apprentice update error:",d),n({error:m,loading:!1})}},deleteApprentice:async r=>{n({loading:!0,error:null});try{const c=await fetch(`/api/apprentices/${r}`,{method:"DELETE"});if(!c.ok)throw new Error(`HTTP error! status: ${c.status}`);u().removeApprentice(r),n({loading:!1})}catch(c){const d=c instanceof Error?c.message:"Failed to delete apprentice";console.error("Apprentice delete error:",c),n({error:d,loading:!1})}}}),{name:"apprentice-store"}));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ry=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),My=n=>n.replace(/^([A-Z])|[\s-_]+(\w)/g,(u,r,c)=>c?c.toUpperCase():r.toLowerCase()),Vm=n=>{const u=My(n);return u.charAt(0).toUpperCase()+u.slice(1)},_p=(...n)=>n.filter((u,r,c)=>!!u&&u.trim()!==""&&c.indexOf(u)===r).join(" ").trim(),Ty=n=>{for(const u in n)if(u.startsWith("aria-")||u==="role"||u==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ey={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cy=G.forwardRef(({color:n="currentColor",size:u=24,strokeWidth:r=2,absoluteStrokeWidth:c,className:d="",children:m,iconNode:f,...v},p)=>G.createElement("svg",{ref:p,...Ey,width:u,height:u,stroke:n,strokeWidth:c?Number(r)*24/Number(u):r,className:_p("lucide",d),...!m&&!Ty(v)&&{"aria-hidden":"true"},...v},[...f.map(([g,x])=>G.createElement(g,x)),...Array.isArray(m)?m:[m]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ue=(n,u)=>{const r=G.forwardRef(({className:c,...d},m)=>G.createElement(Cy,{ref:m,iconNode:u,className:_p(`lucide-${Ry(Vm(n))}`,`lucide-${n}`,c),...d}));return r.displayName=Vm(n),r};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dy=[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]],mu=Ue("award",Dy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ay=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],Gm=Ue("book-open",Ay);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zy=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],Oy=Ue("building",zy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ky=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],or=Ue("calendar",ky);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uy=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Rp=Ue("chevron-down",Uy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ly=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],By=Ue("chevron-left",Ly);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qy=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Hy=Ue("chevron-right",qy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vy=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],Gy=Ue("chevron-up",Vy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yy=[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]],Xy=Ue("chevrons-left",Yy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qy=[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]],Zy=Ue("chevrons-right",Qy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ky=[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]],$y=Ue("chevrons-up-down",Ky);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jy=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],Fy=Ue("circle-alert",Jy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Py=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],Wy=Ue("circle-check-big",Py);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Iy=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]],eb=Ue("circle-x",Iy);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tb=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],ab=Ue("clock",tb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lb=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],Ym=Ue("file-text",lb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nb=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],sb=Ue("funnel",nb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ib=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],Xm=Ue("mail",ib);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rb=[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]],cb=Ue("message-circle",rb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ob=[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]],pu=Ue("pen-line",ob);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ub=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],db=Ue("phone",ub);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fb=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],hb=Ue("search",fb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mb=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],pb=Ue("star",mb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gb=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]],au=Ue("target",gb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vb=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],gu=Ue("trash-2",vb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xb=[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]],yb=Ue("trending-down",xb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bb=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],Sb=Ue("trending-up",bb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jb=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],Mp=Ue("user",jb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nb=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],Xo=Ue("users",Nb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wb=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Qm=Ue("x",wb),_b=G.createContext(void 0),vn=()=>{const n=G.useContext(_b);if(!n)throw new Error("useModal must be used within a ModalProvider");return n},it=G.forwardRef(({className:n,type:u,label:r,error:c,helperText:d,leftIcon:m,rightIcon:f,...v},p)=>s.jsxs("div",{className:"space-y-1",children:[r&&s.jsx("label",{className:"block text-sm font-medium text-gray-700",children:r}),s.jsxs("div",{className:"relative",children:[m&&s.jsx("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",children:m}),s.jsx("input",{type:u,className:Me("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors","file:border-0 file:bg-transparent file:text-sm file:font-medium","placeholder:text-muted-foreground","focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring","disabled:cursor-not-allowed disabled:opacity-50",m&&"pl-9",f&&"pr-9",c&&"border-red-500 focus-visible:ring-red-500",n),ref:p,...v}),f&&s.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400",children:f})]}),c&&s.jsx("p",{className:"text-sm text-red-600",children:c}),d&&!c&&s.jsx("p",{className:"text-sm text-gray-500",children:d})]}));it.displayName="Input";const Ot=G.forwardRef(({className:n,label:u,error:r,helperText:c,options:d,placeholder:m,onChange:f,...v},p)=>{const g=x=>{f?.(x.target.value)};return s.jsxs("div",{className:"space-y-1",children:[u&&s.jsx("label",{className:"block text-sm font-medium text-gray-700",children:u}),s.jsxs("div",{className:"relative",children:[s.jsxs("select",{className:Me("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors","focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring","disabled:cursor-not-allowed disabled:opacity-50","appearance-none pr-8",r&&"border-red-500 focus-visible:ring-red-500",n),ref:p,onChange:g,...v,children:[m&&s.jsx("option",{value:"",disabled:!0,children:m}),d.map(x=>s.jsx("option",{value:x.value,disabled:x.disabled,children:x.label},x.value))]}),s.jsx(Rp,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none"})]}),r&&s.jsx("p",{className:"text-sm text-red-600",children:r}),c&&!r&&s.jsx("p",{className:"text-sm text-gray-500",children:c})]})});Ot.displayName="Select";const Ms=G.forwardRef(({className:n,label:u,error:r,helperText:c,onChange:d,format:m="date",...f},v)=>{const p=g=>{d?.(g.target.value)};return s.jsxs("div",{className:"space-y-1",children:[u&&s.jsx("label",{className:"block text-sm font-medium text-gray-700",children:u}),s.jsxs("div",{className:"relative",children:[s.jsx("input",{type:m,className:Me("flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors","focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring","disabled:cursor-not-allowed disabled:opacity-50","pr-9",r&&"border-red-500 focus-visible:ring-red-500",n),ref:v,onChange:p,...f}),s.jsx(or,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none"})]}),r&&s.jsx("p",{className:"text-sm text-red-600",children:r}),c&&!r&&s.jsx("p",{className:"text-sm text-gray-500",children:c})]})});Ms.displayName="DatePicker";class Rb{validationRules;constructor(u){this.validationRules=u}validate(u){const r={};return Object.keys(this.validationRules).forEach(c=>{const d=c,m=u[d],f=this.validationRules[d];if(f){if(f.required&&(!m||typeof m=="string"&&!m.trim())){r[d]=`${String(d)} is required`;return}if(!(!m&&!f.required)&&f.rules){for(const v of f.rules)if(!v.test(m)){r[d]=v.message;break}}}}),r}validateField(u,r){const c=this.validationRules[u];if(c){if(c.required&&(!r||typeof r=="string"&&!r.trim()))return`${String(u)} is required`;if(!(!r&&!c.required)&&c.rules){for(const d of c.rules)if(!d.test(r))return d.message}}}}const je={email:{test:n=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(n),message:"Please enter a valid email address"},phone:{test:n=>/^\+?[\d\s\-\(\)]+$/.test(n),message:"Please enter a valid phone number"},minLength:n=>({test:u=>u.length>=n,message:`Must be at least ${n} characters long`}),maxLength:n=>({test:u=>u.length<=n,message:`Must be no more than ${n} characters long`}),minValue:n=>({test:u=>u>=n,message:`Must be at least ${n}`}),maxValue:n=>({test:u=>u<=n,message:`Must be no more than ${n}`}),pattern:(n,u)=>({test:r=>n.test(r),message:u}),custom:(n,u)=>({test:n,message:u})};function vu(n,u){const[r,c]=G.useState(n),[d,m]=G.useState({}),[f,v]=G.useState({}),p=new Rb(u),g=G.useCallback((R,N)=>{const S=p.validateField(R,N);return m(A=>({...A,[R]:S})),!S},[p]),x=G.useCallback(()=>{const R=p.validate(r);return m(R),Object.keys(R).length===0},[r,p]),b=G.useCallback((R,N)=>{c(S=>({...S,[R]:N})),f[R]&&g(R,N)},[f,g]),j=G.useCallback(R=>{v(N=>({...N,[R]:!0})),g(R,r[R])},[r,g]),T=G.useCallback(()=>{c(n),m({}),v({})},[n]);return{data:r,errors:d,touched:f,updateField:b,touchField:j,validateForm:x,validateField:g,resetForm:T,isValid:Object.keys(d).length===0}}const Mb=[{value:"Technology",label:"Technology"},{value:"Marketing",label:"Marketing"},{value:"Sales",label:"Sales"},{value:"Human Resources",label:"Human Resources"},{value:"Finance",label:"Finance"},{value:"Operations",label:"Operations"}],Tb=[{value:"active",label:"Active"},{value:"completed",label:"Completed"},{value:"suspended",label:"Suspended"},{value:"withdrawn",label:"Withdrawn"}],Eb=[{value:"John Smith",label:"John Smith"},{value:"Sarah Johnson",label:"Sarah Johnson"},{value:"Mike Brown",label:"Mike Brown"},{value:"Emily Davis",label:"Emily Davis"},{value:"David Wilson",label:"David Wilson"}],Tp=({apprentice:n,onSubmit:u,onCancel:r,isLoading:c=!1})=>{const d={firstName:n?.firstName||"",lastName:n?.lastName||"",email:n?.email||"",phone:n?.phone||"",position:n?.position||"",department:n?.department||"",startDate:n?.startDate?n.startDate.split("T")[0]:"",endDate:n?.endDate?n.endDate.split("T")[0]:"",mentor:n?.mentor||"",status:n?.status||"active"},m={firstName:{required:!0,rules:[je.minLength(2),je.maxLength(50)]},lastName:{required:!0,rules:[je.minLength(2),je.maxLength(50)]},email:{required:!0,rules:[je.email]},phone:{required:!0,rules:[je.phone]},position:{required:!0,rules:[je.minLength(2),je.maxLength(100)]},department:{required:!0},startDate:{required:!0,rules:[je.custom(N=>new Date(N)<=new Date,"Start date cannot be in the future")]},endDate:{required:!0,rules:[je.custom(N=>new Date(N)>new Date,"End date must be in the future")]},mentor:{required:!0},status:{required:!0}},{data:f,errors:v,touched:p,updateField:g,touchField:x,validateForm:b,isValid:j}=vu(d,m),T=N=>{if(N.preventDefault(),b()){const S={...f,startDate:new Date(f.startDate).toISOString(),endDate:new Date(f.endDate).toISOString(),...n?.id&&{id:n.id}};u(S)}},R=()=>{if(f.startDate&&f.endDate){const N=new Date(f.startDate);return new Date(f.endDate)>N}return!0};return s.jsxs("form",{onSubmit:T,className:"space-y-6 p-6",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx(it,{label:"First Name",value:f.firstName,onChange:N=>g("firstName",N.target.value),onBlur:()=>x("firstName"),error:p.firstName?v.firstName:void 0,disabled:c,required:!0}),s.jsx(it,{label:"Last Name",value:f.lastName,onChange:N=>g("lastName",N.target.value),onBlur:()=>x("lastName"),error:p.lastName?v.lastName:void 0,disabled:c,required:!0})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx(it,{label:"Email",type:"email",value:f.email,onChange:N=>g("email",N.target.value),onBlur:()=>x("email"),error:p.email?v.email:void 0,disabled:c,required:!0}),s.jsx(it,{label:"Phone",type:"tel",value:f.phone,onChange:N=>g("phone",N.target.value),onBlur:()=>x("phone"),error:p.phone?v.phone:void 0,disabled:c,required:!0})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx(it,{label:"Position",value:f.position,onChange:N=>g("position",N.target.value),onBlur:()=>x("position"),error:p.position?v.position:void 0,disabled:c,required:!0}),s.jsx(Ot,{label:"Department",options:Mb,value:f.department,onChange:N=>g("department",N),onBlur:()=>x("department"),error:p.department?v.department:void 0,disabled:c,required:!0})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx(Ms,{label:"Start Date",value:f.startDate,onChange:N=>g("startDate",N),onBlur:()=>x("startDate"),error:p.startDate?v.startDate:void 0,disabled:c,required:!0}),s.jsx(Ms,{label:"End Date",value:f.endDate,onChange:N=>g("endDate",N),onBlur:()=>x("endDate"),error:p.endDate?v.endDate:R()?void 0:"End date must be after start date",disabled:c,required:!0})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx(Ot,{label:"Mentor",options:Eb,value:f.mentor,onChange:N=>g("mentor",N),onBlur:()=>x("mentor"),error:p.mentor?v.mentor:void 0,disabled:c,required:!0}),s.jsx(Ot,{label:"Status",options:Tb,value:f.status,onChange:N=>g("status",N),onBlur:()=>x("status"),error:p.status?v.status:void 0,disabled:c,required:!0})]}),s.jsxs("div",{className:"flex justify-end space-x-3 pt-6 border-t",children:[s.jsx(Ae,{type:"button",variant:"outline",onClick:r,disabled:c,children:"Cancel"}),s.jsx(Ae,{type:"submit",disabled:!j||!R()||c,children:c?"Saving...":n?"Update Apprentice":"Create Apprentice"})]})]})},Cb=({apprentice:n,onClose:u})=>{const[r,c]=G.useState(!1),[d,m]=G.useState(!1),{updateApprenticeData:f,deleteApprentice:v}=wp(),{confirmModal:p}=vn(),g=()=>{c(!0)},x=async S=>{try{await f(n.id,S),c(!1),u()}catch(A){console.error("Failed to update apprentice:",A)}},b=()=>{p({title:"Delete Apprentice",message:`Are you sure you want to delete ${n.firstName} ${n.lastName}? This action cannot be undone.`,onConfirm:async()=>{m(!0);try{await v(n.id),u()}catch(S){console.error("Failed to delete apprentice:",S)}finally{m(!1)}}})},j=S=>{switch(S){case"active":return"bg-green-100 text-green-800";case"completed":return"bg-blue-100 text-blue-800";case"suspended":return"bg-yellow-100 text-yellow-800";case"withdrawn":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},T=S=>new Date(S).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),R=()=>{const S=new Date(n.startDate),A=new Date(n.endDate),X=Math.abs(A.getTime()-S.getTime()),q=Math.ceil(X/(1e3*60*60*24));return`${Math.floor(q/30)} months`},N=()=>{const S=new Date,X=new Date(n.endDate).getTime()-S.getTime();if(X<0)return"Completed";const q=Math.ceil(X/(1e3*60*60*24)),P=Math.floor(q/30),ee=q%30;return P>0?`${P} months, ${ee} days remaining`:`${ee} days remaining`};return r?s.jsx("div",{className:"max-w-4xl",children:s.jsx(Tp,{apprentice:n,onSubmit:x,onCancel:()=>c(!1)})}):s.jsxs("div",{className:"max-w-4xl",children:[s.jsx("div",{className:"p-6 border-b",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center",children:s.jsx(Mp,{className:"w-8 h-8 text-blue-600"})}),s.jsxs("div",{children:[s.jsxs("h2",{className:"text-2xl font-bold text-gray-900",children:[n.firstName," ",n.lastName]}),s.jsx("p",{className:"text-gray-500",children:n.position}),s.jsx("span",{className:`inline-flex rounded-full px-3 py-1 text-sm font-semibold ${j(n.status)}`,children:n.status})]})]}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsxs(Ae,{onClick:g,variant:"outline",size:"sm",children:[s.jsx(pu,{className:"w-4 h-4 mr-2"}),"Edit"]}),s.jsxs(Ae,{onClick:b,variant:"destructive",size:"sm",disabled:d,children:[s.jsx(gu,{className:"w-4 h-4 mr-2"}),d?"Deleting...":"Delete"]})]})]})}),s.jsxs("div",{className:"p-6 space-y-6",children:[s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(Xm,{className:"w-5 h-5 mr-2"}),"Contact Information"]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Xm,{className:"w-4 h-4 text-gray-400"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),s.jsx("p",{className:"font-medium",children:n.email})]})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(db,{className:"w-4 h-4 text-gray-400"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Phone"}),s.jsx("p",{className:"font-medium",children:n.phone})]})]})]})})]}),s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(Oy,{className:"w-5 h-5 mr-2"}),"Program Information"]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Department"}),s.jsx("p",{className:"font-medium",children:n.department})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Mentor"}),s.jsx("p",{className:"font-medium",children:n.mentor})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Duration"}),s.jsx("p",{className:"font-medium",children:R()})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Time Remaining"}),s.jsx("p",{className:"font-medium",children:N()})]})]})})]}),s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(or,{className:"w-5 h-5 mr-2"}),"Timeline"]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Start Date"}),s.jsx("p",{className:"font-medium",children:T(n.startDate)})]})]}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"End Date"}),s.jsx("p",{className:"font-medium",children:T(n.endDate)})]})]})]})})]}),s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(mu,{className:"w-5 h-5 mr-2"}),"Progress"]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsxs("div",{className:"flex justify-between items-center mb-2",children:[s.jsx("span",{className:"text-sm font-medium",children:"Overall Progress"}),s.jsxs("span",{className:"text-sm text-gray-500",children:[n.overallProgress,"%"]})]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${n.overallProgress}%`}})})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 pt-4",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Modules Completed"}),s.jsx("p",{className:"font-medium",children:"8 of 12"})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Reviews Completed"}),s.jsx("p",{className:"font-medium",children:"2 of 4"})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Exams Passed"}),s.jsx("p",{className:"font-medium",children:"5 of 6"})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Hours Logged"}),s.jsx("p",{className:"font-medium",children:"320 of 400"})]})]})]})})]}),s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(ab,{className:"w-5 h-5 mr-2"}),"Recent Activity"]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Module 8 completed"}),s.jsx("p",{className:"text-xs text-gray-500",children:"2 days ago"})]})]}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Quarterly review scheduled"}),s.jsx("p",{className:"text-xs text-gray-500",children:"1 week ago"})]})]}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Exam submitted"}),s.jsx("p",{className:"text-xs text-gray-500",children:"2 weeks ago"})]})]})]})})]})]})]})},Db=({onSearch:n,onFilter:u,onClear:r,searchPlaceholder:c="Search...",filterConfigs:d=[],className:m=""})=>{const[f,v]=G.useState(""),[p,g]=G.useState({}),[x,b]=G.useState(!1),j=S=>{v(S),n(S)},T=(S,A)=>{const X={...p,[S]:A};g(X),u(X)},R=()=>{v(""),g({}),r()},N=Object.keys(p).some(S=>p[S]);return s.jsxs("div",{className:`space-y-4 ${m}`,children:[s.jsxs("div",{className:"flex space-x-2",children:[s.jsx("div",{className:"flex-1",children:s.jsx(it,{placeholder:c,value:f,onChange:S=>j(S.target.value),leftIcon:s.jsx(hb,{className:"w-4 h-4"}),rightIcon:f&&s.jsx("button",{onClick:()=>j(""),className:"text-gray-400 hover:text-gray-600",children:s.jsx(Qm,{className:"w-4 h-4"})})})}),d.length>0&&s.jsxs(Ae,{variant:"outline",onClick:()=>b(!x),className:"flex items-center space-x-2",children:[s.jsx(sb,{className:"w-4 h-4"}),s.jsx("span",{children:"Filters"}),N&&s.jsx("span",{className:"ml-1 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:Object.keys(p).filter(S=>p[S]).length})]}),(f||N)&&s.jsx(Ae,{variant:"ghost",onClick:R,className:"text-gray-500 hover:text-gray-700",children:"Clear All"})]}),x&&d.length>0&&s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsx(ke,{className:"text-lg",children:"Filters"})}),s.jsx(Ee,{children:s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:d.map(S=>s.jsxs("div",{children:[S.type==="select"&&s.jsx(Ot,{label:S.label,options:S.options||[],value:p[S.key]||"",onChange:A=>T(S.key,A),placeholder:S.placeholder}),S.type==="multiselect"&&s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:S.label}),s.jsx("div",{className:"space-y-2",children:S.options?.map(A=>s.jsxs("label",{className:"flex items-center space-x-2",children:[s.jsx("input",{type:"checkbox",checked:p[S.key]?.includes(A.value)||!1,onChange:X=>{const q=p[S.key]||[],P=X.target.checked?[...q,A.value]:q.filter(ee=>ee!==A.value);T(S.key,P)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),s.jsx("span",{className:"text-sm text-gray-700",children:A.label})]},A.value))})]}),S.type==="date"&&s.jsx(it,{type:"date",label:S.label,value:p[S.key]||"",onChange:A=>T(S.key,A.target.value),placeholder:S.placeholder}),S.type==="number"&&s.jsx(it,{type:"number",label:S.label,value:p[S.key]||"",onChange:A=>T(S.key,A.target.value),placeholder:S.placeholder})]},S.key))})})]}),N&&s.jsx("div",{className:"flex flex-wrap gap-2",children:Object.entries(p).map(([S,A])=>{if(!A)return null;const X=d.find(P=>P.key===S);if(!X)return null;const q=Array.isArray(A)?A.join(", "):X.options?.find(P=>P.value===A)?.label||A;return s.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800",children:[s.jsxs("span",{className:"font-medium",children:[X.label,":"]}),s.jsx("span",{className:"ml-1",children:q}),s.jsx("button",{onClick:()=>T(S,Array.isArray(A)?[]:""),className:"ml-2 text-blue-600 hover:text-blue-800",children:s.jsx(Qm,{className:"w-3 h-3"})})]},S)})})]})},Ab=[{key:"status",label:"Status",type:"multiselect",options:[{value:"active",label:"Active"},{value:"completed",label:"Completed"},{value:"suspended",label:"Suspended"},{value:"withdrawn",label:"Withdrawn"}]},{key:"department",label:"Department",type:"multiselect",options:[{value:"Technology",label:"Technology"},{value:"Marketing",label:"Marketing"},{value:"Sales",label:"Sales"},{value:"Human Resources",label:"Human Resources"},{value:"Finance",label:"Finance"},{value:"Operations",label:"Operations"}]},{key:"mentor",label:"Mentor",type:"select",options:[{value:"John Smith",label:"John Smith"},{value:"Sarah Johnson",label:"Sarah Johnson"},{value:"Mike Brown",label:"Mike Brown"},{value:"Emily Davis",label:"Emily Davis"},{value:"David Wilson",label:"David Wilson"}]},{key:"startDate",label:"Start Date From",type:"date"}],zb=({currentPage:n,totalPages:u,totalItems:r,pageSize:c,onPageChange:d,onPageSizeChange:m,showPageSizeSelect:f=!0,pageSizeOptions:v=[10,25,50,100],className:p=""})=>{const g=(n-1)*c+1,x=Math.min(n*c,r),b=()=>{const R=[],N=[];for(let S=Math.max(2,n-2);S<=Math.min(u-1,n+2);S++)R.push(S);return n-2>2?N.push(1,"..."):N.push(1),N.push(...R),n+2<u-1?N.push("...",u):N.push(u),N},j=u>1?b():[];return r===0?null:s.jsxs("div",{className:`flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 ${p}`,children:[s.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[s.jsxs("span",{children:["Showing ",g," to ",x," of ",r," results"]}),f&&s.jsxs(s.Fragment,{children:[s.jsx("span",{children:"•"}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{children:"Show"}),s.jsx(Ot,{options:v.map(T=>({value:T.toString(),label:T.toString()})),value:c.toString(),onChange:T=>m(parseInt(T)),className:"w-20"}),s.jsx("span",{children:"per page"})]})]})]}),u>1&&s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx(Ae,{variant:"outline",size:"sm",onClick:()=>d(1),disabled:n===1,className:"p-2",children:s.jsx(Xy,{className:"w-4 h-4"})}),s.jsx(Ae,{variant:"outline",size:"sm",onClick:()=>d(n-1),disabled:n===1,className:"p-2",children:s.jsx(By,{className:"w-4 h-4"})}),j.map((T,R)=>{if(T==="...")return s.jsx("span",{className:"px-2 py-1 text-gray-500",children:"..."},R);const N=T;return s.jsx(Ae,{variant:N===n?"default":"outline",size:"sm",onClick:()=>d(N),className:"min-w-[2.5rem]",children:N},N)}),s.jsx(Ae,{variant:"outline",size:"sm",onClick:()=>d(n+1),disabled:n===u,className:"p-2",children:s.jsx(Hy,{className:"w-4 h-4"})}),s.jsx(Ae,{variant:"outline",size:"sm",onClick:()=>d(u),disabled:n===u,className:"p-2",children:s.jsx(Zy,{className:"w-4 h-4"})})]})]})},Ob=(n=10)=>{const[u,r]=Kt.useState(1),[c,d]=Kt.useState(n);return{currentPage:u,pageSize:c,handlePageChange:p=>{r(p)},handlePageSizeChange:p=>{d(p),r(1)},resetPagination:()=>{r(1),d(n)}}};function kb({data:n,columns:u,onSort:r,className:c="",emptyMessage:d="No data available"}){const[m,f]=G.useState({key:u[0]?.key,direction:null}),v=g=>{if(!u.find(T=>T.key===g)?.sortable)return;let b="asc";m.key===g&&(m.direction==="asc"?b="desc":m.direction==="desc"&&(b=null));const j={key:g,direction:b};f(j),r?.(j)},p=g=>g.sortable?m.key!==g.key||m.direction===null?s.jsx($y,{className:"w-4 h-4 text-gray-400"}):m.direction==="asc"?s.jsx(Gy,{className:"w-4 h-4 text-blue-600"}):s.jsx(Rp,{className:"w-4 h-4 text-blue-600"}):null;return n.length===0?s.jsx("div",{className:"text-center py-12",children:s.jsx("p",{className:"text-gray-500",children:d})}):s.jsx("div",{className:`overflow-x-auto ${c}`,children:s.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[s.jsx("thead",{className:"bg-gray-50",children:s.jsx("tr",{children:u.map(g=>s.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${g.sortable?"cursor-pointer hover:bg-gray-100":""} ${g.className||""}`,style:{width:g.width},onClick:()=>g.sortable&&v(g.key),children:s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx("span",{children:g.label}),p(g)]})},String(g.key)))})}),s.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:n.map((g,x)=>s.jsx("tr",{className:"hover:bg-gray-50",children:u.map(b=>s.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${b.className||""}`,style:{width:b.width},children:b.render?b.render(g[b.key],g):String(g[b.key]||"")},String(b.key)))},x))})]})})}function Ub(n,u){const[r,c]=G.useState({key:"",direction:null}),d=f=>{c(f)};return{sortedData:Kt.useMemo(()=>!r.direction||u?n:[...n].sort((f,v)=>{const p=f[r.key],g=v[r.key];return p<g?r.direction==="asc"?-1:1:p>g?r.direction==="asc"?1:-1:0}),[n,r,u]),sortConfig:r,handleSort:d}}function Lb(){const{apprentices:n,loading:u,error:r,fetchApprentices:c,createApprentice:d}=wp();cr();const{openModal:m}=vn(),[f,v]=G.useState(""),[p,g]=G.useState({}),[x,b]=G.useState("grid"),{currentPage:j,pageSize:T,handlePageChange:R,handlePageSizeChange:N}=Ob(12);G.useEffect(()=>{c()},[c]);const S=n.filter($=>{const W=!f||$.firstName.toLowerCase().includes(f.toLowerCase())||$.lastName.toLowerCase().includes(f.toLowerCase())||$.email.toLowerCase().includes(f.toLowerCase())||$.position.toLowerCase().includes(f.toLowerCase())||$.department.toLowerCase().includes(f.toLowerCase()),me=!p.status?.length||p.status.includes($.status),be=!p.department?.length||p.department.includes($.department),ge=!p.mentor||$.mentor===p.mentor,C=!p.startDate||new Date($.startDate)>=new Date(p.startDate);return W&&me&&be&&ge&&C}),{sortedData:A,handleSort:X}=Ub(S),q=A.length,P=Math.ceil(q/T),ee=(j-1)*T,re=A.slice(ee,ee+T),K=[{key:"firstName",label:"Name",sortable:!0,render:($,W)=>s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:s.jsxs("span",{className:"text-sm font-medium text-blue-600",children:[W.firstName.charAt(0),W.lastName.charAt(0)]})}),s.jsxs("div",{children:[s.jsxs("p",{className:"font-medium",children:[W.firstName," ",W.lastName]}),s.jsx("p",{className:"text-sm text-gray-500",children:W.email})]})]})},{key:"position",label:"Position",sortable:!0,render:($,W)=>s.jsxs("div",{children:[s.jsx("p",{className:"font-medium",children:W.position}),s.jsx("p",{className:"text-sm text-gray-500",children:W.department})]})},{key:"mentor",label:"Mentor",sortable:!0},{key:"startDate",label:"Start Date",sortable:!0,render:$=>new Date($).toLocaleDateString()},{key:"overallProgress",label:"Progress",sortable:!0,render:$=>s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-20 bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${$}%`}})}),s.jsxs("span",{className:"text-sm text-gray-600",children:[$,"%"]})]})},{key:"status",label:"Status",sortable:!0,render:$=>s.jsx("span",{className:`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${te($)}`,children:$})},{key:"id",label:"Actions",render:($,W)=>s.jsx(Ae,{variant:"outline",size:"sm",onClick:()=>oe(W.id),children:"View Details"})}],V=()=>{m({title:"Add New Apprentice",size:"lg",content:s.jsx(Tp,{onSubmit:async $=>{await d($),await c()},onCancel:()=>{}})})},oe=$=>{const W=n.find(me=>me.id===$);W&&m({title:"Apprentice Details",size:"xl",content:s.jsx(Cb,{apprentice:W,onClose:()=>{}})})};if(u&&!isCreating)return s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsx("div",{className:"text-lg",children:"Loading apprentices..."})});if(r)return s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsxs("div",{className:"text-lg text-red-600",children:["Error: ",r]})});const te=$=>{switch($){case"active":return"bg-green-100 text-green-800";case"completed":return"bg-blue-100 text-blue-800";case"suspended":return"bg-yellow-100 text-yellow-800";case"withdrawn":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between px-4 sm:px-0",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold tracking-tight text-gray-900",children:"Apprentices"}),s.jsx("p",{className:"mt-2 text-sm text-gray-700",children:"Manage and track all apprentices in the program."})]}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx(Ae,{variant:"outline",onClick:()=>b(x==="grid"?"table":"grid"),children:x==="grid"?"Table View":"Grid View"}),s.jsx(Ae,{onClick:V,children:"Add Apprentice"})]})]}),s.jsx(Db,{searchPlaceholder:"Search apprentices...",onSearch:v,onFilter:g,onClear:()=>{v(""),g({}),R(1)},filterConfigs:Ab}),s.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[s.jsxs("span",{children:["Showing ",Math.min(ee+1,q)," to ",Math.min(ee+T,q)," of ",q," apprentices"]}),s.jsx("span",{children:S.length!==n.length&&`(filtered from ${n.length} total)`})]}),re.length===0?s.jsxs("div",{className:"text-center py-12",children:[s.jsx("p",{className:"text-gray-500",children:n.length===0?"No apprentices found.":"No apprentices match your search criteria."}),n.length===0&&s.jsx(Ae,{className:"mt-4",onClick:V,children:"Add First Apprentice"})]}):s.jsxs(s.Fragment,{children:[x==="grid"?s.jsx("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3",children:re.map($=>s.jsxs(Re,{className:"hover:shadow-lg transition-shadow",children:[s.jsx(Oe,{children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs(ke,{className:"text-lg",children:[$.firstName," ",$.lastName]}),s.jsx("span",{className:`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${te($.status)}`,children:$.status})]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-900",children:$.position}),s.jsx("p",{className:"text-xs text-gray-500",children:$.department})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-xs text-gray-500 mb-1",children:"Progress"}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${$.overallProgress}%`}})}),s.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:[$.overallProgress,"% complete"]})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-3 text-xs",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-500",children:"Mentor"}),s.jsx("p",{className:"font-medium",children:$.mentor})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-500",children:"Started"}),s.jsx("p",{className:"font-medium",children:new Date($.startDate).toLocaleDateString()})]})]}),s.jsx("div",{className:"pt-2",children:s.jsx(Ae,{variant:"outline",size:"sm",className:"w-full",onClick:()=>oe($.id),children:"View Details"})})]})})]},$.id))}):s.jsx(Re,{children:s.jsx(Ee,{className:"p-0",children:s.jsx(kb,{data:re,columns:K,onSort:X,emptyMessage:"No apprentices found"})})}),s.jsx(zb,{currentPage:j,totalPages:P,totalItems:q,pageSize:T,onPageChange:R,onPageSizeChange:N})]})]})}const Bb={reviews:[],selectedReview:null,loading:!1,error:null,pagination:{page:1,pageSize:10,totalItems:0,totalPages:0},filters:{},sort:{field:"reviewDate",direction:"desc"}},Ep=fu()(hu((n,u)=>({...Bb,setReviews:r=>n({reviews:r}),setSelectedReview:r=>n({selectedReview:r}),addReview:r=>n(c=>({reviews:[...c.reviews,r]})),updateReview:(r,c)=>n(d=>({reviews:d.reviews.map(m=>m.id===r?{...m,...c}:m),selectedReview:d.selectedReview?.id===r?{...d.selectedReview,...c}:d.selectedReview})),removeReview:r=>n(c=>({reviews:c.reviews.filter(d=>d.id!==r),selectedReview:c.selectedReview?.id===r?null:c.selectedReview})),setLoading:r=>n({loading:r}),setError:r=>n({error:r}),setPagination:r=>n({pagination:r}),setFilters:r=>n({filters:r}),setSort:r=>n({sort:r}),resetFilters:()=>n({filters:{}}),fetchReviews:async(r=1,c,d={},m)=>{n({loading:!0,error:null});try{const{pagination:f,sort:v}=u(),p=m||v,g=new URLSearchParams({page:r.toString(),pageSize:f.pageSize.toString(),sortField:p.field,sortDirection:p.direction,...Object.entries(d).reduce((j,[T,R])=>(R!=null&&(Array.isArray(R)?j[T]=R.join(","):typeof R=="object"&&R!==null?Object.entries(R).forEach(([N,S])=>{S!=null&&(j[`${T}.${N}`]=S.toString())}):j[T]=R.toString()),j),{})});c&&g.append("apprenticeId",c);const x=await fetch(`/api/reviews?${g}`);if(!x.ok)throw new Error(`HTTP error! status: ${x.status}`);const b=await x.json();n({reviews:b.data,pagination:b.pagination,filters:d,sort:p,loading:!1})}catch(f){const v=f instanceof Error?f.message:"Failed to fetch reviews";console.error("Reviews fetch error:",f),n({error:v,loading:!1})}},searchReviews:async(r,c=1)=>{const d={...u().filters,searchTerm:r};await u().fetchReviews(c,void 0,d)},fetchReviewById:async r=>{n({loading:!0,error:null});try{const c=await fetch(`/api/reviews/${r}`);if(!c.ok)throw new Error(`HTTP error! status: ${c.status}`);const d=await c.json();n({selectedReview:d.data,loading:!1})}catch(c){const d=c instanceof Error?c.message:"Failed to fetch review";console.error("Review fetch error:",c),n({error:d,loading:!1})}},createReview:async r=>{n({loading:!0,error:null});try{const c=await fetch("/api/reviews",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!c.ok)throw new Error(`HTTP error! status: ${c.status}`);const d=await c.json();n(m=>({reviews:[...m.reviews,d.data],loading:!1}))}catch(c){const d=c instanceof Error?c.message:"Failed to create review";console.error("Review create error:",c),n({error:d,loading:!1})}},updateReviewData:async(r,c)=>{n({loading:!0,error:null});try{const d=await fetch(`/api/reviews/${r}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(c)});if(!d.ok)throw new Error(`HTTP error! status: ${d.status}`);const m=await d.json();u().updateReview(r,m.data),n({loading:!1})}catch(d){const m=d instanceof Error?d.message:"Failed to update review";console.error("Review update error:",d),n({error:m,loading:!1})}},deleteReview:async r=>{n({loading:!0,error:null});try{const c=await fetch(`/api/reviews/${r}`,{method:"DELETE"});if(!c.ok)throw new Error(`HTTP error! status: ${c.status}`);u().removeReview(r),n({loading:!1})}catch(c){const d=c instanceof Error?c.message:"Failed to delete review";console.error("Review delete error:",c),n({error:d,loading:!1})}},getReviewsByApprentice:r=>u().reviews.filter(c=>c.apprenticeId===r)}),{name:"review-store"})),Ja=G.forwardRef(({className:n,label:u,error:r,helperText:c,...d},m)=>s.jsxs("div",{className:"space-y-1",children:[u&&s.jsx("label",{className:"block text-sm font-medium text-gray-700",children:u}),s.jsx("textarea",{className:Me("flex min-h-[60px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm shadow-sm transition-colors","placeholder:text-muted-foreground","focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring","disabled:cursor-not-allowed disabled:opacity-50",r&&"border-red-500 focus-visible:ring-red-500",n),ref:m,...d}),r&&s.jsx("p",{className:"text-sm text-red-600",children:r}),c&&!r&&s.jsx("p",{className:"text-sm text-gray-500",children:c})]}));Ja.displayName="Textarea";const qb=[{value:"1",label:"Q1 (January - March)"},{value:"2",label:"Q2 (April - June)"},{value:"3",label:"Q3 (July - September)"},{value:"4",label:"Q4 (October - December)"}],Hb=[{value:"scheduled",label:"Scheduled"},{value:"in-progress",label:"In Progress"},{value:"completed",label:"Completed"},{value:"overdue",label:"Overdue"}],Zm=[{value:"john-smith",label:"John Smith"},{value:"sarah-johnson",label:"Sarah Johnson"},{value:"mike-brown",label:"Mike Brown"},{value:"emily-davis",label:"Emily Davis"},{value:"david-wilson",label:"David Wilson"}],Vb=[{value:"apprentice-1",label:"Alice Johnson"},{value:"apprentice-2",label:"Bob Smith"},{value:"apprentice-3",label:"Carol Brown"},{value:"apprentice-4",label:"David Wilson"},{value:"apprentice-5",label:"Emma Davis"}],Cp=({review:n,onSubmit:u,onCancel:r,isLoading:c=!1})=>{const d=new Date().getFullYear(),m={apprenticeId:n?.apprenticeId||"",quarter:n?.quarter||1,year:n?.year||d,reviewDate:n?.reviewDate?n.reviewDate.split("T")[0]:"",mentorId:n?.mentorId||"",mentorName:n?.mentorName||"",technicalSkills:n?.scores?.technicalSkills||0,communication:n?.scores?.communication||0,teamwork:n?.scores?.teamwork||0,initiative:n?.scores?.initiative||0,punctuality:n?.scores?.punctuality||0,strengths:n?.strengths?.join(", ")||"",areasForImprovement:n?.areasForImprovement?.join(", ")||"",goals:n?.goals?.join(", ")||"",mentorComments:n?.mentorComments||"",apprenticeComments:n?.apprenticeComments||"",status:n?.status||"scheduled"},f={apprenticeId:{required:!0},quarter:{required:!0},year:{required:!0,rules:[je.minValue(2020),je.maxValue(2030)]},reviewDate:{required:!0},mentorId:{required:!0},mentorName:{required:!0},technicalSkills:{required:!0,rules:[je.minValue(1),je.maxValue(5)]},communication:{required:!0,rules:[je.minValue(1),je.maxValue(5)]},teamwork:{required:!0,rules:[je.minValue(1),je.maxValue(5)]},initiative:{required:!0,rules:[je.minValue(1),je.maxValue(5)]},punctuality:{required:!0,rules:[je.minValue(1),je.maxValue(5)]},strengths:{required:!0,rules:[je.minLength(10)]},areasForImprovement:{required:!0,rules:[je.minLength(10)]},goals:{required:!0,rules:[je.minLength(10)]},mentorComments:{required:!0,rules:[je.minLength(20)]},apprenticeComments:{required:!1},status:{required:!0}},{data:v,errors:p,touched:g,updateField:x,touchField:b,validateForm:j,isValid:T}=vu(m,f),R=S=>{if(S.preventDefault(),j()){const A=Math.round((v.technicalSkills+v.communication+v.teamwork+v.initiative+v.punctuality)/5),X={...v,reviewDate:new Date(v.reviewDate).toISOString(),scores:{technicalSkills:v.technicalSkills,communication:v.communication,teamwork:v.teamwork,initiative:v.initiative,punctuality:v.punctuality,overallRating:A},strengths:v.strengths.split(",").map(q=>q.trim()).filter(q=>q),areasForImprovement:v.areasForImprovement.split(",").map(q=>q.trim()).filter(q=>q),goals:v.goals.split(",").map(q=>q.trim()).filter(q=>q),...n?.id&&{id:n.id}};u(X)}},N=()=>v.technicalSkills&&v.communication&&v.teamwork&&v.initiative&&v.punctuality?Math.round((v.technicalSkills+v.communication+v.teamwork+v.initiative+v.punctuality)/5):0;return s.jsxs("form",{onSubmit:R,className:"space-y-6 p-6",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx(Ot,{label:"Apprentice",options:Vb,value:v.apprenticeId,onChange:S=>x("apprenticeId",S),onBlur:()=>b("apprenticeId"),error:g.apprenticeId?p.apprenticeId:void 0,disabled:c,required:!0}),s.jsx(Ot,{label:"Quarter",options:qb,value:v.quarter.toString(),onChange:S=>x("quarter",parseInt(S)),onBlur:()=>b("quarter"),error:g.quarter?p.quarter:void 0,disabled:c,required:!0})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx(it,{label:"Year",type:"number",value:v.year,onChange:S=>x("year",parseInt(S.target.value)),onBlur:()=>b("year"),error:g.year?p.year:void 0,disabled:c,required:!0}),s.jsx(Ms,{label:"Review Date",value:v.reviewDate,onChange:S=>x("reviewDate",S),onBlur:()=>b("reviewDate"),error:g.reviewDate?p.reviewDate:void 0,disabled:c,required:!0})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx(Ot,{label:"Mentor",options:Zm,value:v.mentorId,onChange:S=>{x("mentorId",S);const A=Zm.find(X=>X.value===S);A&&x("mentorName",A.label)},onBlur:()=>b("mentorId"),error:g.mentorId?p.mentorId:void 0,disabled:c,required:!0}),s.jsx(Ot,{label:"Status",options:Hb,value:v.status,onChange:S=>x("status",S),onBlur:()=>b("status"),error:g.status?p.status:void 0,disabled:c,required:!0})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Performance Scores (1-5)"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[s.jsx(it,{label:"Technical Skills",type:"number",min:"1",max:"5",value:v.technicalSkills,onChange:S=>x("technicalSkills",parseInt(S.target.value)),onBlur:()=>b("technicalSkills"),error:g.technicalSkills?p.technicalSkills:void 0,disabled:c,required:!0}),s.jsx(it,{label:"Communication",type:"number",min:"1",max:"5",value:v.communication,onChange:S=>x("communication",parseInt(S.target.value)),onBlur:()=>b("communication"),error:g.communication?p.communication:void 0,disabled:c,required:!0}),s.jsx(it,{label:"Teamwork",type:"number",min:"1",max:"5",value:v.teamwork,onChange:S=>x("teamwork",parseInt(S.target.value)),onBlur:()=>b("teamwork"),error:g.teamwork?p.teamwork:void 0,disabled:c,required:!0}),s.jsx(it,{label:"Initiative",type:"number",min:"1",max:"5",value:v.initiative,onChange:S=>x("initiative",parseInt(S.target.value)),onBlur:()=>b("initiative"),error:g.initiative?p.initiative:void 0,disabled:c,required:!0}),s.jsx(it,{label:"Punctuality",type:"number",min:"1",max:"5",value:v.punctuality,onChange:S=>x("punctuality",parseInt(S.target.value)),onBlur:()=>b("punctuality"),error:g.punctuality?p.punctuality:void 0,disabled:c,required:!0}),s.jsx("div",{className:"flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Overall Rating"}),s.jsxs("p",{className:"text-2xl font-bold text-blue-600",children:[N(),"/5"]})]})})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Feedback"}),s.jsxs("div",{className:"space-y-4",children:[s.jsx(Ja,{label:"Strengths (comma-separated)",value:v.strengths,onChange:S=>x("strengths",S.target.value),onBlur:()=>b("strengths"),error:g.strengths?p.strengths:void 0,disabled:c,rows:3,placeholder:"e.g., Strong technical skills, Good communication, Proactive attitude",required:!0}),s.jsx(Ja,{label:"Areas for Improvement (comma-separated)",value:v.areasForImprovement,onChange:S=>x("areasForImprovement",S.target.value),onBlur:()=>b("areasForImprovement"),error:g.areasForImprovement?p.areasForImprovement:void 0,disabled:c,rows:3,placeholder:"e.g., Time management, Documentation, Code review process",required:!0}),s.jsx(Ja,{label:"Goals for Next Quarter (comma-separated)",value:v.goals,onChange:S=>x("goals",S.target.value),onBlur:()=>b("goals"),error:g.goals?p.goals:void 0,disabled:c,rows:3,placeholder:"e.g., Complete advanced JavaScript course, Lead a small project, Improve test coverage",required:!0})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Comments"}),s.jsxs("div",{className:"space-y-4",children:[s.jsx(Ja,{label:"Mentor Comments",value:v.mentorComments,onChange:S=>x("mentorComments",S.target.value),onBlur:()=>b("mentorComments"),error:g.mentorComments?p.mentorComments:void 0,disabled:c,rows:4,placeholder:"Detailed feedback from mentor...",required:!0}),s.jsx(Ja,{label:"Apprentice Comments (Optional)",value:v.apprenticeComments,onChange:S=>x("apprenticeComments",S.target.value),onBlur:()=>b("apprenticeComments"),error:g.apprenticeComments?p.apprenticeComments:void 0,disabled:c,rows:4,placeholder:"Self-reflection and feedback from apprentice..."})]})]}),s.jsxs("div",{className:"flex justify-end space-x-3 pt-6 border-t",children:[s.jsx(Ae,{type:"button",variant:"outline",onClick:r,disabled:c,children:"Cancel"}),s.jsx(Ae,{type:"submit",disabled:!T||c,children:c?"Saving...":n?"Update Review":"Create Review"})]})]})},Gb=({review:n,onClose:u})=>{const[r,c]=G.useState(!1),[d,m]=G.useState(!1),{updateReviewData:f,deleteReview:v}=Ep(),{confirmModal:p}=vn(),g=()=>{c(!0)},x=async S=>{try{await f(n.id,S),c(!1),u()}catch(A){console.error("Failed to update review:",A)}},b=()=>{p({title:"Delete Review",message:`Are you sure you want to delete this Q${n.quarter} ${n.year} review? This action cannot be undone.`,onConfirm:async()=>{m(!0);try{await v(n.id),u()}catch(S){console.error("Failed to delete review:",S)}finally{m(!1)}}})},j=S=>{switch(S){case"completed":return"bg-green-100 text-green-800";case"in-progress":return"bg-blue-100 text-blue-800";case"scheduled":return"bg-yellow-100 text-yellow-800";case"overdue":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},T=S=>new Date(S).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),R=S=>S>=4?"text-green-600":S>=3?"text-yellow-600":"text-red-600",N=S=>Array.from({length:5},(A,X)=>s.jsx(pb,{className:`w-4 h-4 ${X<S?"text-yellow-400 fill-current":"text-gray-300"}`},X));return r?s.jsx("div",{className:"max-w-4xl",children:s.jsx(Cp,{review:n,onSubmit:x,onCancel:()=>c(!1)})}):s.jsxs("div",{className:"max-w-4xl",children:[s.jsx("div",{className:"p-6 border-b",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsxs("h2",{className:"text-2xl font-bold text-gray-900",children:["Q",n.quarter," ",n.year," Review"]}),s.jsxs("p",{className:"text-gray-500 mt-1",children:["Review Date: ",T(n.reviewDate)]}),s.jsx("span",{className:`inline-flex rounded-full px-3 py-1 text-sm font-semibold mt-2 ${j(n.status)}`,children:n.status})]}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsxs(Ae,{onClick:g,variant:"outline",size:"sm",children:[s.jsx(pu,{className:"w-4 h-4 mr-2"}),"Edit"]}),s.jsxs(Ae,{onClick:b,variant:"destructive",size:"sm",disabled:d,children:[s.jsx(gu,{className:"w-4 h-4 mr-2"}),d?"Deleting...":"Delete"]})]})]})}),s.jsxs("div",{className:"p-6 space-y-6",children:[s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(Mp,{className:"w-5 h-5 mr-2"}),"Mentor Information"]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Mentor Name"}),s.jsx("p",{className:"font-medium",children:n.mentorName})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Mentor ID"}),s.jsx("p",{className:"font-medium",children:n.mentorId})]})]})})]}),s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(mu,{className:"w-5 h-5 mr-2"}),"Performance Scores"]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Technical Skills"}),s.jsxs("p",{className:`text-2xl font-bold ${R(n.scores.technicalSkills)}`,children:[n.scores.technicalSkills,"/5"]}),s.jsx("div",{className:"flex justify-center mt-1",children:N(n.scores.technicalSkills)})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Communication"}),s.jsxs("p",{className:`text-2xl font-bold ${R(n.scores.communication)}`,children:[n.scores.communication,"/5"]}),s.jsx("div",{className:"flex justify-center mt-1",children:N(n.scores.communication)})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Teamwork"}),s.jsxs("p",{className:`text-2xl font-bold ${R(n.scores.teamwork)}`,children:[n.scores.teamwork,"/5"]}),s.jsx("div",{className:"flex justify-center mt-1",children:N(n.scores.teamwork)})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Initiative"}),s.jsxs("p",{className:`text-2xl font-bold ${R(n.scores.initiative)}`,children:[n.scores.initiative,"/5"]}),s.jsx("div",{className:"flex justify-center mt-1",children:N(n.scores.initiative)})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Punctuality"}),s.jsxs("p",{className:`text-2xl font-bold ${R(n.scores.punctuality)}`,children:[n.scores.punctuality,"/5"]}),s.jsx("div",{className:"flex justify-center mt-1",children:N(n.scores.punctuality)})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500 mb-2",children:"Overall Rating"}),s.jsxs("p",{className:`text-3xl font-bold ${R(n.scores.overallRating)}`,children:[n.scores.overallRating,"/5"]}),s.jsx("div",{className:"flex justify-center mt-1",children:N(n.scores.overallRating)})]})]})})]}),s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(Sb,{className:"w-5 h-5 mr-2"}),"Strengths"]})}),s.jsx(Ee,{children:s.jsx("div",{className:"space-y-2",children:n.strengths.map((S,A)=>s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),s.jsx("span",{children:S})]},A))})})]}),s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(yb,{className:"w-5 h-5 mr-2"}),"Areas for Improvement"]})}),s.jsx(Ee,{children:s.jsx("div",{className:"space-y-2",children:n.areasForImprovement.map((S,A)=>s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),s.jsx("span",{children:S})]},A))})})]}),s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(au,{className:"w-5 h-5 mr-2"}),"Goals for Next Quarter"]})}),s.jsx(Ee,{children:s.jsx("div",{className:"space-y-2",children:n.goals.map((S,A)=>s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),s.jsx("span",{children:S})]},A))})})]}),s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(cb,{className:"w-5 h-5 mr-2"}),"Comments"]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Mentor Comments"}),s.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:s.jsx("p",{className:"text-gray-700",children:n.mentorComments})})]}),n.apprenticeComments&&s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Apprentice Comments"}),s.jsx("div",{className:"bg-blue-50 rounded-lg p-4",children:s.jsx("p",{className:"text-gray-700",children:n.apprenticeComments})})]})]})})]}),s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(or,{className:"w-5 h-5 mr-2"}),"Review Timeline"]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Review Scheduled"}),s.jsx("p",{className:"text-xs text-gray-500",children:T(n.reviewDate)})]})]}),n.status==="completed"&&s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Review Completed"}),s.jsxs("p",{className:"text-xs text-gray-500",children:["Overall Rating: ",n.scores.overallRating,"/5"]})]})]}),n.status==="in-progress"&&s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Review In Progress"}),s.jsxs("p",{className:"text-xs text-gray-500",children:["Being conducted by ",n.mentorName]})]})]}),n.status==="overdue"&&s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Review Overdue"}),s.jsx("p",{className:"text-xs text-gray-500",children:"Please schedule and complete as soon as possible"})]})]})]})})]})]})]})};function Yb(){const{reviews:n,loading:u,error:r,fetchReviews:c,createReview:d}=Ep(),{openModal:m}=vn(),[f,v]=G.useState(!1);G.useEffect(()=>{c()},[c]);const p=()=>{m({title:"Schedule New Review",size:"xl",content:s.jsx(Cp,{onSubmit:async j=>{await d(j),await c()},onCancel:()=>{}})})},g=j=>{const T=n.find(R=>R.id===j);T&&m({title:"Review Details",size:"xl",content:s.jsx(Gb,{review:T,onClose:()=>{}})})};if(u&&!f)return s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsx("div",{className:"text-lg",children:"Loading reviews..."})});if(r)return s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsxs("div",{className:"text-lg text-red-600",children:["Error: ",r]})});const x=j=>{switch(j){case"completed":return"bg-green-100 text-green-800";case"in-progress":return"bg-blue-100 text-blue-800";case"scheduled":return"bg-yellow-100 text-yellow-800";case"overdue":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},b=j=>Array.from({length:5},(T,R)=>s.jsx("span",{className:`text-sm ${R<j?"text-yellow-400":"text-gray-300"}`,children:"★"},R));return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between px-4 sm:px-0",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold tracking-tight text-gray-900",children:"Quarterly Reviews"}),s.jsx("p",{className:"mt-2 text-sm text-gray-700",children:"Track and manage apprentice performance reviews."})]}),s.jsx(Ae,{onClick:p,children:"Schedule Review"})]}),s.jsx("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:n.map(j=>s.jsxs(Re,{className:"hover:shadow-lg transition-shadow",children:[s.jsx(Oe,{children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs(ke,{className:"text-lg",children:["Q",j.quarter," ",j.year," Review"]}),s.jsx("span",{className:`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${x(j.status)}`,children:j.status})]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Apprentice"}),s.jsxs("p",{className:"text-sm text-gray-600",children:["Apprentice ID: ",j.apprenticeId]})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Mentor"}),s.jsx("p",{className:"text-sm text-gray-600",children:j.mentorName})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Overall Rating"}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"flex",children:b(Math.round(j.scores.overallRating))}),s.jsxs("span",{className:"text-sm text-gray-600",children:[j.scores.overallRating,"/10"]})]})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-500",children:"Technical Skills"}),s.jsxs("p",{className:"font-medium",children:[j.scores.technicalSkills,"/10"]})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-500",children:"Communication"}),s.jsxs("p",{className:"font-medium",children:[j.scores.communication,"/10"]})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-500",children:"Teamwork"}),s.jsxs("p",{className:"font-medium",children:[j.scores.teamwork,"/10"]})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-500",children:"Initiative"}),s.jsxs("p",{className:"font-medium",children:[j.scores.initiative,"/10"]})]})]}),j.strengths.length>0&&s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-900 mb-1",children:"Key Strengths"}),s.jsxs("div",{className:"flex flex-wrap gap-1",children:[j.strengths.slice(0,2).map((T,R)=>s.jsx("span",{className:"inline-flex rounded bg-green-100 px-2 py-1 text-xs text-green-800",children:T},R)),j.strengths.length>2&&s.jsxs("span",{className:"inline-flex rounded bg-gray-100 px-2 py-1 text-xs text-gray-600",children:["+",j.strengths.length-2," more"]})]})]}),s.jsx("div",{className:"pt-2",children:s.jsx(Ae,{variant:"outline",size:"sm",className:"w-full",onClick:()=>g(j.id),children:"View Full Review"})})]})})]},j.id))}),n.length===0&&s.jsxs("div",{className:"text-center py-12",children:[s.jsx("p",{className:"text-gray-500",children:"No reviews found."}),s.jsx(Ae,{className:"mt-4",onClick:p,children:"Schedule First Review"})]})]})}const Km=[{value:"written",label:"Written Exam"},{value:"practical",label:"Practical Assessment"},{value:"oral",label:"Oral Examination"},{value:"project",label:"Project Assessment"}],Xb=[{value:"upcoming",label:"Upcoming"},{value:"active",label:"Active"},{value:"completed",label:"Completed"},{value:"cancelled",label:"Cancelled"}],Qb=[{value:"JavaScript",label:"JavaScript"},{value:"React",label:"React"},{value:"Node.js",label:"Node.js"},{value:"Python",label:"Python"},{value:"Database",label:"Database"},{value:"System Design",label:"System Design"},{value:"DevOps",label:"DevOps"},{value:"Testing",label:"Testing"},{value:"Security",label:"Security"},{value:"General",label:"General Knowledge"}],Dp=({exam:n,onSubmit:u,onCancel:r,isLoading:c=!1})=>{const d={title:n?.title||"",description:n?.description||"",type:n?.type||"written",subject:n?.subject||"",maxScore:n?.maxScore||100,passingScore:n?.passingScore||60,duration:n?.duration||60,scheduledDate:n?.scheduledDate?n.scheduledDate.split("T")[0]:"",status:n?.status||"upcoming",instructions:n?.instructions||""},m={title:{required:!0,rules:[je.minLength(3),je.maxLength(200)]},description:{required:!0,rules:[je.minLength(10),je.maxLength(1e3)]},type:{required:!0},subject:{required:!0},maxScore:{required:!0,rules:[je.minValue(1),je.maxValue(1e3)]},passingScore:{required:!0,rules:[je.minValue(1),je.maxValue(1e3)]},duration:{required:!0,rules:[je.minValue(5),je.maxValue(480)]},scheduledDate:{required:!0,rules:[je.custom(N=>new Date(N)>new Date,"Scheduled date must be in the future")]},status:{required:!0},instructions:{required:!1,rules:[je.maxLength(2e3)]}},{data:f,errors:v,touched:p,updateField:g,touchField:x,validateForm:b,isValid:j}=vu(d,m),T=N=>{if(N.preventDefault(),b()){const S={...f,scheduledDate:new Date(f.scheduledDate).toISOString(),...n?.id&&{id:n.id}};u(S)}},R=()=>f.passingScore<=f.maxScore;return s.jsxs("form",{onSubmit:T,className:"space-y-6 p-6",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx("div",{className:"md:col-span-2",children:s.jsx(it,{label:"Exam Title",value:f.title,onChange:N=>g("title",N.target.value),onBlur:()=>x("title"),error:p.title?v.title:void 0,disabled:c,required:!0,placeholder:"e.g., JavaScript Fundamentals Assessment"})}),s.jsx(Ot,{label:"Exam Type",options:Km,value:f.type,onChange:N=>g("type",N),onBlur:()=>x("type"),error:p.type?v.type:void 0,disabled:c,required:!0}),s.jsx(Ot,{label:"Subject",options:Qb,value:f.subject,onChange:N=>g("subject",N),onBlur:()=>x("subject"),error:p.subject?v.subject:void 0,disabled:c,required:!0})]}),s.jsx("div",{className:"md:col-span-2",children:s.jsx(Ja,{label:"Description",value:f.description,onChange:N=>g("description",N.target.value),onBlur:()=>x("description"),error:p.description?v.description:void 0,disabled:c,rows:4,placeholder:"Describe the exam content, objectives, and what will be assessed...",required:!0})}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[s.jsx(it,{label:"Maximum Score",type:"number",value:f.maxScore,onChange:N=>g("maxScore",parseInt(N.target.value)),onBlur:()=>x("maxScore"),error:p.maxScore?v.maxScore:void 0,disabled:c,required:!0,min:"1"}),s.jsx(it,{label:"Passing Score",type:"number",value:f.passingScore,onChange:N=>g("passingScore",parseInt(N.target.value)),onBlur:()=>x("passingScore"),error:p.passingScore?v.passingScore:R()?void 0:"Passing score cannot exceed maximum score",disabled:c,required:!0,min:"1"}),s.jsx(it,{label:"Duration (minutes)",type:"number",value:f.duration,onChange:N=>g("duration",parseInt(N.target.value)),onBlur:()=>x("duration"),error:p.duration?v.duration:void 0,disabled:c,required:!0,min:"5",helperText:"Duration in minutes"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx(Ms,{label:"Scheduled Date",format:"datetime-local",value:f.scheduledDate,onChange:N=>g("scheduledDate",N),onBlur:()=>x("scheduledDate"),error:p.scheduledDate?v.scheduledDate:void 0,disabled:c,required:!0}),s.jsx(Ot,{label:"Status",options:Xb,value:f.status,onChange:N=>g("status",N),onBlur:()=>x("status"),error:p.status?v.status:void 0,disabled:c,required:!0})]}),s.jsx("div",{children:s.jsx(Ja,{label:"Instructions (Optional)",value:f.instructions,onChange:N=>g("instructions",N.target.value),onBlur:()=>x("instructions"),error:p.instructions?v.instructions:void 0,disabled:c,rows:6,placeholder:"Provide detailed instructions for the exam, including what materials are allowed, format, etc."})}),s.jsxs("div",{className:"bg-gray-50 rounded-lg p-4 space-y-2",children:[s.jsx("h3",{className:"font-semibold text-gray-900",children:"Exam Summary"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-500",children:"Type:"})," ",f.type?Km.find(N=>N.value===f.type)?.label:"Not selected"]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-500",children:"Subject:"})," ",f.subject||"Not selected"]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-500",children:"Score Range:"})," ",f.passingScore,"/",f.maxScore," (",f.maxScore>0?Math.round(f.passingScore/f.maxScore*100):0,"% to pass)"]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-500",children:"Duration:"})," ",f.duration," minutes (",Math.floor(f.duration/60),"h ",f.duration%60,"m)"]})]})]}),s.jsxs("div",{className:"flex justify-end space-x-3 pt-6 border-t",children:[s.jsx(Ae,{type:"button",variant:"outline",onClick:r,disabled:c,children:"Cancel"}),s.jsx(Ae,{type:"submit",disabled:!j||!R()||c,children:c?"Saving...":n?"Update Exam":"Create Exam"})]})]})},Qo=[{id:"1",examId:"1",apprenticeId:"1",score:85,completedAt:"2024-01-15T10:30:00Z",timeSpent:55,feedback:"Excellent understanding of core concepts. Minor improvements needed in advanced topics.",passed:!0,retakeAllowed:!1,retakeCount:0},{id:"2",examId:"1",apprenticeId:"2",score:55,completedAt:"2024-01-15T11:15:00Z",timeSpent:75,feedback:"Good effort but needs more practice with practical implementation.",passed:!1,retakeAllowed:!0,retakeCount:0},{id:"3",examId:"1",apprenticeId:"3",score:92,completedAt:"2024-01-15T14:20:00Z",timeSpent:48,feedback:"Outstanding performance with innovative solutions.",passed:!0,retakeAllowed:!1,retakeCount:0}],Zb=({exam:n,onClose:u})=>{const[r,c]=G.useState(!1),[d,m]=G.useState(!1),{confirmModal:f}=vn(),v=()=>{c(!0)},p=async S=>{try{console.log("Updating exam:",S),c(!1),u()}catch(A){console.error("Failed to update exam:",A)}},g=()=>{f({title:"Delete Exam",message:`Are you sure you want to delete "${n.title}"? This action cannot be undone.`,onConfirm:async()=>{m(!0);try{console.log("Deleting exam:",n.id),u()}catch(S){console.error("Failed to delete exam:",S)}finally{m(!1)}}})},x=S=>{switch(S){case"completed":return"bg-green-100 text-green-800";case"active":return"bg-blue-100 text-blue-800";case"upcoming":return"bg-yellow-100 text-yellow-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},b=S=>{switch(S){case"written":return s.jsx(Ym,{className:"w-5 h-5"});case"practical":return s.jsx(au,{className:"w-5 h-5"});case"oral":return s.jsx(Xo,{className:"w-5 h-5"});case"project":return s.jsx(Gm,{className:"w-5 h-5"});default:return s.jsx(Ym,{className:"w-5 h-5"})}},j=S=>new Date(S).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),T=S=>{const A=Math.floor(S/60),X=S%60;return A>0?`${A}h ${X}m`:`${X}m`},N=(()=>{const S=Qo.filter(K=>K.examId===n.id),A=S.length,X=S.filter(K=>K.passed).length,q=A-X,P=A>0?Math.round(S.reduce((K,V)=>K+V.score,0)/A):0,ee=A>0?Math.round(S.reduce((K,V)=>K+V.timeSpent,0)/A):0,re=A>0?Math.round(X/A*100):0;return{totalAttempts:A,passed:X,failed:q,averageScore:P,averageTime:ee,passRate:re}})();return r?s.jsx("div",{className:"max-w-4xl",children:s.jsx(Dp,{exam:n,onSubmit:p,onCancel:()=>c(!1)})}):s.jsxs("div",{className:"max-w-4xl",children:[s.jsx("div",{className:"p-6 border-b",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center",children:b(n.type)}),s.jsxs("div",{children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:n.title}),s.jsx("p",{className:"text-gray-500",children:n.subject}),s.jsx("span",{className:`inline-flex rounded-full px-3 py-1 text-sm font-semibold mt-2 ${x(n.status)}`,children:n.status})]})]}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsxs(Ae,{onClick:v,variant:"outline",size:"sm",children:[s.jsx(pu,{className:"w-4 h-4 mr-2"}),"Edit"]}),s.jsxs(Ae,{onClick:g,variant:"destructive",size:"sm",disabled:d,children:[s.jsx(gu,{className:"w-4 h-4 mr-2"}),d?"Deleting...":"Delete"]})]})]})}),s.jsxs("div",{className:"p-6 space-y-6",children:[s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(Gm,{className:"w-5 h-5 mr-2"}),"Exam Information"]})}),s.jsxs(Ee,{children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Type"}),s.jsx("p",{className:"font-medium capitalize",children:n.type})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Subject"}),s.jsx("p",{className:"font-medium",children:n.subject})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Duration"}),s.jsx("p",{className:"font-medium",children:T(n.duration)})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Scheduled Date"}),s.jsx("p",{className:"font-medium",children:j(n.scheduledDate)})]})]}),s.jsxs("div",{className:"mt-4",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Description"}),s.jsx("p",{className:"mt-1 text-gray-700",children:n.description})]})]})]}),s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(mu,{className:"w-5 h-5 mr-2"}),"Scoring Information"]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Maximum Score"}),s.jsx("p",{className:"text-2xl font-bold text-blue-600",children:n.maxScore})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Passing Score"}),s.jsx("p",{className:"text-2xl font-bold text-green-600",children:n.passingScore})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Pass Percentage"}),s.jsxs("p",{className:"text-2xl font-bold text-purple-600",children:[Math.round(n.passingScore/n.maxScore*100),"%"]})]})]})})]}),s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(au,{className:"w-5 h-5 mr-2"}),"Performance Statistics"]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Total Attempts"}),s.jsx("p",{className:"text-xl font-bold",children:N.totalAttempts})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Passed"}),s.jsx("p",{className:"text-xl font-bold text-green-600",children:N.passed})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Failed"}),s.jsx("p",{className:"text-xl font-bold text-red-600",children:N.failed})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Pass Rate"}),s.jsxs("p",{className:"text-xl font-bold text-blue-600",children:[N.passRate,"%"]})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Avg Score"}),s.jsx("p",{className:"text-xl font-bold",children:N.averageScore})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Avg Time"}),s.jsx("p",{className:"text-xl font-bold",children:T(N.averageTime)})]})]})})]}),n.instructions&&s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(Fy,{className:"w-5 h-5 mr-2"}),"Instructions"]})}),s.jsx(Ee,{children:s.jsx("div",{className:"bg-blue-50 rounded-lg p-4",children:s.jsx("p",{className:"text-gray-700 whitespace-pre-wrap",children:n.instructions})})})]}),s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(Xo,{className:"w-5 h-5 mr-2"}),"Recent Results"]})}),s.jsx(Ee,{children:Qo.filter(S=>S.examId===n.id).length>0?s.jsx("div",{className:"space-y-4",children:Qo.filter(S=>S.examId===n.id).slice(0,5).map(S=>s.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${S.passed?"bg-green-100":"bg-red-100"}`,children:S.passed?s.jsx(Wy,{className:"w-4 h-4 text-green-600"}):s.jsx(eb,{className:"w-4 h-4 text-red-600"})}),s.jsxs("div",{children:[s.jsxs("p",{className:"font-medium",children:["Apprentice ",S.apprenticeId]}),s.jsx("p",{className:"text-sm text-gray-500",children:j(S.completedAt)})]})]}),s.jsxs("div",{className:"text-right",children:[s.jsxs("p",{className:`font-bold ${S.passed?"text-green-600":"text-red-600"}`,children:[S.score,"/",n.maxScore]}),s.jsx("p",{className:"text-sm text-gray-500",children:T(S.timeSpent)})]})]},S.id))}):s.jsxs("div",{className:"text-center py-8 text-gray-500",children:[s.jsx(Xo,{className:"w-12 h-12 mx-auto mb-4 text-gray-300"}),s.jsx("p",{children:"No results yet"})]})})]}),s.jsxs(Re,{children:[s.jsx(Oe,{children:s.jsxs(ke,{className:"flex items-center",children:[s.jsx(or,{className:"w-5 h-5 mr-2"}),"Exam Timeline"]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Exam Created"}),s.jsx("p",{className:"text-xs text-gray-500",children:n.status==="upcoming"?"Ready for scheduling":"Scheduled"})]})]}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:`w-2 h-2 rounded-full ${n.status==="active"||n.status==="completed"?"bg-green-500":"bg-gray-300"}`}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Exam Scheduled"}),s.jsx("p",{className:"text-xs text-gray-500",children:j(n.scheduledDate)})]})]}),n.status==="completed"&&s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Exam Completed"}),s.jsxs("p",{className:"text-xs text-gray-500",children:[N.totalAttempts," apprentices completed"]})]})]}),n.status==="cancelled"&&s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Exam Cancelled"}),s.jsx("p",{className:"text-xs text-gray-500",children:"Contact administrator for more information"})]})]})]})})]})]})]})};function Kb(){const[n,u]=G.useState([]),[r,c]=G.useState(!0),[d,m]=G.useState(null),{openModal:f}=vn(),[v,p]=G.useState(!1);G.useEffect(()=>{g()},[]);const g=async()=>{try{const S=await fetch("/api/exams");if(!S.ok)throw new Error("Failed to fetch exams");const A=await S.json();u(A)}catch(S){m(S instanceof Error?S.message:"Unknown error")}finally{c(!1)}},x=()=>{f({title:"Create New Exam",size:"xl",content:s.jsx(Dp,{onSubmit:async S=>{if(!(await fetch("/api/exams",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(S)})).ok)throw new Error("Failed to create exam");await g()},onCancel:()=>{}})})},b=S=>{const A=n.find(X=>X.id===S);A&&f({title:"Exam Details",size:"xl",content:s.jsx(Zb,{exam:A,onClose:()=>{}})})},j=async S=>{alert(`Starting exam ${S}. In a real app, this would navigate to the exam taking interface.`);try{await fetch(`/api/exams/${S}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:"active"})}),await g()}catch(A){console.error("Failed to update exam status:",A)}};if(r)return s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsx("div",{className:"text-lg",children:"Loading exams..."})});if(d)return s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsxs("div",{className:"text-lg text-red-600",children:["Error: ",d]})});const T=S=>{switch(S){case"completed":return"bg-green-100 text-green-800";case"active":return"bg-blue-100 text-blue-800";case"upcoming":return"bg-yellow-100 text-yellow-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},R=S=>{switch(S){case"written":return"📝";case"practical":return"🔧";case"oral":return"🎤";case"project":return"💻";default:return"📋"}},N=S=>{if(S<60)return`${S} min`;const A=Math.floor(S/60),X=S%60;return X>0?`${A}h ${X}m`:`${A}h`};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between px-4 sm:px-0",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold tracking-tight text-gray-900",children:"Exams"}),s.jsx("p",{className:"mt-2 text-sm text-gray-700",children:"Manage and track apprentice examinations and assessments."})]}),s.jsx(Ae,{onClick:x,children:"Create Exam"})]}),s.jsx("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:n.map(S=>s.jsxs(Re,{className:"hover:shadow-lg transition-shadow",children:[s.jsx(Oe,{children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs(ke,{className:"text-lg flex items-center space-x-2",children:[s.jsx("span",{children:R(S.type)}),s.jsx("span",{children:S.title})]}),s.jsx("span",{className:`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${T(S.status)}`,children:S.status})]})}),s.jsx(Ee,{children:s.jsxs("div",{className:"space-y-4",children:[s.jsx("div",{children:s.jsx("p",{className:"text-sm text-gray-600",children:S.description})}),s.jsxs("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-500",children:"Subject"}),s.jsx("p",{className:"font-medium",children:S.subject})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-500",children:"Type"}),s.jsx("p",{className:"font-medium capitalize",children:S.type})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-500",children:"Duration"}),s.jsx("p",{className:"font-medium",children:N(S.duration)})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-500",children:"Passing Score"}),s.jsxs("p",{className:"font-medium",children:[S.passingScore,"/",S.maxScore]})]})]}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm text-gray-500",children:"Scheduled Date"}),s.jsx("p",{className:"text-sm font-medium",children:new Date(S.scheduledDate).toLocaleString()})]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:`${S.passingScore/S.maxScore*100}%`}})}),s.jsxs("p",{className:"text-xs text-gray-500",children:[Math.round(S.passingScore/S.maxScore*100),"% required to pass"]}),s.jsxs("div",{className:"pt-2 space-y-2",children:[s.jsx(Ae,{variant:"outline",size:"sm",className:"w-full",onClick:()=>b(S.id),children:"View Details"}),S.status==="upcoming"&&s.jsx(Ae,{size:"sm",className:"w-full",onClick:()=>j(S.id),children:"Start Exam"})]})]})})]},S.id))}),n.length===0&&s.jsxs("div",{className:"text-center py-12",children:[s.jsx("p",{className:"text-gray-500",children:"No exams found."}),s.jsx(Ae,{className:"mt-4",onClick:x,children:"Create First Exam"})]})]})}const Cs=sx({component:ny}),$b=Es({getParentRoute:()=>Cs,path:"/",component:wy}),Jb=Es({getParentRoute:()=>Cs,path:"/apprentices",component:Lb}),Fb=Es({getParentRoute:()=>Cs,path:"/reviews",component:Yb}),Pb=Es({getParentRoute:()=>Cs,path:"/exams",component:Kb}),Wb=Cs.addChildren([$b,Jb,Fb,Pb]);class Ib extends Kt.Component{constructor(u){super(u),this.state={hasError:!1}}static getDerivedStateFromError(u){return{hasError:!0,error:u}}componentDidCatch(u,r){console.error("Application Error:",u,r)}render(){return this.state.hasError?s.jsx("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",children:s.jsxs("div",{className:"text-center space-y-4",children:[s.jsx("div",{className:"text-6xl",children:"⚠️"}),s.jsx("h1",{className:"text-2xl font-bold text-destructive",children:"Something went wrong"}),s.jsx("p",{className:"text-muted-foreground max-w-md",children:"An unexpected error occurred. Please refresh the page or contact support if the problem persists."}),s.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90",children:"Refresh Page"})]})}):this.props.children}}const e1=mx({routeTree:Wb,defaultPreload:"intent"});async function t1(){}t1().then(()=>{lv.createRoot(document.getElementById("root")).render(s.jsx(Kt.StrictMode,{children:s.jsx(Ib,{children:s.jsx(vx,{router:e1})})}))});
