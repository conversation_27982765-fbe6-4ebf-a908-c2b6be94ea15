{"version": 3, "file": "vite.cjs", "sources": ["../../src/vite.ts"], "sourcesContent": ["import { createVitePlugin } from 'unplugin'\n\nimport { configSchema } from './core/config'\nimport { unpluginRouterCodeSplitterFactory } from './core/router-code-splitter-plugin'\nimport { unpluginRouterGeneratorFactory } from './core/router-generator-plugin'\nimport { unpluginRouterComposedFactory } from './core/router-composed-plugin'\nimport { unpluginRouteAutoImportFactory } from './core/route-autoimport-plugin'\nimport type { Config } from './core/config'\n\nconst tanstackRouterAutoImport = createVitePlugin(\n  unpluginRouteAutoImportFactory,\n)\n\n/**\n * @example\n * ```ts\n * export default defineConfig({\n *   plugins: [tanstackRouterGenerator()],\n *   // ...\n * })\n * ```\n */\nconst tanstackRouterGenerator = createVitePlugin(unpluginRouterGeneratorFactory)\n\n/**\n * @example\n * ```ts\n * export default defineConfig({\n *   plugins: [tanStackRouterCodeSplitter()],\n *   // ...\n * })\n * ```\n */\nconst tanStackRouterCodeSplitter = createVitePlugin(\n  unpluginRouterCodeSplitterFactory,\n)\n\n/**\n * @example\n * ```ts\n * export default defineConfig({\n *   plugins: [tanstackRouter()],\n *   // ...\n * })\n * ```\n */\nconst tanstackRouter = createVitePlugin(unpluginRouterComposedFactory)\n\n/**\n * @deprecated Use `tanstackRouter` instead.\n */\nconst TanStackRouterVite = tanstackRouter\n\nexport default tanstackRouter\nexport {\n  configSchema,\n  tanstackRouterAutoImport,\n  tanStackRouterCodeSplitter,\n  tanstackRouterGenerator,\n  TanStackRouterVite,\n  tanstackRouter,\n}\n\nexport type { Config }\n"], "names": ["createVitePlugin", "unpluginRouteAutoImportFactory", "unpluginRouterGeneratorFactory", "unpluginRouterCodeSplitterFactory", "unpluginRouterComposedFactory"], "mappings": ";;;;;;;;AASA,MAAM,2BAA2BA,SAAA;AAAA,EAC/BC,sBAAAA;AACF;AAWM,MAAA,0BAA0BD,0BAAiBE,sBAA8B,8BAAA;AAW/E,MAAM,6BAA6BF,SAAA;AAAA,EACjCG,yBAAAA;AACF;AAWM,MAAA,iBAAiBH,0BAAiBI,qBAA6B,6BAAA;AAKrE,MAAM,qBAAqB;;;;;;;;"}