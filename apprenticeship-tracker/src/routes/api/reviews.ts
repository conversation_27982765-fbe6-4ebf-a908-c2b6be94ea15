export async function GET() {
  // API endpoint for getting reviews
  return new Response(JSON.stringify({ reviews: [] }), {
    headers: { 'Content-Type': 'application/json' },
  })
}

export async function POST(request: Request) {
  // API endpoint for creating reviews
  const body = await request.json()
  return new Response(JSON.stringify({ success: true, data: body }), {
    headers: { 'Content-Type': 'application/json' },
  })
}