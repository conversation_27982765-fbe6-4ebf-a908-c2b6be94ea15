{"version": 3, "file": "esbuild.cjs", "sources": ["../../src/esbuild.ts"], "sourcesContent": ["import { createEsbuildPlugin } from 'unplugin'\n\nimport { configSchema } from './core/config'\nimport { unpluginRouterCodeSplitterFactory } from './core/router-code-splitter-plugin'\nimport { unpluginRouterGeneratorFactory } from './core/router-generator-plugin'\nimport { unpluginRouterComposedFactory } from './core/router-composed-plugin'\n\nimport type { Config } from './core/config'\n\n/**\n * @example\n * ```ts\n * export default {\n *   plugins: [TanStackRouterGeneratorEsbuild()],\n *   // ...\n * }\n * ```\n */\nconst TanStackRouterGeneratorEsbuild = createEsbuildPlugin(\n  unpluginRouterGeneratorFactory,\n)\n\n/**\n * @example\n * ```ts\n * export default {\n *  plugins: [TanStackRouterCodeSplitterEsbuild()],\n *  // ...\n * }\n * ```\n */\nconst TanStackRouterCodeSplitterEsbuild = createEsbuildPlugin(\n  unpluginRouterCodeSplitterFactory,\n)\n\n/**\n * @example\n * ```ts\n * export default {\n *   plugins: [tanstackRouter()],\n *   // ...\n * }\n * ```\n */\nconst TanStackRouterEsbuild = createEsbuildPlugin(unpluginRouterComposedFactory)\nconst tanstackRouter = TanStackRouterEsbuild\nexport default TanStackRouterEsbuild\n\nexport {\n  configSchema,\n  TanStackRouterGeneratorEsbuild,\n  TanStackRouterCodeSplitterEsbuild,\n  TanStackRouterEsbuild,\n  tanstackRouter,\n}\n\nexport type { Config }\n"], "names": ["createEsbuildPlugin", "unpluginRouterGeneratorFactory", "unpluginRouterCodeSplitterFactory", "unpluginRouterComposedFactory"], "mappings": ";;;;;;;AAkBA,MAAM,iCAAiCA,SAAA;AAAA,EACrCC,sBAAAA;AACF;AAWA,MAAM,oCAAoCD,SAAA;AAAA,EACxCE,yBAAAA;AACF;AAWM,MAAA,wBAAwBF,6BAAoBG,qBAA6B,6BAAA;AAC/E,MAAM,iBAAiB;;;;;;;"}