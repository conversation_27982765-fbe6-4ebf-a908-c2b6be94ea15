{"version": 3, "file": "framework-options.cjs", "sources": ["../../../../src/core/code-splitter/framework-options.ts"], "sourcesContent": ["type FrameworkOptions = {\n  package: string\n  idents: {\n    createFileRoute: string\n    lazyFn: string\n    lazyRouteComponent: string\n  }\n}\n\nexport function getFrameworkOptions(framework: string): FrameworkOptions {\n  let frameworkOptions: FrameworkOptions\n\n  switch (framework) {\n    case 'react':\n      frameworkOptions = {\n        package: '@tanstack/react-router',\n        idents: {\n          createFileRoute: 'createFileRoute',\n          lazyFn: 'lazyFn',\n          lazyRouteComponent: 'lazyRouteComponent',\n        },\n      }\n      break\n    case 'solid':\n      frameworkOptions = {\n        package: '@tanstack/solid-router',\n        idents: {\n          createFileRoute: 'createFileRoute',\n          lazyFn: 'lazyFn',\n          lazyRouteComponent: 'lazyRouteComponent',\n        },\n      }\n      break\n    default:\n      throw new Error(\n        `[getFrameworkOptions] - Unsupported framework: ${framework}`,\n      )\n  }\n\n  return frameworkOptions\n}\n"], "names": [], "mappings": ";;AASO,SAAS,oBAAoB,WAAqC;AACnE,MAAA;AAEJ,UAAQ,WAAW;AAAA,IACjB,KAAK;AACgB,yBAAA;AAAA,QACjB,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,iBAAiB;AAAA,UACjB,QAAQ;AAAA,UACR,oBAAoB;AAAA,QAAA;AAAA,MAExB;AACA;AAAA,IACF,KAAK;AACgB,yBAAA;AAAA,QACjB,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,iBAAiB;AAAA,UACjB,QAAQ;AAAA,UACR,oBAAoB;AAAA,QAAA;AAAA,MAExB;AACA;AAAA,IACF;AACE,YAAM,IAAI;AAAA,QACR,kDAAkD,SAAS;AAAA,MAC7D;AAAA,EAAA;AAGG,SAAA;AACT;;"}