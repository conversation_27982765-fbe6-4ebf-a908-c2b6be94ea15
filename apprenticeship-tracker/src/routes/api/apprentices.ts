export async function GET() {
  // API endpoint for getting apprentices
  return new Response(JSON.stringify({ apprentices: [] }), {
    headers: { 'Content-Type': 'application/json' },
  })
}

export async function POST(request: Request) {
  // API endpoint for creating apprentices
  const body = await request.json()
  return new Response(JSON.stringify({ success: true, data: body }), {
    headers: { 'Content-Type': 'application/json' },
  })
}