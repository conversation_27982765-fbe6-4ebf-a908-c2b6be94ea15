import { createRootRoute, createRoute } from '@tanstack/react-router'
import Layout from './components/Layout'
import { Dashboard } from './features/dashboard/components/Dashboard'
import { Apprentices } from './features/apprentices/components/Apprentices'
import { Reviews } from './features/reviews/components/Reviews'
import { Exams } from './features/exams/components/Exams'

const rootRoute = createRootRoute({
  component: Layout,
})

const indexRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/',
  component: Dashboard,
})

const apprenticesRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/apprentices',
  component: Apprentices,
})

const reviewsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/reviews',
  component: Reviews,
})

const examsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/exams',
  component: Exams,
})

export const routeTree = rootRoute.addChildren([
  indexRoute,
  apprenticesRoute,
  reviewsRoute,
  examsRoute,
])