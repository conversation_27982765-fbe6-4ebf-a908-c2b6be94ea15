export async function GET() {
  // API endpoint for getting exams
  return new Response(JSON.stringify({ exams: [] }), {
    headers: { 'Content-Type': 'application/json' },
  })
}

export async function POST(request: Request) {
  // API endpoint for creating exams
  const body = await request.json()
  return new Response(JSON.stringify({ success: true, data: body }), {
    headers: { 'Content-Type': 'application/json' },
  })
}