{"version": 3, "file": "route-autoimport-plugin.js", "sources": ["../../../src/core/route-autoimport-plugin.ts"], "sourcesContent": ["import { generateFromAst, logDiff, parseAst } from '@tanstack/router-utils'\nimport babel from '@babel/core'\nimport * as template from '@babel/template'\nimport { getConfig } from './config'\nimport { debug } from './utils'\nimport type { Config } from './config'\nimport type { UnpluginFactory } from 'unplugin'\n\n/**\n * This plugin adds imports for createFileRoute and createLazyFileRoute to the file route.\n */\nexport const unpluginRouteAutoImportFactory: UnpluginFactory<\n  Partial<Config> | undefined\n> = (options = {}) => {\n  let ROOT: string = process.cwd()\n  let userConfig = options as Config\n\n  return {\n    name: 'tanstack-router:autoimport',\n    enforce: 'pre',\n\n    transform: {\n      filter: {\n        // this is necessary for webpack / rspack to avoid matching .html files\n        id: /\\.(m|c)?(j|t)sx?$/,\n        code: /createFileRoute\\(|createLazyFileRoute\\(/,\n      },\n      handler(code, id) {\n        if (!globalThis.TSR_ROUTES_BY_ID_MAP?.has(id)) {\n          return null\n        }\n        let routeType: 'createFileRoute' | 'createLazyFileRoute'\n        if (code.includes('createFileRoute(')) {\n          routeType = 'createFileRoute'\n        } else if (code.includes('createLazyFileRoute(')) {\n          routeType = 'createLazyFileRoute'\n        } else {\n          return null\n        }\n\n        const routerImportPath = `@tanstack/${userConfig.target}-router`\n\n        const ast = parseAst({ code })\n\n        let isCreateRouteFunctionImported = false as boolean\n\n        babel.traverse(ast, {\n          Program: {\n            enter(programPath) {\n              programPath.traverse({\n                ImportDeclaration(path) {\n                  const importedSpecifiers = path.node.specifiers.map(\n                    (specifier) => specifier.local.name,\n                  )\n                  if (\n                    importedSpecifiers.includes(routeType) &&\n                    path.node.source.value === routerImportPath\n                  ) {\n                    isCreateRouteFunctionImported = true\n                  }\n                },\n              })\n            },\n          },\n        })\n\n        if (!isCreateRouteFunctionImported) {\n          if (debug) console.info('Adding autoimports to route ', id)\n\n          const autoImportStatement = template.statement(\n            `import { ${routeType} } from '${routerImportPath}'`,\n          )()\n          ast.program.body.unshift(autoImportStatement)\n\n          const result = generateFromAst(ast, {\n            sourceMaps: true,\n            filename: id,\n            sourceFileName: id,\n          })\n          if (debug) {\n            logDiff(code, result.code)\n            console.log('Output:\\n', result.code + '\\n\\n')\n          }\n          return result\n        }\n\n        return null\n      },\n    },\n\n    vite: {\n      configResolved(config) {\n        ROOT = config.root\n        userConfig = getConfig(options, ROOT)\n      },\n    },\n\n    rspack() {\n      ROOT = process.cwd()\n      userConfig = getConfig(options, ROOT)\n    },\n\n    webpack() {\n      ROOT = process.cwd()\n      userConfig = getConfig(options, ROOT)\n    },\n  }\n}\n"], "names": [], "mappings": ";;;;;AAWO,MAAM,iCAET,CAAC,UAAU,OAAO;AAChB,MAAA,OAAe,QAAQ,IAAI;AAC/B,MAAI,aAAa;AAEV,SAAA;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,IAET,WAAW;AAAA,MACT,QAAQ;AAAA;AAAA,QAEN,IAAI;AAAA,QACJ,MAAM;AAAA,MACR;AAAA,MACA,QAAQ,MAAM,IAAI;;AAChB,YAAI,GAAC,gBAAW,yBAAX,mBAAiC,IAAI,MAAK;AACtC,iBAAA;AAAA,QAAA;AAEL,YAAA;AACA,YAAA,KAAK,SAAS,kBAAkB,GAAG;AACzB,sBAAA;AAAA,QACH,WAAA,KAAK,SAAS,sBAAsB,GAAG;AACpC,sBAAA;AAAA,QAAA,OACP;AACE,iBAAA;AAAA,QAAA;AAGH,cAAA,mBAAmB,aAAa,WAAW,MAAM;AAEvD,cAAM,MAAM,SAAS,EAAE,MAAM;AAE7B,YAAI,gCAAgC;AAEpC,cAAM,SAAS,KAAK;AAAA,UAClB,SAAS;AAAA,YACP,MAAM,aAAa;AACjB,0BAAY,SAAS;AAAA,gBACnB,kBAAkB,MAAM;AAChB,wBAAA,qBAAqB,KAAK,KAAK,WAAW;AAAA,oBAC9C,CAAC,cAAc,UAAU,MAAM;AAAA,kBACjC;AAEE,sBAAA,mBAAmB,SAAS,SAAS,KACrC,KAAK,KAAK,OAAO,UAAU,kBAC3B;AACgC,oDAAA;AAAA,kBAAA;AAAA,gBAClC;AAAA,cACF,CACD;AAAA,YAAA;AAAA,UACH;AAAA,QACF,CACD;AAED,YAAI,CAAC,+BAA+B;AAClC,cAAI,MAAO,SAAQ,KAAK,gCAAgC,EAAE;AAE1D,gBAAM,sBAAsB,SAAS;AAAA,YACnC,YAAY,SAAS,YAAY,gBAAgB;AAAA,UAAA,EACjD;AACE,cAAA,QAAQ,KAAK,QAAQ,mBAAmB;AAEtC,gBAAA,SAAS,gBAAgB,KAAK;AAAA,YAClC,YAAY;AAAA,YACZ,UAAU;AAAA,YACV,gBAAgB;AAAA,UAAA,CACjB;AACD,cAAI,OAAO;AACD,oBAAA,MAAM,OAAO,IAAI;AACzB,oBAAQ,IAAI,aAAa,OAAO,OAAO,MAAM;AAAA,UAAA;AAExC,iBAAA;AAAA,QAAA;AAGF,eAAA;AAAA,MAAA;AAAA,IAEX;AAAA,IAEA,MAAM;AAAA,MACJ,eAAe,QAAQ;AACrB,eAAO,OAAO;AACD,qBAAA,UAAU,SAAS,IAAI;AAAA,MAAA;AAAA,IAExC;AAAA,IAEA,SAAS;AACP,aAAO,QAAQ,IAAI;AACN,mBAAA,UAAU,SAAS,IAAI;AAAA,IACtC;AAAA,IAEA,UAAU;AACR,aAAO,QAAQ,IAAI;AACN,mBAAA,UAAU,SAAS,IAAI;AAAA,IAAA;AAAA,EAExC;AACF;"}