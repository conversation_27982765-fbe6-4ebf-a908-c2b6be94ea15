import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { Exam, ExamResult, PaginationInfo, SearchFilters, SortOptions, APIResponse } from '../../types'

interface ExamState {
  exams: Exam[]
  examResults: ExamResult[]
  selectedExam: Exam | null
  selectedResult: ExamResult | null
  loading: boolean
  error: string | null
  pagination: PaginationInfo
  filters: SearchFilters
  sort: SortOptions
}

interface ExamActions {
  setExams: (exams: Exam[]) => void
  setExamResults: (results: ExamResult[]) => void
  setSelectedExam: (exam: Exam | null) => void
  setSelectedResult: (result: ExamResult | null) => void
  addExam: (exam: Exam) => void
  addExamResult: (result: ExamResult) => void
  updateExam: (id: string, updates: Partial<Exam>) => void
  updateExamResult: (id: string, updates: Partial<ExamResult>) => void
  removeExam: (id: string) => void
  removeExamResult: (id: string) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setPagination: (pagination: PaginationInfo) => void
  setFilters: (filters: SearchFilters) => void
  setSort: (sort: SortOptions) => void
  resetFilters: () => void
  fetchExams: (page?: number, filters?: SearchFilters, sort?: SortOptions) => Promise<void>
  fetchExamById: (id: string) => Promise<void>
  fetchExamResults: (examId?: string, apprenticeId?: string) => Promise<void>
  fetchExamResultById: (id: string) => Promise<void>
  createExam: (exam: Omit<Exam, 'id'>) => Promise<void>
  createExamResult: (result: Omit<ExamResult, 'id'>) => Promise<void>
  updateExamData: (id: string, updates: Partial<Exam>) => Promise<void>
  updateExamResultData: (id: string, updates: Partial<ExamResult>) => Promise<void>
  deleteExam: (id: string) => Promise<void>
  deleteExamResult: (id: string) => Promise<void>
  searchExams: (searchTerm: string, page?: number) => Promise<void>
  getExamsByApprentice: (apprenticeId: string) => ExamResult[]
}

const initialState: ExamState = {
  exams: [],
  examResults: [],
  selectedExam: null,
  selectedResult: null,
  loading: false,
  error: null,
  pagination: {
    page: 1,
    pageSize: 10,
    totalItems: 0,
    totalPages: 0
  },
  filters: {},
  sort: {
    field: 'scheduledDate',
    direction: 'desc'
  }
}

export const useExamStore = create<ExamState & ExamActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

      setExams: (exams) => set({ exams }),
      
      setExamResults: (results) => set({ examResults: results }),
      
      setSelectedExam: (exam) => set({ selectedExam: exam }),
      
      setSelectedResult: (result) => set({ selectedResult: result }),
      
      addExam: (exam) => 
        set((state) => ({ 
          exams: [...state.exams, exam] 
        })),
      
      addExamResult: (result) => 
        set((state) => ({ 
          examResults: [...state.examResults, result] 
        })),
      
      updateExam: (id, updates) =>
        set((state) => ({
          exams: state.exams.map(e => 
            e.id === id ? { ...e, ...updates } : e
          ),
          selectedExam: state.selectedExam?.id === id 
            ? { ...state.selectedExam, ...updates } 
            : state.selectedExam
        })),
      
      updateExamResult: (id, updates) =>
        set((state) => ({
          examResults: state.examResults.map(r => 
            r.id === id ? { ...r, ...updates } : r
          ),
          selectedResult: state.selectedResult?.id === id 
            ? { ...state.selectedResult, ...updates } 
            : state.selectedResult
        })),
      
      removeExam: (id) =>
        set((state) => ({
          exams: state.exams.filter(e => e.id !== id),
          selectedExam: state.selectedExam?.id === id ? null : state.selectedExam
        })),
      
      removeExamResult: (id) =>
        set((state) => ({
          examResults: state.examResults.filter(r => r.id !== id),
          selectedResult: state.selectedResult?.id === id ? null : state.selectedResult
        })),
      
      setLoading: (loading) => set({ loading }),
      
      setError: (error) => set({ error }),
      
      setPagination: (pagination) => set({ pagination }),
      
      setFilters: (filters) => set({ filters }),
      
      setSort: (sort) => set({ sort }),
      
      resetFilters: () => set({ filters: {} }),

      fetchExams: async (page = 1, filters = {}, sort) => {
        set({ loading: true, error: null })
        try {
          const { pagination: currentPagination, sort: currentSort } = get()
          const sortToUse = sort || currentSort
          
          const params = new URLSearchParams({
            page: page.toString(),
            pageSize: currentPagination.pageSize.toString(),
            sortField: sortToUse.field,
            sortDirection: sortToUse.direction,
            ...Object.entries(filters).reduce((acc, [key, value]) => {
              if (value !== undefined && value !== null) {
                if (Array.isArray(value)) {
                  acc[key] = value.join(',')
                } else if (typeof value === 'object' && value !== null) {
                  Object.entries(value).forEach(([nestedKey, nestedValue]) => {
                    if (nestedValue !== undefined && nestedValue !== null) {
                      acc[`${key}.${nestedKey}`] = nestedValue.toString()
                    }
                  })
                } else {
                  acc[key] = value.toString()
                }
              }
              return acc
            }, {} as Record<string, string>)
          })

          const response = await fetch(`/api/exams?${params}`)
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<Exam[]> = await response.json()
          set({ 
            exams: result.data, 
            pagination: result.pagination!,
            filters,
            sort: sortToUse,
            loading: false 
          })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch exams'
          console.error('Exams fetch error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      searchExams: async (searchTerm, page = 1) => {
        const filters = { ...get().filters, searchTerm }
        await get().fetchExams(page, filters)
      },

      fetchExamById: async (id) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch(`/api/exams/${id}`)
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<Exam> = await response.json()
          set({ selectedExam: result.data, loading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch exam'
          console.error('Exam fetch error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      fetchExamResults: async (examId, apprenticeId) => {
        set({ loading: true, error: null })
        try {
          const params = new URLSearchParams()
          if (examId) params.append('examId', examId)
          if (apprenticeId) params.append('apprenticeId', apprenticeId)

          const response = await fetch(`/api/exam-results?${params}`)
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<ExamResult[]> = await response.json()
          set({ examResults: result.data, loading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch exam results'
          console.error('Exam results fetch error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      fetchExamResultById: async (id) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch(`/api/exam-results/${id}`)
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<ExamResult> = await response.json()
          set({ selectedResult: result.data, loading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch exam result'
          console.error('Exam result fetch error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      createExam: async (examData) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch('/api/exams', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(examData)
          })
          
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<Exam> = await response.json()
          set((state) => ({ 
            exams: [...state.exams, result.data],
            loading: false 
          }))
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to create exam'
          console.error('Exam create error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      createExamResult: async (resultData) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch('/api/exam-results', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(resultData)
          })
          
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<ExamResult> = await response.json()
          set((state) => ({ 
            examResults: [...state.examResults, result.data],
            loading: false 
          }))
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to create exam result'
          console.error('Exam result create error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      updateExamData: async (id, updates) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch(`/api/exams/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(updates)
          })
          
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<Exam> = await response.json()
          get().updateExam(id, result.data)
          set({ loading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to update exam'
          console.error('Exam update error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      updateExamResultData: async (id, updates) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch(`/api/exam-results/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(updates)
          })
          
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<ExamResult> = await response.json()
          get().updateExamResult(id, result.data)
          set({ loading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to update exam result'
          console.error('Exam result update error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      deleteExam: async (id) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch(`/api/exams/${id}`, {
            method: 'DELETE'
          })
          
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          get().removeExam(id)
          set({ loading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to delete exam'
          console.error('Exam delete error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      deleteExamResult: async (id) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch(`/api/exam-results/${id}`, {
            method: 'DELETE'
          })
          
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          get().removeExamResult(id)
          set({ loading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to delete exam result'
          console.error('Exam result delete error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      getExamsByApprentice: (apprenticeId) => {
        return get().examResults.filter(r => r.apprenticeId === apprenticeId)
      }
    }),
    { name: 'exam-store' }
  )
)