import { useEffect, useState } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { useApprenticeStore } from '../apprenticeStore'

export function Apprentices() {
  const { apprentices, loading, error, fetchApprentices, createApprentice } = useApprenticeStore()
  const [isCreating, setIsCreating] = useState(false)

  useEffect(() => {
    fetchApprentices()
  }, [fetchApprentices])

  const handleAddApprentice = async () => {
    setIsCreating(true)
    try {
      // Create a sample apprentice for demo purposes
      await createApprentice({
        firstName: 'New',
        lastName: 'Apprentice',
        email: `apprentice${Date.now()}@example.com`,
        position: 'Junior Developer',
        department: 'Technology',
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
        mentor: '<PERSON>',
        status: 'active'
      })
      
      // Refresh the list
      await fetchApprentices()
      
      // In a real app, you would navigate to a form or open a modal
      alert('Sample apprentice created! In a real app, this would open a form.')
    } catch (error) {
      console.error('Failed to create apprentice:', error)
    } finally {
      setIsCreating(false)
    }
  }

  const handleViewDetails = (apprenticeId: string) => {
    // In a real app, this would navigate to a details page
    alert(`View details for apprentice ${apprenticeId}. In a real app, this would navigate to a details page.`)
    // navigate({ to: `/apprentices/${apprenticeId}` })
  }

  if (loading && !isCreating) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading apprentices...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-red-600">Error: {error}</div>
      </div>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      case 'suspended':
        return 'bg-yellow-100 text-yellow-800'
      case 'withdrawn':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between px-4 sm:px-0">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Apprentices</h1>
          <p className="mt-2 text-sm text-gray-700">
            Manage and track all apprentices in the program.
          </p>
        </div>
        <Button onClick={handleAddApprentice} disabled={isCreating}>
          {isCreating ? 'Creating...' : 'Add Apprentice'}
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {apprentices.map((apprentice) => (
          <Card key={apprentice.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">
                  {apprentice.firstName} {apprentice.lastName}
                </CardTitle>
                <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getStatusColor(apprentice.status)}`}>
                  {apprentice.status}
                </span>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-900">{apprentice.position}</p>
                  <p className="text-xs text-gray-500">{apprentice.department}</p>
                </div>
                
                <div>
                  <p className="text-xs text-gray-500 mb-1">Progress</p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${apprentice.overallProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">{apprentice.overallProgress}% complete</p>
                </div>

                <div className="grid grid-cols-2 gap-3 text-xs">
                  <div>
                    <p className="text-gray-500">Mentor</p>
                    <p className="font-medium">{apprentice.mentor}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Started</p>
                    <p className="font-medium">{new Date(apprentice.startDate).toLocaleDateString()}</p>
                  </div>
                </div>

                <div className="pt-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full"
                    onClick={() => handleViewDetails(apprentice.id)}
                  >
                    View Details
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {apprentices.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No apprentices found.</p>
          <Button className="mt-4" onClick={handleAddApprentice} disabled={isCreating}>
            {isCreating ? 'Creating...' : 'Add First Apprentice'}
          </Button>
        </div>
      )}
    </div>
  )
}