import { 
  Notification,
  CalendarEvent,
  ExamQuestion,
  ExamSubmission,
  AuditLog,
  AnalyticsData,
  FileUpload,
  BulkOperation,
  Department
} from '../types'

// Helper function to generate realistic dates
const generateDate = (daysAgo: number): string => {
  const date = new Date()
  date.setDate(date.getDate() - daysAgo)
  return date.toISOString().split('T')[0]
}

const generateDateTime = (daysAgo: number, hour: number = 9): string => {
  const date = new Date()
  date.setDate(date.getDate() - daysAgo)
  date.setHours(hour, 0, 0, 0)
  return date.toISOString()
}

// Mock Departments
export const mockDepartments: Department[] = [
  {
    id: '1',
    name: 'Engineering',
    description: 'Software development, DevOps, and technical innovation',
    manager: '<PERSON>',
    managerId: 'mgr-1',
    budget: 250000,
    totalApprentices: 8,
    activeApprentices: 6,
    location: 'London Office - Floor 3',
    createdAt: generateDateTime(365),
    updatedAt: generateDateTime(30)
  },
  {
    id: '2',
    name: 'Marketing',
    description: 'Digital marketing, content creation, and brand management',
    manager: '<PERSON>',
    managerId: 'mgr-2',
    budget: 150000,
    totalApprentices: 5,
    activeApprentices: 4,
    location: 'Manchester Office - Floor 2',
    createdAt: generateDateTime(365),
    updatedAt: generateDateTime(15)
  },
  {
    id: '3',
    name: 'HR',
    description: 'Human resources, recruitment, and employee development',
    manager: 'David Brown',
    managerId: 'mgr-3',
    budget: 100000,
    totalApprentices: 3,
    activeApprentices: 3,
    location: 'Birmingham Office - Floor 1',
    createdAt: generateDateTime(365),
    updatedAt: generateDateTime(7)
  },
  {
    id: '4',
    name: 'Finance',
    description: 'Financial analysis, accounting, and business intelligence',
    manager: 'Lisa Johnson',
    managerId: 'mgr-4',
    budget: 120000,
    totalApprentices: 4,
    activeApprentices: 3,
    location: 'London Office - Floor 2',
    createdAt: generateDateTime(365),
    updatedAt: generateDateTime(20)
  }
]

// Mock Notifications
export const mockNotifications: Notification[] = [
  {
    id: '1',
    userId: '1',
    type: 'info',
    title: 'Quarterly Review Scheduled',
    message: 'Your Q4 2024 review has been scheduled for December 15th at 2:00 PM',
    actionUrl: '/reviews/upcoming',
    actionText: 'View Details',
    isRead: false,
    createdAt: generateDateTime(2),
    expiresAt: generateDateTime(-30)
  },
  {
    id: '2',
    userId: '2',
    type: 'success',
    title: 'Exam Passed',
    message: 'Congratulations! You have successfully passed the Digital Marketing Strategy exam with a score of 85%',
    actionUrl: '/exams/results/3',
    actionText: 'View Results',
    isRead: true,
    createdAt: generateDateTime(5),
  },
  {
    id: '3',
    userId: '4',
    type: 'warning',
    title: 'Progress Update Required',
    message: 'Please update your progress for the HR Administration module',
    actionUrl: '/progress/update',
    actionText: 'Update Progress',
    isRead: false,
    createdAt: generateDateTime(1),
    expiresAt: generateDateTime(-7)
  },
  {
    id: '4',
    userId: '1',
    type: 'info',
    title: 'New Learning Module Available',
    message: 'Advanced React Patterns module is now available for enrollment',
    actionUrl: '/modules/advanced-react',
    actionText: 'Enroll Now',
    isRead: false,
    createdAt: generateDateTime(3)
  },
  {
    id: '5',
    userId: '3',
    type: 'error',
    title: 'Document Missing',
    message: 'Please upload your certification document for the completed module',
    actionUrl: '/documents/upload',
    actionText: 'Upload Document',
    isRead: false,
    createdAt: generateDateTime(7),
    expiresAt: generateDateTime(-14)
  }
]

// Mock Calendar Events
export const mockCalendarEvents: CalendarEvent[] = [
  {
    id: '1',
    title: 'Q4 Quarterly Review - John Smith',
    description: 'Performance review meeting with mentor Sarah Johnson',
    type: 'review',
    startDate: generateDateTime(-5, 14),
    endDate: generateDateTime(-5, 15),
    location: 'Conference Room A',
    participants: ['1', '1'],
    apprenticeId: '1',
    mentorId: '1',
    isRecurring: false,
    reminders: [60, 15],
    status: 'completed',
    createdAt: generateDateTime(10),
    updatedAt: generateDateTime(5)
  },
  {
    id: '2',
    title: 'JavaScript Fundamentals Exam',
    description: 'Comprehensive assessment of JavaScript knowledge',
    type: 'exam',
    startDate: generateDateTime(7, 9),
    endDate: generateDateTime(7, 11),
    location: 'Testing Center',
    participants: ['1', '2', '3'],
    isRecurring: false,
    reminders: [1440, 60],
    status: 'scheduled',
    createdAt: generateDateTime(20),
    updatedAt: generateDateTime(15)
  },
  {
    id: '3',
    title: 'Team Building Workshop',
    description: 'Department-wide team building activities',
    type: 'training',
    startDate: generateDateTime(14, 10),
    endDate: generateDateTime(14, 16),
    location: 'Main Hall',
    participants: ['1', '2', '3', '4', '5'],
    isRecurring: false,
    reminders: [2880, 1440],
    status: 'scheduled',
    createdAt: generateDateTime(30),
    updatedAt: generateDateTime(25)
  },
  {
    id: '4',
    title: 'Weekly Mentor Check-in',
    description: 'Regular mentor-apprentice meeting',
    type: 'meeting',
    startDate: generateDateTime(1, 15),
    endDate: generateDateTime(1, 16),
    location: 'Sarah\'s Office',
    participants: ['1', '1'],
    apprenticeId: '1',
    mentorId: '1',
    isRecurring: true,
    recurrenceRule: 'FREQ=WEEKLY;BYDAY=FR',
    reminders: [60],
    status: 'scheduled',
    createdAt: generateDateTime(60),
    updatedAt: generateDateTime(8)
  },
  {
    id: '5',
    title: 'Project Presentation Deadline',
    description: 'Final submission deadline for React project',
    type: 'deadline',
    startDate: generateDateTime(3, 17),
    location: 'Online Submission',
    participants: ['1'],
    apprenticeId: '1',
    isRecurring: false,
    reminders: [4320, 1440, 60],
    status: 'scheduled',
    createdAt: generateDateTime(45),
    updatedAt: generateDateTime(40)
  }
]

// Mock Exam Questions
export const mockExamQuestions: ExamQuestion[] = [
  {
    id: '1',
    examId: '1',
    question: 'What is the difference between let and var in JavaScript?',
    type: 'multiple-choice',
    options: [
      'let has block scope, var has function scope',
      'let has function scope, var has block scope', 
      'There is no difference',
      'let is hoisted, var is not'
    ],
    correctAnswer: 0,
    points: 10,
    order: 1,
    explanation: 'let has block scope while var has function scope, which is one of the key differences introduced in ES6.'
  },
  {
    id: '2',
    examId: '1',
    question: 'Explain the concept of closure in JavaScript and provide an example.',
    type: 'essay',
    points: 20,
    order: 2,
    explanation: 'A closure is a function that has access to variables in its outer (enclosing) scope even after the outer function has returned.'
  },
  {
    id: '3',
    examId: '1',
    question: 'What is the output of console.log(typeof null)?',
    type: 'short-answer',
    correctAnswer: 'object',
    points: 5,
    order: 3,
    explanation: 'This is a well-known JavaScript quirk where typeof null returns "object" instead of "null".'
  },
  {
    id: '4',
    examId: '1',
    question: 'Write a function that debounces another function call.',
    type: 'code',
    points: 25,
    order: 4,
    explanation: 'A debounce function delays the execution of a function until after a specified delay has elapsed since the last time it was called.'
  },
  {
    id: '5',
    examId: '2',
    question: 'What is the virtual DOM in React?',
    type: 'multiple-choice',
    options: [
      'A JavaScript representation of the real DOM',
      'A testing framework for React',
      'A state management library',
      'A routing system'
    ],
    correctAnswer: 0,
    points: 10,
    order: 1,
    explanation: 'The virtual DOM is a JavaScript representation of the real DOM that React uses to optimize rendering performance.'
  }
]

// Mock Exam Submissions
export const mockExamSubmissions: ExamSubmission[] = [
  {
    id: '1',
    examId: '1',
    apprenticeId: '1',
    questionId: '1',
    answer: '0',
    isCorrect: true,
    pointsEarned: 10,
    submittedAt: generateDateTime(30, 10)
  },
  {
    id: '2',
    examId: '1',
    apprenticeId: '1',
    questionId: '2',
    answer: 'A closure is a function that has access to variables in its outer scope even after the outer function has returned. For example: function outer() { let x = 10; return function inner() { console.log(x); }; } const closureFunction = outer(); closureFunction(); // logs 10',
    isCorrect: true,
    pointsEarned: 18,
    submittedAt: generateDateTime(30, 10)
  },
  {
    id: '3',
    examId: '1',
    apprenticeId: '1',
    questionId: '3',
    answer: 'object',
    isCorrect: true,
    pointsEarned: 5,
    submittedAt: generateDateTime(30, 10)
  },
  {
    id: '4',
    examId: '1',
    apprenticeId: '1',
    questionId: '4',
    answer: 'function debounce(func, delay) { let timeoutId; return function(...args) { clearTimeout(timeoutId); timeoutId = setTimeout(() => func.apply(this, args), delay); }; }',
    isCorrect: true,
    pointsEarned: 25,
    submittedAt: generateDateTime(30, 10)
  }
]

// Mock Audit Logs
export const mockAuditLogs: AuditLog[] = [
  {
    id: '1',
    userId: '1',
    userType: 'apprentice',
    action: 'LOGIN',
    resourceType: 'system',
    resourceId: 'auth',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    timestamp: generateDateTime(0, 9)
  },
  {
    id: '2',
    userId: '1',
    userType: 'mentor',
    action: 'UPDATE_REVIEW',
    resourceType: 'review',
    resourceId: '1',
    changes: {
      scores: { technicalSkills: 7, communication: 8 },
      mentorComments: 'Updated feedback based on recent performance'
    },
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    timestamp: generateDateTime(1, 14)
  },
  {
    id: '3',
    userId: '2',
    userType: 'apprentice',
    action: 'SUBMIT_EXAM',
    resourceType: 'exam',
    resourceId: '1',
    changes: {
      score: 85,
      timeSpent: 110,
      passed: true
    },
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    timestamp: generateDateTime(2, 11)
  },
  {
    id: '4',
    userId: 'admin',
    userType: 'admin',
    action: 'CREATE_APPRENTICE',
    resourceType: 'apprentice',
    resourceId: '12',
    changes: {
      firstName: 'Grace',
      lastName: 'Turner',
      department: 'HR',
      mentor: 'Lisa Green'
    },
    ipAddress: '************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    timestamp: generateDateTime(47, 16)
  }
]

// Mock Analytics Data
export const mockAnalyticsData: AnalyticsData = {
  apprenticeProgressTrends: [
    { date: generateDate(180), averageProgress: 25, activeApprentices: 8 },
    { date: generateDate(150), averageProgress: 32, activeApprentices: 9 },
    { date: generateDate(120), averageProgress: 38, activeApprentices: 10 },
    { date: generateDate(90), averageProgress: 45, activeApprentices: 11 },
    { date: generateDate(60), averageProgress: 52, activeApprentices: 11 },
    { date: generateDate(30), averageProgress: 58, activeApprentices: 10 },
    { date: generateDate(0), averageProgress: 62, activeApprentices: 10 }
  ],
  departmentPerformance: [
    {
      department: 'Engineering',
      averageProgress: 68,
      completionRate: 92,
      retentionRate: 95
    },
    {
      department: 'Marketing',
      averageProgress: 72,
      completionRate: 88,
      retentionRate: 90
    },
    {
      department: 'HR',
      averageProgress: 58,
      completionRate: 85,
      retentionRate: 88
    },
    {
      department: 'Finance',
      averageProgress: 65,
      completionRate: 90,
      retentionRate: 92
    }
  ],
  examPerformance: [
    {
      examId: '1',
      examTitle: 'JavaScript Fundamentals',
      averageScore: 78,
      passRate: 85,
      totalAttempts: 12
    },
    {
      examId: '2',
      examTitle: 'React Development Project',
      averageScore: 82,
      passRate: 90,
      totalAttempts: 8
    },
    {
      examId: '3',
      examTitle: 'Digital Marketing Strategy',
      averageScore: 75,
      passRate: 80,
      totalAttempts: 10
    }
  ],
  mentorEffectiveness: [
    {
      mentorId: '1',
      mentorName: 'Sarah Johnson',
      averageApprenticeProgress: 72,
      apprenticeCount: 2,
      reviewRating: 8.5
    },
    {
      mentorId: '2',
      mentorName: 'Mike Brown',
      averageApprenticeProgress: 68,
      apprenticeCount: 3,
      reviewRating: 8.2
    },
    {
      mentorId: '3',
      mentorName: 'Lisa Green',
      averageApprenticeProgress: 58,
      apprenticeCount: 3,
      reviewRating: 7.8
    },
    {
      mentorId: '4',
      mentorName: 'James Wilson',
      averageApprenticeProgress: 62,
      apprenticeCount: 2,
      reviewRating: 7.5
    },
    {
      mentorId: '5',
      mentorName: 'Emma Davis',
      averageApprenticeProgress: 45,
      apprenticeCount: 2,
      reviewRating: 7.2
    }
  ],
  monthlyStats: [
    { month: 'Jan 2024', newApprentices: 3, completedApprentices: 0, examsCompleted: 2, reviewsCompleted: 4 },
    { month: 'Feb 2024', newApprentices: 2, completedApprentices: 0, examsCompleted: 3, reviewsCompleted: 3 },
    { month: 'Mar 2024', newApprentices: 2, completedApprentices: 0, examsCompleted: 4, reviewsCompleted: 5 },
    { month: 'Apr 2024', newApprentices: 2, completedApprentices: 0, examsCompleted: 2, reviewsCompleted: 2 },
    { month: 'May 2024', newApprentices: 1, completedApprentices: 0, examsCompleted: 3, reviewsCompleted: 4 },
    { month: 'Jun 2024', newApprentices: 1, completedApprentices: 1, examsCompleted: 5, reviewsCompleted: 6 },
    { month: 'Jul 2024', newApprentices: 0, completedApprentices: 0, examsCompleted: 3, reviewsCompleted: 3 },
    { month: 'Aug 2024', newApprentices: 0, completedApprentices: 0, examsCompleted: 2, reviewsCompleted: 2 },
    { month: 'Sep 2024', newApprentices: 0, completedApprentices: 1, examsCompleted: 4, reviewsCompleted: 5 },
    { month: 'Oct 2024', newApprentices: 0, completedApprentices: 0, examsCompleted: 2, reviewsCompleted: 3 },
    { month: 'Nov 2024', newApprentices: 0, completedApprentices: 0, examsCompleted: 1, reviewsCompleted: 2 },
    { month: 'Dec 2024', newApprentices: 0, completedApprentices: 1, examsCompleted: 3, reviewsCompleted: 4 }
  ]
}

// Mock File Uploads
export const mockFileUploads: FileUpload[] = [
  {
    id: '1',
    filename: 'john-smith-cv.pdf',
    originalName: 'John Smith CV.pdf',
    mimeType: 'application/pdf',
    size: 245760,
    uploadedBy: '1',
    uploadedAt: generateDateTime(180),
    category: 'document',
    relatedTo: {
      type: 'apprentice',
      id: '1'
    },
    url: '/uploads/documents/john-smith-cv.pdf',
    isPublic: false
  },
  {
    id: '2',
    filename: 'quarterly-review-q2-2024.pdf',
    originalName: 'Quarterly Review Q2 2024.pdf',
    mimeType: 'application/pdf',
    size: 156890,
    uploadedBy: '1',
    uploadedAt: generateDateTime(120),
    category: 'report',
    relatedTo: {
      type: 'review',
      id: '1'
    },
    url: '/uploads/reports/quarterly-review-q2-2024.pdf',
    isPublic: false
  },
  {
    id: '3',
    filename: 'react-certification.jpg',
    originalName: 'React Certification.jpg',
    mimeType: 'image/jpeg',
    size: 892340,
    uploadedBy: '1',
    uploadedAt: generateDateTime(90),
    category: 'certificate',
    relatedTo: {
      type: 'progress',
      id: '2'
    },
    url: '/uploads/certificates/react-certification.jpg',
    isPublic: true
  },
  {
    id: '4',
    filename: 'profile-photo.jpg',
    originalName: 'Profile Photo.jpg',
    mimeType: 'image/jpeg',
    size: 567890,
    uploadedBy: '2',
    uploadedAt: generateDateTime(165),
    category: 'image',
    relatedTo: {
      type: 'apprentice',
      id: '2'
    },
    url: '/uploads/images/profile-photo.jpg',
    isPublic: true
  }
]

// Mock Bulk Operations
export const mockBulkOperations: BulkOperation[] = [
  {
    id: '1',
    type: 'import',
    resourceType: 'apprentice',
    status: 'completed',
    totalRecords: 15,
    processedRecords: 15,
    failedRecords: 0,
    fileUrl: '/uploads/bulk/apprentices-import.csv',
    createdBy: 'admin',
    createdAt: generateDateTime(30, 10),
    completedAt: generateDateTime(30, 10)
  },
  {
    id: '2',
    type: 'export',
    resourceType: 'review',
    status: 'completed',
    totalRecords: 25,
    processedRecords: 25,
    failedRecords: 0,
    fileUrl: '/uploads/bulk/reviews-export.xlsx',
    createdBy: 'admin',
    createdAt: generateDateTime(7, 14),
    completedAt: generateDateTime(7, 14)
  },
  {
    id: '3',
    type: 'update',
    resourceType: 'apprentice',
    status: 'in-progress',
    totalRecords: 12,
    processedRecords: 8,
    failedRecords: 1,
    errors: ['Invalid email format for record 5'],
    createdBy: 'admin',
    createdAt: generateDateTime(1, 9)
  },
  {
    id: '4',
    type: 'export',
    resourceType: 'exam',
    status: 'failed',
    totalRecords: 0,
    processedRecords: 0,
    failedRecords: 0,
    errors: ['Database connection timeout'],
    createdBy: 'admin',
    createdAt: generateDateTime(2, 11),
    completedAt: generateDateTime(2, 11)
  }
]