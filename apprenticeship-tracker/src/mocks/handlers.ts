import { http, HttpResponse } from 'msw'
import { 
  mockApprentices, 
  mockQuarterlyReviews, 
  mockExams, 
  mockExamResults, 
  mockProgressModules, 
  mockApprenticeProgress,
  mockMentors,
  mockDepartments
} from '../utils/mock-data'
import {
  mockNotifications,
  mockCalendarEvents,
  mockExamQuestions,
  mockExamSubmissions,
  mockAuditLogs,
  mockAnalyticsData,
  mockFileUploads,
  mockBulkOperations
} from '../utils/enhanced-mock-data'
import { DashboardStats, SearchFilters, SortOptions, APIResponse } from '../types'

// Helper function to add realistic delays
const delay = (ms: number = 100) => new Promise(resolve => setTimeout(resolve, ms))

// Helper function to generate dashboard stats
const generateDashboardStats = (): DashboardStats => {
  const activeApprentices = mockApprentices.filter(a => a.status === 'active').length
  const completedApprentices = mockApprentices.filter(a => a.status === 'completed').length
  const averageProgress = mockApprentices.reduce((sum, a) => sum + a.overallProgress, 0) / mockApprentices.length
  
  return {
    totalApprentices: mockApprentices.length,
    activeApprentices,
    completedApprentices,
    averageProgress: Math.round(averageProgress),
    upcomingReviews: mockQuarterlyReviews.filter(r => r.status === 'scheduled').length,
    overdueReviews: mockQuarterlyReviews.filter(r => r.status === 'overdue').length,
    upcomingExams: mockExams.filter(e => e.status === 'upcoming').length,
    passRate: 85
  }
}

// Helper function to apply search filters
const applySearchFilters = (items: any[], filters: SearchFilters): any[] => {
  return items.filter(item => {
    // Search term filter
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase()
      const searchableFields = ['firstName', 'lastName', 'email', 'department', 'position', 'mentor']
      const matchesSearch = searchableFields.some(field => 
        item[field]?.toLowerCase().includes(searchTerm)
      )
      if (!matchesSearch) return false
    }

    // Status filter
    if (filters.status && filters.status.length > 0) {
      if (!filters.status.includes(item.status)) return false
    }

    // Department filter
    if (filters.department && filters.department.length > 0) {
      if (!filters.department.includes(item.department)) return false
    }

    // Mentor filter
    if (filters.mentor && filters.mentor.length > 0) {
      if (!filters.mentor.includes(item.mentor)) return false
    }

    // Progress filter
    if (filters.progress) {
      if (filters.progress.min !== undefined && item.overallProgress < filters.progress.min) return false
      if (filters.progress.max !== undefined && item.overallProgress > filters.progress.max) return false
    }

    // Date range filter
    if (filters.dateRange) {
      const itemDate = new Date(item.startDate || item.reviewDate || item.createdAt)
      if (filters.dateRange.start && itemDate < new Date(filters.dateRange.start)) return false
      if (filters.dateRange.end && itemDate > new Date(filters.dateRange.end)) return false
    }

    return true
  })
}

// Helper function to apply sorting
const applySorting = (items: any[], sort: SortOptions): any[] => {
  return [...items].sort((a, b) => {
    const aValue = a[sort.field]
    const bValue = b[sort.field]
    
    if (aValue < bValue) return sort.direction === 'asc' ? -1 : 1
    if (aValue > bValue) return sort.direction === 'asc' ? 1 : -1
    return 0
  })
}

// Helper function to apply pagination
const applyPagination = (items: any[], page: number, pageSize: number) => {
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  return {
    data: items.slice(startIndex, endIndex),
    pagination: {
      page,
      pageSize,
      totalItems: items.length,
      totalPages: Math.ceil(items.length / pageSize)
    }
  }
}

// Helper function to create API response
const createAPIResponse = <T>(data: T, message?: string, pagination?: any, filters?: SearchFilters, sort?: SortOptions): APIResponse<T> => {
  return {
    data,
    message,
    pagination,
    filters,
    sort,
    timestamp: new Date().toISOString()
  }
}

// Import enhanced handlers
import { enhancedHandlers } from './enhanced-handlers'

export const handlers = enhancedHandlers