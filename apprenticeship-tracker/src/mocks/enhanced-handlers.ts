import { http, HttpResponse } from 'msw'
import { 
  mockApprentices, 
  mockQuarterlyReviews, 
  mockExams, 
  mockExamResults, 
  mockProgressModules, 
  mockApprenticeProgress,
  mockMentors,
  mockDepartments
} from '../utils/mock-data'
import {
  mockNotifications,
  mockCalendarEvents,
  mockExamQuestions,
  mockExamSubmissions,
  mockAuditLogs,
  mockAnalyticsData,
  mockFileUploads,
  mockBulkOperations
} from '../utils/enhanced-mock-data'
import { DashboardStats, SearchFilters, SortOptions, APIResponse } from '../types'

// Helper function to add realistic delays
const delay = (ms: number = 100) => new Promise(resolve => setTimeout(resolve, ms))

// Helper function to generate dashboard stats
const generateDashboardStats = (): DashboardStats => {
  const activeApprentices = mockApprentices.filter(a => a.status === 'active').length
  const completedApprentices = mockApprentices.filter(a => a.status === 'completed').length
  const averageProgress = mockApprentices.reduce((sum, a) => sum + a.overallProgress, 0) / mockApprentices.length
  
  return {
    totalApprentices: mockApprentices.length,
    activeApprentices,
    completedApprentices,
    averageProgress: Math.round(averageProgress),
    upcomingReviews: mockQuarterlyReviews.filter(r => r.status === 'scheduled').length,
    overdueReviews: mockQuarterlyReviews.filter(r => r.status === 'overdue').length,
    upcomingExams: mockExams.filter(e => e.status === 'upcoming').length,
    passRate: 85
  }
}

// Helper function to apply search filters
const applySearchFilters = (items: any[], filters: SearchFilters): any[] => {
  return items.filter(item => {
    // Search term filter
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase()
      const searchableFields = ['firstName', 'lastName', 'email', 'department', 'position', 'mentor']
      const matchesSearch = searchableFields.some(field => 
        item[field]?.toLowerCase().includes(searchTerm)
      )
      if (!matchesSearch) return false
    }

    // Status filter
    if (filters.status && filters.status.length > 0) {
      if (!filters.status.includes(item.status)) return false
    }

    // Department filter
    if (filters.department && filters.department.length > 0) {
      if (!filters.department.includes(item.department)) return false
    }

    // Mentor filter
    if (filters.mentor && filters.mentor.length > 0) {
      if (!filters.mentor.includes(item.mentor)) return false
    }

    // Progress filter
    if (filters.progress) {
      if (filters.progress.min !== undefined && item.overallProgress < filters.progress.min) return false
      if (filters.progress.max !== undefined && item.overallProgress > filters.progress.max) return false
    }

    // Date range filter
    if (filters.dateRange) {
      const itemDate = new Date(item.startDate || item.reviewDate || item.createdAt)
      if (filters.dateRange.start && itemDate < new Date(filters.dateRange.start)) return false
      if (filters.dateRange.end && itemDate > new Date(filters.dateRange.end)) return false
    }

    return true
  })
}

// Helper function to apply sorting
const applySorting = (items: any[], sort: SortOptions): any[] => {
  return [...items].sort((a, b) => {
    const aValue = a[sort.field]
    const bValue = b[sort.field]
    
    if (aValue < bValue) return sort.direction === 'asc' ? -1 : 1
    if (aValue > bValue) return sort.direction === 'asc' ? 1 : -1
    return 0
  })
}

// Helper function to apply pagination
const applyPagination = (items: any[], page: number, pageSize: number) => {
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  return {
    data: items.slice(startIndex, endIndex),
    pagination: {
      page,
      pageSize,
      totalItems: items.length,
      totalPages: Math.ceil(items.length / pageSize)
    }
  }
}

// Helper function to create API response
const createAPIResponse = <T>(data: T, message?: string, pagination?: any, filters?: SearchFilters, sort?: SortOptions): APIResponse<T> => {
  return {
    data,
    message,
    pagination,
    filters,
    sort,
    timestamp: new Date().toISOString()
  }
}

export const enhancedHandlers = [
  // ========================
  // DASHBOARD ENDPOINTS
  // ========================
  http.get('/api/dashboard/stats', async () => {
    await delay(300)
    return HttpResponse.json(generateDashboardStats())
  }),

  http.get('/api/dashboard/analytics', async () => {
    await delay(500)
    return HttpResponse.json(mockAnalyticsData)
  }),

  // ========================
  // APPRENTICES ENDPOINTS
  // ========================
  http.get('/api/apprentices', async ({ request }) => {
    await delay(200)
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10')
    const searchTerm = url.searchParams.get('search') || ''
    const sortField = url.searchParams.get('sortField') || 'firstName'
    const sortDirection = url.searchParams.get('sortDirection') || 'asc'
    
    const filters: SearchFilters = {
      searchTerm,
      status: url.searchParams.get('status')?.split(',') || [],
      department: url.searchParams.get('department')?.split(',') || [],
      mentor: url.searchParams.get('mentor')?.split(',') || [],
      progress: {
        min: url.searchParams.get('progressMin') ? parseInt(url.searchParams.get('progressMin')!) : undefined,
        max: url.searchParams.get('progressMax') ? parseInt(url.searchParams.get('progressMax')!) : undefined
      },
      dateRange: {
        start: url.searchParams.get('dateStart') || undefined,
        end: url.searchParams.get('dateEnd') || undefined
      }
    }
    
    const sort: SortOptions = {
      field: sortField,
      direction: sortDirection as 'asc' | 'desc'
    }
    
    let filteredApprentices = applySearchFilters(mockApprentices, filters)
    filteredApprentices = applySorting(filteredApprentices, sort)
    
    const paginatedResult = applyPagination(filteredApprentices, page, pageSize)
    
    return HttpResponse.json(createAPIResponse(
      paginatedResult.data,
      `Found ${paginatedResult.pagination.totalItems} apprentices`,
      paginatedResult.pagination,
      filters,
      sort
    ))
  }),

  http.get('/api/apprentices/search-suggestions', async ({ request }) => {
    await delay(100)
    const url = new URL(request.url)
    const query = url.searchParams.get('q') || ''
    
    if (!query) {
      return HttpResponse.json({ suggestions: [] })
    }
    
    const suggestions = mockApprentices
      .filter(a => 
        a.firstName.toLowerCase().includes(query.toLowerCase()) ||
        a.lastName.toLowerCase().includes(query.toLowerCase()) ||
        a.email.toLowerCase().includes(query.toLowerCase())
      )
      .slice(0, 10)
      .map(a => ({
        id: a.id,
        label: `${a.firstName} ${a.lastName}`,
        value: a.email,
        department: a.department
      }))
    
    return HttpResponse.json({ suggestions })
  }),

  http.get('/api/apprentices/:id', async ({ params }) => {
    await delay(150)
    const apprentice = mockApprentices.find(a => a.id === params.id)
    if (!apprentice) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(createAPIResponse(apprentice))
  }),

  http.post('/api/apprentices', async ({ request }) => {
    await delay(300)
    const newApprentice = await request.json() as any
    const apprentice = {
      id: Date.now().toString(),
      ...newApprentice,
      overallProgress: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    mockApprentices.push(apprentice)
    return HttpResponse.json(createAPIResponse(apprentice, 'Apprentice created successfully'), { status: 201 })
  }),

  http.put('/api/apprentices/:id', async ({ params, request }) => {
    await delay(250)
    const updates = await request.json() as any
    const index = mockApprentices.findIndex(a => a.id === params.id)
    if (index === -1) {
      return new HttpResponse(null, { status: 404 })
    }
    mockApprentices[index] = { 
      ...mockApprentices[index], 
      ...updates, 
      updatedAt: new Date().toISOString() 
    }
    return HttpResponse.json(createAPIResponse(mockApprentices[index], 'Apprentice updated successfully'))
  }),

  http.delete('/api/apprentices/:id', async ({ params }) => {
    await delay(200)
    const index = mockApprentices.findIndex(a => a.id === params.id)
    if (index === -1) {
      return new HttpResponse(null, { status: 404 })
    }
    mockApprentices.splice(index, 1)
    return HttpResponse.json(createAPIResponse(null, 'Apprentice deleted successfully'), { status: 204 })
  }),

  // Bulk operations for apprentices
  http.post('/api/apprentices/bulk-import', async ({ request }) => {
    await delay(2000) // Simulate longer processing time
    const formData = await request.formData()
    const file = formData.get('file')
    
    if (!file) {
      return new HttpResponse(JSON.stringify({ error: 'No file provided' }), { status: 400 })
    }
    
    const operation = {
      id: Date.now().toString(),
      type: 'import' as const,
      resourceType: 'apprentice',
      status: 'completed' as const,
      totalRecords: 5,
      processedRecords: 5,
      failedRecords: 0,
      createdBy: 'admin',
      createdAt: new Date().toISOString(),
      completedAt: new Date().toISOString()
    }
    
    mockBulkOperations.push(operation)
    return HttpResponse.json(createAPIResponse(operation, 'Bulk import completed successfully'))
  }),

  http.get('/api/apprentices/export', async ({ request }) => {
    await delay(1000)
    const url = new URL(request.url)
    const format = url.searchParams.get('format') || 'csv'
    
    const operation = {
      id: Date.now().toString(),
      type: 'export' as const,
      resourceType: 'apprentice',
      status: 'completed' as const,
      totalRecords: mockApprentices.length,
      processedRecords: mockApprentices.length,
      failedRecords: 0,
      fileUrl: `/downloads/apprentices-export.${format}`,
      createdBy: 'admin',
      createdAt: new Date().toISOString(),
      completedAt: new Date().toISOString()
    }
    
    mockBulkOperations.push(operation)
    return HttpResponse.json(createAPIResponse(operation, 'Export completed successfully'))
  }),

  // ========================
  // DEPARTMENTS ENDPOINTS
  // ========================
  http.get('/api/departments', async () => {
    await delay(100)
    return HttpResponse.json(createAPIResponse(mockDepartments))
  }),

  http.get('/api/departments/:id', async ({ params }) => {
    await delay(120)
    const department = mockDepartments.find(d => d.id === params.id)
    if (!department) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(createAPIResponse(department))
  }),

  http.post('/api/departments', async ({ request }) => {
    await delay(200)
    const newDepartment = await request.json() as any
    const department = {
      id: Date.now().toString(),
      ...newDepartment,
      totalApprentices: 0,
      activeApprentices: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    mockDepartments.push(department)
    return HttpResponse.json(createAPIResponse(department, 'Department created successfully'), { status: 201 })
  }),

  // ========================
  // MENTORS ENDPOINTS
  // ========================
  http.get('/api/mentors', async ({ request }) => {
    await delay(150)
    const url = new URL(request.url)
    const department = url.searchParams.get('department')
    
    let filteredMentors = mockMentors
    if (department) {
      filteredMentors = mockMentors.filter(m => m.department === department)
    }
    
    return HttpResponse.json(createAPIResponse(filteredMentors))
  }),

  http.get('/api/mentors/:id', async ({ params }) => {
    await delay(120)
    const mentor = mockMentors.find(m => m.id === params.id)
    if (!mentor) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(createAPIResponse(mentor))
  }),

  http.get('/api/mentors/:id/apprentices', async ({ params }) => {
    await delay(180)
    const mentor = mockMentors.find(m => m.id === params.id)
    if (!mentor) {
      return new HttpResponse(null, { status: 404 })
    }
    
    const apprentices = mockApprentices.filter(a => mentor.apprentices.includes(a.id))
    return HttpResponse.json(createAPIResponse(apprentices))
  }),

  // ========================
  // REVIEWS ENDPOINTS
  // ========================
  http.get('/api/reviews', async ({ request }) => {
    await delay(180)
    const url = new URL(request.url)
    const apprenticeId = url.searchParams.get('apprenticeId')
    const mentorId = url.searchParams.get('mentorId')
    const year = url.searchParams.get('year')
    const quarter = url.searchParams.get('quarter')
    
    let filteredReviews = mockQuarterlyReviews
    
    if (apprenticeId) {
      filteredReviews = filteredReviews.filter(r => r.apprenticeId === apprenticeId)
    }
    
    if (mentorId) {
      filteredReviews = filteredReviews.filter(r => r.mentorId === mentorId)
    }
    
    if (year) {
      filteredReviews = filteredReviews.filter(r => r.year === parseInt(year))
    }
    
    if (quarter) {
      filteredReviews = filteredReviews.filter(r => r.quarter === parseInt(quarter))
    }
    
    return HttpResponse.json(createAPIResponse(filteredReviews))
  }),

  http.get('/api/reviews/:id', async ({ params }) => {
    await delay(150)
    const review = mockQuarterlyReviews.find(r => r.id === params.id)
    if (!review) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(createAPIResponse(review))
  }),

  http.post('/api/reviews', async ({ request }) => {
    await delay(300)
    const newReview = await request.json() as any
    const review = {
      id: Date.now().toString(),
      ...newReview,
      status: 'scheduled' as const
    }
    mockQuarterlyReviews.push(review)
    return HttpResponse.json(createAPIResponse(review, 'Review created successfully'), { status: 201 })
  }),

  http.put('/api/reviews/:id', async ({ params, request }) => {
    await delay(250)
    const updates = await request.json() as any
    const index = mockQuarterlyReviews.findIndex(r => r.id === params.id)
    if (index === -1) {
      return new HttpResponse(null, { status: 404 })
    }
    mockQuarterlyReviews[index] = { ...mockQuarterlyReviews[index], ...updates }
    return HttpResponse.json(createAPIResponse(mockQuarterlyReviews[index], 'Review updated successfully'))
  }),

  // ========================
  // EXAMS ENDPOINTS
  // ========================
  http.get('/api/exams', async ({ request }) => {
    await delay(150)
    const url = new URL(request.url)
    const status = url.searchParams.get('status')
    const subject = url.searchParams.get('subject')
    
    let filteredExams = mockExams
    
    if (status) {
      filteredExams = filteredExams.filter(e => e.status === status)
    }
    
    if (subject) {
      filteredExams = filteredExams.filter(e => e.subject === subject)
    }
    
    return HttpResponse.json(createAPIResponse(filteredExams))
  }),

  http.get('/api/exams/:id', async ({ params }) => {
    await delay(120)
    const exam = mockExams.find(e => e.id === params.id)
    if (!exam) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(createAPIResponse(exam))
  }),

  http.get('/api/exams/:id/questions', async ({ params }) => {
    await delay(200)
    const examId = params.id as string
    const questions = mockExamQuestions.filter(q => q.examId === examId)
    return HttpResponse.json(createAPIResponse(questions))
  }),

  http.post('/api/exams', async ({ request }) => {
    await delay(300)
    const newExam = await request.json() as any
    const exam = {
      id: Date.now().toString(),
      ...newExam,
      status: 'upcoming' as const
    }
    mockExams.push(exam)
    return HttpResponse.json(createAPIResponse(exam, 'Exam created successfully'), { status: 201 })
  }),

  http.put('/api/exams/:id', async ({ params, request }) => {
    await delay(250)
    const updates = await request.json() as any
    const index = mockExams.findIndex(e => e.id === params.id)
    if (index === -1) {
      return new HttpResponse(null, { status: 404 })
    }
    mockExams[index] = { ...mockExams[index], ...updates }
    return HttpResponse.json(createAPIResponse(mockExams[index], 'Exam updated successfully'))
  }),

  http.delete('/api/exams/:id', async ({ params }) => {
    await delay(200)
    const index = mockExams.findIndex(e => e.id === params.id)
    if (index === -1) {
      return new HttpResponse(null, { status: 404 })
    }
    mockExams.splice(index, 1)
    return HttpResponse.json(createAPIResponse(null, 'Exam deleted successfully'), { status: 204 })
  }),

  // ========================
  // EXAM RESULTS ENDPOINTS
  // ========================
  http.get('/api/exam-results', async ({ request }) => {
    await delay(180)
    const url = new URL(request.url)
    const apprenticeId = url.searchParams.get('apprenticeId')
    const examId = url.searchParams.get('examId')
    
    let filteredResults = mockExamResults
    
    if (apprenticeId) {
      filteredResults = filteredResults.filter(r => r.apprenticeId === apprenticeId)
    }
    
    if (examId) {
      filteredResults = filteredResults.filter(r => r.examId === examId)
    }
    
    return HttpResponse.json(createAPIResponse(filteredResults))
  }),

  http.post('/api/exam-results', async ({ request }) => {
    await delay(400)
    const newResult = await request.json() as any
    const result = {
      id: Date.now().toString(),
      ...newResult,
      completedAt: new Date().toISOString()
    }
    mockExamResults.push(result)
    return HttpResponse.json(createAPIResponse(result, 'Exam result recorded successfully'), { status: 201 })
  }),

  // ========================
  // EXAM SUBMISSIONS ENDPOINTS
  // ========================
  http.get('/api/exam-submissions', async ({ request }) => {
    await delay(150)
    const url = new URL(request.url)
    const examId = url.searchParams.get('examId')
    const apprenticeId = url.searchParams.get('apprenticeId')
    
    let filteredSubmissions = mockExamSubmissions
    
    if (examId) {
      filteredSubmissions = filteredSubmissions.filter(s => s.examId === examId)
    }
    
    if (apprenticeId) {
      filteredSubmissions = filteredSubmissions.filter(s => s.apprenticeId === apprenticeId)
    }
    
    return HttpResponse.json(createAPIResponse(filteredSubmissions))
  }),

  http.post('/api/exam-submissions', async ({ request }) => {
    await delay(200)
    const newSubmission = await request.json() as any
    const submission = {
      id: Date.now().toString(),
      ...newSubmission,
      submittedAt: new Date().toISOString()
    }
    mockExamSubmissions.push(submission)
    return HttpResponse.json(createAPIResponse(submission, 'Submission recorded successfully'), { status: 201 })
  }),

  // ========================
  // PROGRESS ENDPOINTS
  // ========================
  http.get('/api/modules', async () => {
    await delay(120)
    return HttpResponse.json(createAPIResponse(mockProgressModules))
  }),

  http.get('/api/progress', async ({ request }) => {
    await delay(150)
    const url = new URL(request.url)
    const apprenticeId = url.searchParams.get('apprenticeId')
    const moduleId = url.searchParams.get('moduleId')
    
    let filteredProgress = mockApprenticeProgress
    
    if (apprenticeId) {
      filteredProgress = filteredProgress.filter(p => p.apprenticeId === apprenticeId)
    }
    
    if (moduleId) {
      filteredProgress = filteredProgress.filter(p => p.moduleId === moduleId)
    }
    
    return HttpResponse.json(createAPIResponse(filteredProgress))
  }),

  http.put('/api/progress/:id', async ({ params, request }) => {
    await delay(200)
    const updates = await request.json() as any
    const index = mockApprenticeProgress.findIndex(p => p.id === params.id)
    if (index === -1) {
      return new HttpResponse(null, { status: 404 })
    }
    mockApprenticeProgress[index] = { ...mockApprenticeProgress[index], ...updates }
    return HttpResponse.json(createAPIResponse(mockApprenticeProgress[index], 'Progress updated successfully'))
  }),

  // ========================
  // NOTIFICATIONS ENDPOINTS
  // ========================
  http.get('/api/notifications', async ({ request }) => {
    await delay(120)
    const url = new URL(request.url)
    const userId = url.searchParams.get('userId')
    const unreadOnly = url.searchParams.get('unreadOnly') === 'true'
    
    let filteredNotifications = mockNotifications
    
    if (userId) {
      filteredNotifications = filteredNotifications.filter(n => n.userId === userId)
    }
    
    if (unreadOnly) {
      filteredNotifications = filteredNotifications.filter(n => !n.isRead)
    }
    
    return HttpResponse.json(createAPIResponse(filteredNotifications))
  }),

  http.put('/api/notifications/:id/read', async ({ params }) => {
    await delay(100)
    const notification = mockNotifications.find(n => n.id === params.id)
    if (!notification) {
      return new HttpResponse(null, { status: 404 })
    }
    notification.isRead = true
    return HttpResponse.json(createAPIResponse(notification, 'Notification marked as read'))
  }),

  http.put('/api/notifications/mark-all-read', async ({ request }) => {
    await delay(150)
    const { userId } = await request.json() as { userId: string }
    const userNotifications = mockNotifications.filter(n => n.userId === userId)
    userNotifications.forEach(n => n.isRead = true)
    return HttpResponse.json(createAPIResponse(null, 'All notifications marked as read'))
  }),

  // ========================
  // CALENDAR ENDPOINTS
  // ========================
  http.get('/api/calendar/events', async ({ request }) => {
    await delay(180)
    const url = new URL(request.url)
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    const apprenticeId = url.searchParams.get('apprenticeId')
    const mentorId = url.searchParams.get('mentorId')
    const type = url.searchParams.get('type')
    
    let filteredEvents = mockCalendarEvents
    
    if (start) {
      filteredEvents = filteredEvents.filter(e => new Date(e.startDate) >= new Date(start))
    }
    
    if (end) {
      filteredEvents = filteredEvents.filter(e => new Date(e.startDate) <= new Date(end))
    }
    
    if (apprenticeId) {
      filteredEvents = filteredEvents.filter(e => e.apprenticeId === apprenticeId)
    }
    
    if (mentorId) {
      filteredEvents = filteredEvents.filter(e => e.mentorId === mentorId)
    }
    
    if (type) {
      filteredEvents = filteredEvents.filter(e => e.type === type)
    }
    
    return HttpResponse.json(createAPIResponse(filteredEvents))
  }),

  http.post('/api/calendar/events', async ({ request }) => {
    await delay(250)
    const newEvent = await request.json() as any
    const event = {
      id: Date.now().toString(),
      ...newEvent,
      status: 'scheduled' as const,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    mockCalendarEvents.push(event)
    return HttpResponse.json(createAPIResponse(event, 'Calendar event created successfully'), { status: 201 })
  }),

  http.put('/api/calendar/events/:id', async ({ params, request }) => {
    await delay(200)
    const updates = await request.json() as any
    const index = mockCalendarEvents.findIndex(e => e.id === params.id)
    if (index === -1) {
      return new HttpResponse(null, { status: 404 })
    }
    mockCalendarEvents[index] = { 
      ...mockCalendarEvents[index], 
      ...updates, 
      updatedAt: new Date().toISOString() 
    }
    return HttpResponse.json(createAPIResponse(mockCalendarEvents[index], 'Calendar event updated successfully'))
  }),

  http.delete('/api/calendar/events/:id', async ({ params }) => {
    await delay(150)
    const index = mockCalendarEvents.findIndex(e => e.id === params.id)
    if (index === -1) {
      return new HttpResponse(null, { status: 404 })
    }
    mockCalendarEvents.splice(index, 1)
    return HttpResponse.json(createAPIResponse(null, 'Calendar event deleted successfully'), { status: 204 })
  }),

  // ========================
  // FILE UPLOAD ENDPOINTS
  // ========================
  http.post('/api/files/upload', async ({ request }) => {
    await delay(800) // Simulate file upload time
    const formData = await request.formData()
    const file = formData.get('file') as File
    const category = formData.get('category') as string
    const relatedType = formData.get('relatedType') as string
    const relatedId = formData.get('relatedId') as string
    
    if (!file) {
      return new HttpResponse(JSON.stringify({ error: 'No file provided' }), { status: 400 })
    }
    
    const fileUpload = {
      id: Date.now().toString(),
      filename: `${Date.now()}-${file.name}`,
      originalName: file.name,
      mimeType: file.type,
      size: file.size,
      uploadedBy: 'current-user',
      uploadedAt: new Date().toISOString(),
      category: category as any,
      relatedTo: relatedType && relatedId ? {
        type: relatedType as any,
        id: relatedId
      } : undefined,
      url: `/uploads/${category}/${Date.now()}-${file.name}`,
      isPublic: category === 'image'
    }
    
    mockFileUploads.push(fileUpload)
    return HttpResponse.json(createAPIResponse(fileUpload, 'File uploaded successfully'), { status: 201 })
  }),

  http.get('/api/files', async ({ request }) => {
    await delay(120)
    const url = new URL(request.url)
    const category = url.searchParams.get('category')
    const relatedType = url.searchParams.get('relatedType')
    const relatedId = url.searchParams.get('relatedId')
    
    let filteredFiles = mockFileUploads
    
    if (category) {
      filteredFiles = filteredFiles.filter(f => f.category === category)
    }
    
    if (relatedType && relatedId) {
      filteredFiles = filteredFiles.filter(f => 
        f.relatedTo?.type === relatedType && f.relatedTo?.id === relatedId
      )
    }
    
    return HttpResponse.json(createAPIResponse(filteredFiles))
  }),

  http.delete('/api/files/:id', async ({ params }) => {
    await delay(200)
    const index = mockFileUploads.findIndex(f => f.id === params.id)
    if (index === -1) {
      return new HttpResponse(null, { status: 404 })
    }
    mockFileUploads.splice(index, 1)
    return HttpResponse.json(createAPIResponse(null, 'File deleted successfully'), { status: 204 })
  }),

  // ========================
  // BULK OPERATIONS ENDPOINTS
  // ========================
  http.get('/api/bulk-operations', async ({ request }) => {
    await delay(150)
    const url = new URL(request.url)
    const resourceType = url.searchParams.get('resourceType')
    const status = url.searchParams.get('status')
    
    let filteredOperations = mockBulkOperations
    
    if (resourceType) {
      filteredOperations = filteredOperations.filter(op => op.resourceType === resourceType)
    }
    
    if (status) {
      filteredOperations = filteredOperations.filter(op => op.status === status)
    }
    
    return HttpResponse.json(createAPIResponse(filteredOperations))
  }),

  http.get('/api/bulk-operations/:id', async ({ params }) => {
    await delay(120)
    const operation = mockBulkOperations.find(op => op.id === params.id)
    if (!operation) {
      return new HttpResponse(null, { status: 404 })
    }
    return HttpResponse.json(createAPIResponse(operation))
  }),

  // ========================
  // AUDIT LOG ENDPOINTS
  // ========================
  http.get('/api/audit-logs', async ({ request }) => {
    await delay(200)
    const url = new URL(request.url)
    const userId = url.searchParams.get('userId')
    const action = url.searchParams.get('action')
    const resourceType = url.searchParams.get('resourceType')
    const page = parseInt(url.searchParams.get('page') || '1')
    const pageSize = parseInt(url.searchParams.get('pageSize') || '50')
    
    let filteredLogs = mockAuditLogs
    
    if (userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === userId)
    }
    
    if (action) {
      filteredLogs = filteredLogs.filter(log => log.action === action)
    }
    
    if (resourceType) {
      filteredLogs = filteredLogs.filter(log => log.resourceType === resourceType)
    }
    
    // Sort by timestamp descending
    filteredLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    
    const paginatedResult = applyPagination(filteredLogs, page, pageSize)
    
    return HttpResponse.json(createAPIResponse(
      paginatedResult.data,
      `Found ${paginatedResult.pagination.totalItems} audit logs`,
      paginatedResult.pagination
    ))
  }),

  // ========================
  // ANALYTICS ENDPOINTS
  // ========================
  http.get('/api/analytics/apprentice-trends', async ({ request }) => {
    await delay(400)
    const url = new URL(request.url)
    const period = url.searchParams.get('period') || '6months'
    
    // Filter data based on period
    let trends = mockAnalyticsData.apprenticeProgressTrends
    if (period === '3months') {
      trends = trends.slice(-3)
    } else if (period === '1year') {
      // Return all data for 1 year
    }
    
    return HttpResponse.json(createAPIResponse(trends))
  }),

  http.get('/api/analytics/department-performance', async () => {
    await delay(350)
    return HttpResponse.json(createAPIResponse(mockAnalyticsData.departmentPerformance))
  }),

  http.get('/api/analytics/exam-performance', async () => {
    await delay(300)
    return HttpResponse.json(createAPIResponse(mockAnalyticsData.examPerformance))
  }),

  http.get('/api/analytics/mentor-effectiveness', async () => {
    await delay(320)
    return HttpResponse.json(createAPIResponse(mockAnalyticsData.mentorEffectiveness))
  }),

  http.get('/api/analytics/monthly-stats', async ({ request }) => {
    await delay(280)
    const url = new URL(request.url)
    const year = url.searchParams.get('year') || '2024'
    
    // Filter by year if needed
    const stats = mockAnalyticsData.monthlyStats.filter(stat => 
      stat.month.includes(year)
    )
    
    return HttpResponse.json(createAPIResponse(stats))
  }),

  // ========================
  // SEARCH ENDPOINTS
  // ========================
  http.get('/api/search/global', async ({ request }) => {
    await delay(250)
    const url = new URL(request.url)
    const query = url.searchParams.get('q') || ''
    const limit = parseInt(url.searchParams.get('limit') || '20')
    
    if (!query) {
      return HttpResponse.json(createAPIResponse({ results: [], total: 0 }))
    }
    
    const results: any[] = []
    
    // Search apprentices
    const apprenticeResults = mockApprentices
      .filter(a => 
        a.firstName.toLowerCase().includes(query.toLowerCase()) ||
        a.lastName.toLowerCase().includes(query.toLowerCase()) ||
        a.email.toLowerCase().includes(query.toLowerCase())
      )
      .slice(0, 5)
      .map(a => ({
        id: a.id,
        type: 'apprentice',
        title: `${a.firstName} ${a.lastName}`,
        subtitle: a.position,
        url: `/apprentices/${a.id}`
      }))
    
    // Search mentors
    const mentorResults = mockMentors
      .filter(m => 
        m.firstName.toLowerCase().includes(query.toLowerCase()) ||
        m.lastName.toLowerCase().includes(query.toLowerCase()) ||
        m.email.toLowerCase().includes(query.toLowerCase())
      )
      .slice(0, 3)
      .map(m => ({
        id: m.id,
        type: 'mentor',
        title: `${m.firstName} ${m.lastName}`,
        subtitle: m.position,
        url: `/mentors/${m.id}`
      }))
    
    // Search exams
    const examResults = mockExams
      .filter(e => 
        e.title.toLowerCase().includes(query.toLowerCase()) ||
        e.subject.toLowerCase().includes(query.toLowerCase())
      )
      .slice(0, 3)
      .map(e => ({
        id: e.id,
        type: 'exam',
        title: e.title,
        subtitle: e.subject,
        url: `/exams/${e.id}`
      }))
    
    results.push(...apprenticeResults, ...mentorResults, ...examResults)
    
    return HttpResponse.json(createAPIResponse({
      results: results.slice(0, limit),
      total: results.length
    }))
  }),

  // ========================
  // REPORTING ENDPOINTS
  // ========================
  http.get('/api/reports/apprentice-progress', async ({ request }) => {
    await delay(600)
    const url = new URL(request.url)
    const format = url.searchParams.get('format') || 'json'
    const departmentId = url.searchParams.get('departmentId')
    
    let apprentices = mockApprentices
    if (departmentId) {
      apprentices = apprentices.filter(a => a.departmentId === departmentId)
    }
    
    const report = {
      generatedAt: new Date().toISOString(),
      totalApprentices: apprentices.length,
      averageProgress: Math.round(
        apprentices.reduce((sum, a) => sum + a.overallProgress, 0) / apprentices.length
      ),
      apprentices: apprentices.map(a => ({
        id: a.id,
        name: `${a.firstName} ${a.lastName}`,
        department: a.department,
        progress: a.overallProgress,
        status: a.status,
        mentor: a.mentor
      }))
    }
    
    if (format === 'csv') {
      return HttpResponse.json(createAPIResponse({
        downloadUrl: '/downloads/apprentice-progress-report.csv',
        report
      }))
    }
    
    return HttpResponse.json(createAPIResponse(report))
  }),

  http.get('/api/reports/department-summary', async ({ request }) => {
    await delay(500)
    const url = new URL(request.url)
    const year = url.searchParams.get('year') || '2024'
    
    const summary = mockDepartments.map(dept => {
      const deptApprentices = mockApprentices.filter(a => a.departmentId === dept.id)
      const deptReviews = mockQuarterlyReviews.filter(r => 
        r.year === parseInt(year) && 
        deptApprentices.some(a => a.id === r.apprenticeId)
      )
      
      return {
        department: dept.name,
        totalApprentices: deptApprentices.length,
        activeApprentices: deptApprentices.filter(a => a.status === 'active').length,
        completedApprentices: deptApprentices.filter(a => a.status === 'completed').length,
        averageProgress: Math.round(
          deptApprentices.reduce((sum, a) => sum + a.overallProgress, 0) / deptApprentices.length
        ),
        totalReviews: deptReviews.length,
        averageReviewScore: deptReviews.length > 0 ? 
          Math.round(
            deptReviews.reduce((sum, r) => sum + r.scores.overallRating, 0) / deptReviews.length * 10
          ) / 10 : 0
      }
    })
    
    return HttpResponse.json(createAPIResponse({
      year: parseInt(year),
      generatedAt: new Date().toISOString(),
      departments: summary
    }))
  })
]