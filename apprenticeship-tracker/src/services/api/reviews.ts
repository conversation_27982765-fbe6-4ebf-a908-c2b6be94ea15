import { apiClient } from './client'

export interface Review {
  id: string
  apprenticeId: string
  reviewerId: string
  date: string
  type: 'monthly' | 'quarterly' | 'annual'
  rating: number
  feedback: string
  goals: string[]
  status: 'draft' | 'submitted' | 'approved'
}

export const reviewsApi = {
  getAll: () => apiClient.get<Review[]>('/reviews'),
  getById: (id: string) => apiClient.get<Review>(`/reviews/${id}`),
  getByApprentice: (apprenticeId: string) => apiClient.get<Review[]>(`/reviews?apprenticeId=${apprenticeId}`),
  create: (data: Omit<Review, 'id'>) => apiClient.post<Review>('/reviews', data),
  update: (id: string, data: Partial<Review>) => apiClient.put<Review>(`/reviews/${id}`, data),
  delete: (id: string) => apiClient.delete<void>(`/reviews/${id}`),
}