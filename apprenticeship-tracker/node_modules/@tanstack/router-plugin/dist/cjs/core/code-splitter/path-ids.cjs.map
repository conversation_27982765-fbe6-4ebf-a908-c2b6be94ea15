{"version": 3, "file": "path-ids.cjs", "sources": ["../../../../src/core/code-splitter/path-ids.ts"], "sourcesContent": ["export function createIdentifier(strings: Array<string>): string {\n  if (strings.length === 0) {\n    throw new Error('Cannot create an identifier from an empty array')\n  }\n\n  const sortedStrings = [...strings].sort()\n  const combinedString = sortedStrings.join('---') // Delimiter\n\n  // Replace unsafe characters\n  let safeString = combinedString.replace(/\\//g, '--slash--')\n  safeString = safeString.replace(/\\\\/g, '--backslash--')\n  safeString = safeString.replace(/\\?/g, '--question--')\n  safeString = safeString.replace(/%/g, '--percent--')\n  safeString = safeString.replace(/#/g, '--hash--')\n  safeString = safeString.replace(/\\+/g, '--plus--')\n  safeString = safeString.replace(/=/g, '--equals--')\n  safeString = safeString.replace(/&/g, '--ampersand--')\n  safeString = safeString.replace(/\\s/g, '_') // Replace spaces with underscores\n\n  return safeString\n}\n\nexport function decodeIdentifier(identifier: string): Array<string> {\n  if (!identifier) {\n    return []\n  }\n\n  let combinedString = identifier.replace(/--slash--/g, '/')\n  combinedString = combinedString.replace(/--backslash--/g, '\\\\')\n  combinedString = combinedString.replace(/--question--/g, '?')\n  combinedString = combinedString.replace(/--percent--/g, '%')\n  combinedString = combinedString.replace(/--hash--/g, '#')\n  combinedString = combinedString.replace(/--plus--/g, '+')\n  combinedString = combinedString.replace(/--equals--/g, '=')\n  combinedString = combinedString.replace(/--ampersand--/g, '&')\n  combinedString = combinedString.replace(/_/g, ' ') // Restore spaces\n\n  return combinedString.split('---')\n}\n"], "names": [], "mappings": ";;AAAO,SAAS,iBAAiB,SAAgC;AAC3D,MAAA,QAAQ,WAAW,GAAG;AAClB,UAAA,IAAI,MAAM,iDAAiD;AAAA,EAAA;AAGnE,QAAM,gBAAgB,CAAC,GAAG,OAAO,EAAE,KAAK;AAClC,QAAA,iBAAiB,cAAc,KAAK,KAAK;AAG/C,MAAI,aAAa,eAAe,QAAQ,OAAO,WAAW;AAC7C,eAAA,WAAW,QAAQ,OAAO,eAAe;AACzC,eAAA,WAAW,QAAQ,OAAO,cAAc;AACxC,eAAA,WAAW,QAAQ,MAAM,aAAa;AACtC,eAAA,WAAW,QAAQ,MAAM,UAAU;AACnC,eAAA,WAAW,QAAQ,OAAO,UAAU;AACpC,eAAA,WAAW,QAAQ,MAAM,YAAY;AACrC,eAAA,WAAW,QAAQ,MAAM,eAAe;AACxC,eAAA,WAAW,QAAQ,OAAO,GAAG;AAEnC,SAAA;AACT;AAEO,SAAS,iBAAiB,YAAmC;AAClE,MAAI,CAAC,YAAY;AACf,WAAO,CAAC;AAAA,EAAA;AAGV,MAAI,iBAAiB,WAAW,QAAQ,cAAc,GAAG;AACxC,mBAAA,eAAe,QAAQ,kBAAkB,IAAI;AAC7C,mBAAA,eAAe,QAAQ,iBAAiB,GAAG;AAC3C,mBAAA,eAAe,QAAQ,gBAAgB,GAAG;AAC1C,mBAAA,eAAe,QAAQ,aAAa,GAAG;AACvC,mBAAA,eAAe,QAAQ,aAAa,GAAG;AACvC,mBAAA,eAAe,QAAQ,eAAe,GAAG;AACzC,mBAAA,eAAe,QAAQ,kBAAkB,GAAG;AAC5C,mBAAA,eAAe,QAAQ,MAAM,GAAG;AAE1C,SAAA,eAAe,MAAM,KAAK;AACnC;;;"}