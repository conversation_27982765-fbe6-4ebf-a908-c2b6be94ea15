import React, { useState } from 'react'
import { Input } from './input'
import { Select } from './select'
import { Button } from './button'
import { Card, CardContent, CardHeader, CardTitle } from './card'
import { Search, Filter, X } from 'lucide-react'

interface FilterConfig {
  key: string
  label: string
  type: 'select' | 'multiselect' | 'date' | 'number'
  options?: Array<{ value: string; label: string }>
  placeholder?: string
}

interface SearchFilterProps {
  onSearch: (query: string) => void
  onFilter: (filters: Record<string, any>) => void
  onClear: () => void
  searchPlaceholder?: string
  filterConfigs?: FilterConfig[]
  className?: string
}

export const SearchFilter: React.FC<SearchFilterProps> = ({
  onSearch,
  onFilter,
  onClear,
  searchPlaceholder = 'Search...',
  filterConfigs = [],
  className = ''
}) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState<Record<string, any>>({})
  const [showFilters, setShowFilters] = useState(false)

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    onSearch(query)
  }

  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    onFilter(newFilters)
  }

  const handleClearAll = () => {
    setSearchQuery('')
    setFilters({})
    onClear()
  }

  const hasActiveFilters = Object.keys(filters).some(key => filters[key])

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search Bar */}
      <div className="flex space-x-2">
        <div className="flex-1">
          <Input
            placeholder={searchPlaceholder}
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            leftIcon={<Search className="w-4 h-4" />}
            rightIcon={
              searchQuery && (
                <button
                  onClick={() => handleSearch('')}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-4 h-4" />
                </button>
              )
            }
          />
        </div>
        
        {filterConfigs.length > 0 && (
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2"
          >
            <Filter className="w-4 h-4" />
            <span>Filters</span>
            {hasActiveFilters && (
              <span className="ml-1 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                {Object.keys(filters).filter(key => filters[key]).length}
              </span>
            )}
          </Button>
        )}
        
        {(searchQuery || hasActiveFilters) && (
          <Button
            variant="ghost"
            onClick={handleClearAll}
            className="text-gray-500 hover:text-gray-700"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Filters Panel */}
      {showFilters && filterConfigs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filterConfigs.map((config) => (
                <div key={config.key}>
                  {config.type === 'select' && (
                    <Select
                      label={config.label}
                      options={config.options || []}
                      value={filters[config.key] || ''}
                      onChange={(value) => handleFilterChange(config.key, value)}
                      placeholder={config.placeholder}
                    />
                  )}
                  
                  {config.type === 'multiselect' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {config.label}
                      </label>
                      <div className="space-y-2">
                        {config.options?.map((option) => (
                          <label key={option.value} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={filters[config.key]?.includes(option.value) || false}
                              onChange={(e) => {
                                const currentValues = filters[config.key] || []
                                const newValues = e.target.checked
                                  ? [...currentValues, option.value]
                                  : currentValues.filter((v: string) => v !== option.value)
                                handleFilterChange(config.key, newValues)
                              }}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="text-sm text-gray-700">{option.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {config.type === 'date' && (
                    <Input
                      type="date"
                      label={config.label}
                      value={filters[config.key] || ''}
                      onChange={(e) => handleFilterChange(config.key, e.target.value)}
                      placeholder={config.placeholder}
                    />
                  )}
                  
                  {config.type === 'number' && (
                    <Input
                      type="number"
                      label={config.label}
                      value={filters[config.key] || ''}
                      onChange={(e) => handleFilterChange(config.key, e.target.value)}
                      placeholder={config.placeholder}
                    />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {Object.entries(filters).map(([key, value]) => {
            if (!value) return null
            
            const config = filterConfigs.find(c => c.key === key)
            if (!config) return null
            
            const displayValue = Array.isArray(value) 
              ? value.join(', ') 
              : config.options?.find(opt => opt.value === value)?.label || value
            
            return (
              <span
                key={key}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
              >
                <span className="font-medium">{config.label}:</span>
                <span className="ml-1">{displayValue}</span>
                <button
                  onClick={() => handleFilterChange(key, Array.isArray(value) ? [] : '')}
                  className="ml-2 text-blue-600 hover:text-blue-800"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            )
          })}
        </div>
      )}
    </div>
  )
}

// Preset filter configurations for different entities
export const apprenticeFilterConfigs: FilterConfig[] = [
  {
    key: 'status',
    label: 'Status',
    type: 'multiselect',
    options: [
      { value: 'active', label: 'Active' },
      { value: 'completed', label: 'Completed' },
      { value: 'suspended', label: 'Suspended' },
      { value: 'withdrawn', label: 'Withdrawn' }
    ]
  },
  {
    key: 'department',
    label: 'Department',
    type: 'multiselect',
    options: [
      { value: 'Technology', label: 'Technology' },
      { value: 'Marketing', label: 'Marketing' },
      { value: 'Sales', label: 'Sales' },
      { value: 'Human Resources', label: 'Human Resources' },
      { value: 'Finance', label: 'Finance' },
      { value: 'Operations', label: 'Operations' }
    ]
  },
  {
    key: 'mentor',
    label: 'Mentor',
    type: 'select',
    options: [
      { value: 'John Smith', label: 'John Smith' },
      { value: 'Sarah Johnson', label: 'Sarah Johnson' },
      { value: 'Mike Brown', label: 'Mike Brown' },
      { value: 'Emily Davis', label: 'Emily Davis' },
      { value: 'David Wilson', label: 'David Wilson' }
    ]
  },
  {
    key: 'startDate',
    label: 'Start Date From',
    type: 'date'
  }
]

export const reviewFilterConfigs: FilterConfig[] = [
  {
    key: 'status',
    label: 'Status',
    type: 'multiselect',
    options: [
      { value: 'scheduled', label: 'Scheduled' },
      { value: 'in-progress', label: 'In Progress' },
      { value: 'completed', label: 'Completed' },
      { value: 'overdue', label: 'Overdue' }
    ]
  },
  {
    key: 'quarter',
    label: 'Quarter',
    type: 'multiselect',
    options: [
      { value: '1', label: 'Q1' },
      { value: '2', label: 'Q2' },
      { value: '3', label: 'Q3' },
      { value: '4', label: 'Q4' }
    ]
  },
  {
    key: 'year',
    label: 'Year',
    type: 'select',
    options: [
      { value: '2024', label: '2024' },
      { value: '2023', label: '2023' },
      { value: '2022', label: '2022' }
    ]
  },
  {
    key: 'mentor',
    label: 'Mentor',
    type: 'select',
    options: [
      { value: 'John Smith', label: 'John Smith' },
      { value: 'Sarah Johnson', label: 'Sarah Johnson' },
      { value: 'Mike Brown', label: 'Mike Brown' },
      { value: 'Emily Davis', label: 'Emily Davis' },
      { value: 'David Wilson', label: 'David Wilson' }
    ]
  }
]

export const examFilterConfigs: FilterConfig[] = [
  {
    key: 'status',
    label: 'Status',
    type: 'multiselect',
    options: [
      { value: 'upcoming', label: 'Upcoming' },
      { value: 'active', label: 'Active' },
      { value: 'completed', label: 'Completed' },
      { value: 'cancelled', label: 'Cancelled' }
    ]
  },
  {
    key: 'type',
    label: 'Exam Type',
    type: 'multiselect',
    options: [
      { value: 'written', label: 'Written' },
      { value: 'practical', label: 'Practical' },
      { value: 'oral', label: 'Oral' },
      { value: 'project', label: 'Project' }
    ]
  },
  {
    key: 'subject',
    label: 'Subject',
    type: 'select',
    options: [
      { value: 'JavaScript', label: 'JavaScript' },
      { value: 'React', label: 'React' },
      { value: 'Node.js', label: 'Node.js' },
      { value: 'Python', label: 'Python' },
      { value: 'Database', label: 'Database' },
      { value: 'System Design', label: 'System Design' },
      { value: 'DevOps', label: 'DevOps' },
      { value: 'Testing', label: 'Testing' },
      { value: 'Security', label: 'Security' },
      { value: 'General', label: 'General Knowledge' }
    ]
  },
  {
    key: 'scheduledDate',
    label: 'Scheduled Date From',
    type: 'date'
  }
]