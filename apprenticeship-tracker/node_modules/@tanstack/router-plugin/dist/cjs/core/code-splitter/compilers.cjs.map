{"version": 3, "file": "compilers.cjs", "sources": ["../../../../src/core/code-splitter/compilers.ts"], "sourcesContent": ["import * as t from '@babel/types'\nimport babel from '@babel/core'\nimport * as template from '@babel/template'\nimport {\n  deadCodeElimination,\n  findReferencedIdentifiers,\n} from 'babel-dead-code-elimination'\nimport { generateFromAst, parseAst } from '@tanstack/router-utils'\nimport { tsrSplit } from '../constants'\nimport { routeHmrStatement } from '../route-hmr-statement'\nimport { createIdentifier } from './path-ids'\nimport { getFrameworkOptions } from './framework-options'\nimport type { GeneratorResult, ParseAstOptions } from '@tanstack/router-utils'\nimport type { CodeSplitGroupings, SplitRouteIdentNodes } from '../constants'\nimport type { Config, DeletableNodes } from '../config'\n\ntype SplitNodeMeta = {\n  routeIdent: SplitRouteIdentNodes\n  splitStrategy: 'lazyFn' | 'lazyRouteComponent'\n  localImporterIdent: string\n  exporterIdent: string\n  localExporterIdent: string\n}\nconst SPLIT_NODES_CONFIG = new Map<SplitRouteIdentNodes, SplitNodeMeta>([\n  [\n    'loader',\n    {\n      routeIdent: 'loader',\n      localImporterIdent: '$$splitLoaderImporter', // const $$splitLoaderImporter = () => import('...')\n      splitStrategy: 'lazyFn',\n      localExporterIdent: 'SplitLoader', // const SplitLoader = ...\n      exporterIdent: 'loader', // export { SplitLoader as loader }\n    },\n  ],\n  [\n    'component',\n    {\n      routeIdent: 'component',\n      localImporterIdent: '$$splitComponentImporter', // const $$splitComponentImporter = () => import('...')\n      splitStrategy: 'lazyRouteComponent',\n      localExporterIdent: 'SplitComponent', // const SplitComponent = ...\n      exporterIdent: 'component', // export { SplitComponent as component }\n    },\n  ],\n  [\n    'pendingComponent',\n    {\n      routeIdent: 'pendingComponent',\n      localImporterIdent: '$$splitPendingComponentImporter', // const $$splitPendingComponentImporter = () => import('...')\n      splitStrategy: 'lazyRouteComponent',\n      localExporterIdent: 'SplitPendingComponent', // const SplitPendingComponent = ...\n      exporterIdent: 'pendingComponent', // export { SplitPendingComponent as pendingComponent }\n    },\n  ],\n  [\n    'errorComponent',\n    {\n      routeIdent: 'errorComponent',\n      localImporterIdent: '$$splitErrorComponentImporter', // const $$splitErrorComponentImporter = () => import('...')\n      splitStrategy: 'lazyRouteComponent',\n      localExporterIdent: 'SplitErrorComponent', // const SplitErrorComponent = ...\n      exporterIdent: 'errorComponent', // export { SplitErrorComponent as errorComponent }\n    },\n  ],\n  [\n    'notFoundComponent',\n    {\n      routeIdent: 'notFoundComponent',\n      localImporterIdent: '$$splitNotFoundComponentImporter', // const $$splitNotFoundComponentImporter = () => import('...')\n      splitStrategy: 'lazyRouteComponent',\n      localExporterIdent: 'SplitNotFoundComponent', // const SplitNotFoundComponent = ...\n      exporterIdent: 'notFoundComponent', // export { SplitNotFoundComponent as notFoundComponent }\n    },\n  ],\n])\nconst KNOWN_SPLIT_ROUTE_IDENTS = [...SPLIT_NODES_CONFIG.keys()] as const\n\nfunction addSplitSearchParamToFilename(\n  filename: string,\n  grouping: Array<string>,\n) {\n  const [bareFilename] = filename.split('?')\n\n  const params = new URLSearchParams()\n  params.append(tsrSplit, createIdentifier(grouping))\n\n  return `${bareFilename}?${params.toString()}`\n}\n\nfunction removeSplitSearchParamFromFilename(filename: string) {\n  const [bareFilename] = filename.split('?')\n  return bareFilename!\n}\n\nconst splittableCreateRouteFns = ['createFileRoute']\nconst unsplittableCreateRouteFns = [\n  'createRootRoute',\n  'createRootRouteWithContext',\n]\nconst allCreateRouteFns = [\n  ...splittableCreateRouteFns,\n  ...unsplittableCreateRouteFns,\n]\n\nexport function compileCodeSplitReferenceRoute(\n  opts: ParseAstOptions & {\n    codeSplitGroupings: CodeSplitGroupings\n    deleteNodes?: Set<DeletableNodes>\n    targetFramework: Config['target']\n    filename: string\n    id: string\n    addHmr?: boolean\n  },\n): GeneratorResult {\n  const ast = parseAst(opts)\n\n  const refIdents = findReferencedIdentifiers(ast)\n\n  function findIndexForSplitNode(str: string) {\n    return opts.codeSplitGroupings.findIndex((group) =>\n      group.includes(str as any),\n    )\n  }\n\n  const frameworkOptions = getFrameworkOptions(opts.targetFramework)\n  const PACKAGE = frameworkOptions.package\n  const LAZY_ROUTE_COMPONENT_IDENT = frameworkOptions.idents.lazyRouteComponent\n  const LAZY_FN_IDENT = frameworkOptions.idents.lazyFn\n\n  let createRouteFn: string\n\n  babel.traverse(ast, {\n    Program: {\n      enter(programPath) {\n        /**\n         * If the component for the route is being imported from\n         * another file, this is to track the path to that file\n         * the path itself doesn't matter, we just need to keep\n         * track of it so that we can remove it from the imports\n         * list if it's not being used like:\n         *\n         * `import '../shared/imported'`\n         */\n        const removableImportPaths = new Set<string>([])\n\n        programPath.traverse({\n          CallExpression: (path) => {\n            if (!t.isIdentifier(path.node.callee)) {\n              return\n            }\n\n            if (!allCreateRouteFns.includes(path.node.callee.name)) {\n              return\n            }\n\n            createRouteFn = path.node.callee.name\n\n            function babelHandleReference(routeOptions: t.Node | undefined) {\n              const hasImportedOrDefinedIdentifier = (name: string) => {\n                return programPath.scope.hasBinding(name)\n              }\n\n              if (t.isObjectExpression(routeOptions)) {\n                if (opts.deleteNodes && opts.deleteNodes.size > 0) {\n                  routeOptions.properties = routeOptions.properties.filter(\n                    (prop) => {\n                      if (t.isObjectProperty(prop)) {\n                        if (t.isIdentifier(prop.key)) {\n                          if (opts.deleteNodes?.has(prop.key.name as any)) {\n                            return false\n                          }\n                        }\n                      }\n                      return true\n                    },\n                  )\n                }\n                if (!splittableCreateRouteFns.includes(createRouteFn)) {\n                  // we can't split this route but we still add HMR handling if enabled\n                  if (opts.addHmr) {\n                    programPath.pushContainer('body', routeHmrStatement)\n                  }\n                  // exit traversal so this route is not split\n                  return programPath.stop()\n                }\n                routeOptions.properties.forEach((prop) => {\n                  if (t.isObjectProperty(prop)) {\n                    if (t.isIdentifier(prop.key)) {\n                      const key = prop.key.name\n\n                      // If the user has not specified a split grouping for this key\n                      // then we should not split it\n                      const codeSplitGroupingByKey = findIndexForSplitNode(key)\n                      if (codeSplitGroupingByKey === -1) {\n                        return\n                      }\n                      const codeSplitGroup = [\n                        ...new Set(\n                          opts.codeSplitGroupings[codeSplitGroupingByKey],\n                        ),\n                      ]\n\n                      // find key in nodeSplitConfig\n                      const isNodeConfigAvailable = SPLIT_NODES_CONFIG.has(\n                        key as any,\n                      )\n\n                      if (!isNodeConfigAvailable) {\n                        return\n                      }\n\n                      // Exit early if the value is undefined\n                      // Since we don't need to run an import just to get the value of `undefined`\n                      // This is useful for cases like: `createFileRoute('/')({ component: undefined })`\n                      if (\n                        t.isIdentifier(prop.value) &&\n                        prop.value.name === 'undefined'\n                      ) {\n                        return\n                      }\n\n                      const splitNodeMeta = SPLIT_NODES_CONFIG.get(key as any)!\n\n                      // We need to extract the existing search params from the filename, if any\n                      // and add the relevant codesplitPrefix to them, then write them back to the filename\n                      const splitUrl = addSplitSearchParamToFilename(\n                        opts.filename,\n                        codeSplitGroup,\n                      )\n\n                      if (\n                        splitNodeMeta.splitStrategy === 'lazyRouteComponent'\n                      ) {\n                        const value = prop.value\n\n                        let shouldSplit = true\n\n                        if (t.isIdentifier(value)) {\n                          const existingImportPath =\n                            getImportSpecifierAndPathFromLocalName(\n                              programPath,\n                              value.name,\n                            ).path\n                          if (existingImportPath) {\n                            removableImportPaths.add(existingImportPath)\n                          }\n\n                          // exported identifiers should not be split\n                          // since they are already being imported\n                          // and need to be retained in the compiled file\n                          const isExported = hasExport(ast, value)\n                          shouldSplit = !isExported\n\n                          if (shouldSplit) {\n                            removeIdentifierLiteral(path, value)\n                          }\n                        }\n\n                        if (!shouldSplit) {\n                          return\n                        }\n\n                        // Prepend the import statement to the program along with the importer function\n                        // Check to see if lazyRouteComponent is already imported before attempting\n                        // to import it again\n                        if (\n                          !hasImportedOrDefinedIdentifier(\n                            LAZY_ROUTE_COMPONENT_IDENT,\n                          )\n                        ) {\n                          programPath.unshiftContainer('body', [\n                            template.statement(\n                              `import { ${LAZY_ROUTE_COMPONENT_IDENT} } from '${PACKAGE}'`,\n                            )(),\n                          ])\n                        }\n\n                        // Check to see if the importer function is already defined\n                        // If not, define it with the dynamic import statement\n                        if (\n                          !hasImportedOrDefinedIdentifier(\n                            splitNodeMeta.localImporterIdent,\n                          )\n                        ) {\n                          programPath.unshiftContainer('body', [\n                            template.statement(\n                              `const ${splitNodeMeta.localImporterIdent} = () => import('${splitUrl}')`,\n                            )(),\n                          ])\n                        }\n\n                        prop.value = template.expression(\n                          `${LAZY_ROUTE_COMPONENT_IDENT}(${splitNodeMeta.localImporterIdent}, '${splitNodeMeta.exporterIdent}')`,\n                        )()\n\n                        // add HMR handling\n                        if (opts.addHmr) {\n                          programPath.pushContainer('body', routeHmrStatement)\n                        }\n                      }\n\n                      if (splitNodeMeta.splitStrategy === 'lazyFn') {\n                        const value = prop.value\n\n                        let shouldSplit = true\n\n                        if (t.isIdentifier(value)) {\n                          const existingImportPath =\n                            getImportSpecifierAndPathFromLocalName(\n                              programPath,\n                              value.name,\n                            ).path\n                          if (existingImportPath) {\n                            removableImportPaths.add(existingImportPath)\n                          }\n\n                          // exported identifiers should not be split\n                          // since they are already being imported\n                          // and need to be retained in the compiled file\n                          const isExported = hasExport(ast, value)\n                          shouldSplit = !isExported\n\n                          if (shouldSplit) {\n                            removeIdentifierLiteral(path, value)\n                          }\n                        }\n\n                        if (!shouldSplit) {\n                          return\n                        }\n\n                        // Prepend the import statement to the program along with the importer function\n                        if (!hasImportedOrDefinedIdentifier(LAZY_FN_IDENT)) {\n                          programPath.unshiftContainer(\n                            'body',\n                            template.smart(\n                              `import { ${LAZY_FN_IDENT} } from '${PACKAGE}'`,\n                            )(),\n                          )\n                        }\n\n                        // Check to see if the importer function is already defined\n                        // If not, define it with the dynamic import statement\n                        if (\n                          !hasImportedOrDefinedIdentifier(\n                            splitNodeMeta.localImporterIdent,\n                          )\n                        ) {\n                          programPath.unshiftContainer('body', [\n                            template.statement(\n                              `const ${splitNodeMeta.localImporterIdent} = () => import('${splitUrl}')`,\n                            )(),\n                          ])\n                        }\n\n                        // Add the lazyFn call with the dynamic import to the prop value\n                        prop.value = template.expression(\n                          `${LAZY_FN_IDENT}(${splitNodeMeta.localImporterIdent}, '${splitNodeMeta.exporterIdent}')`,\n                        )()\n                      }\n                    }\n                  }\n\n                  programPath.scope.crawl()\n                })\n              }\n            }\n\n            if (t.isCallExpression(path.parentPath.node)) {\n              // createFileRoute('/')({ ... })\n              const options = resolveIdentifier(\n                path,\n                path.parentPath.node.arguments[0],\n              )\n\n              babelHandleReference(options)\n            } else if (t.isVariableDeclarator(path.parentPath.node)) {\n              // createFileRoute({ ... })\n              const caller = resolveIdentifier(path, path.parentPath.node.init)\n\n              if (t.isCallExpression(caller)) {\n                const options = resolveIdentifier(path, caller.arguments[0])\n                babelHandleReference(options)\n              }\n            }\n          },\n        })\n\n        /**\n         * If the component for the route is being imported,\n         * and it's not being used, remove the import statement\n         * from the program, by checking that the import has no\n         * specifiers\n         */\n        if (removableImportPaths.size > 0) {\n          programPath.traverse({\n            ImportDeclaration(path) {\n              if (path.node.specifiers.length > 0) return\n              if (removableImportPaths.has(path.node.source.value)) {\n                path.remove()\n              }\n            },\n          })\n        }\n      },\n    },\n  })\n\n  deadCodeElimination(ast, refIdents)\n\n  return generateFromAst(ast, {\n    sourceMaps: true,\n    sourceFileName: opts.filename,\n    filename: opts.filename,\n  })\n}\n\nexport function compileCodeSplitVirtualRoute(\n  opts: ParseAstOptions & {\n    splitTargets: Array<SplitRouteIdentNodes>\n    filename: string\n  },\n): GeneratorResult {\n  const ast = parseAst(opts)\n  const refIdents = findReferencedIdentifiers(ast)\n\n  const intendedSplitNodes = new Set(opts.splitTargets)\n\n  const knownExportedIdents = new Set<string>()\n\n  babel.traverse(ast, {\n    Program: {\n      enter(programPath) {\n        const trackedNodesToSplitByType: Record<\n          SplitRouteIdentNodes,\n          { node: t.Node | undefined; meta: SplitNodeMeta } | undefined\n        > = {\n          component: undefined,\n          loader: undefined,\n          pendingComponent: undefined,\n          errorComponent: undefined,\n          notFoundComponent: undefined,\n        }\n\n        // Find and track all the known split-able nodes\n        programPath.traverse({\n          CallExpression: (path) => {\n            if (!t.isIdentifier(path.node.callee)) {\n              return\n            }\n\n            if (!splittableCreateRouteFns.includes(path.node.callee.name)) {\n              return\n            }\n\n            function babelHandleVirtual(options: t.Node | undefined) {\n              if (t.isObjectExpression(options)) {\n                options.properties.forEach((prop) => {\n                  if (t.isObjectProperty(prop)) {\n                    // do not use `intendedSplitNodes` here\n                    // since we have special considerations that need\n                    // to be accounted for like (not splitting exported identifiers)\n                    KNOWN_SPLIT_ROUTE_IDENTS.forEach((splitType) => {\n                      if (\n                        !t.isIdentifier(prop.key) ||\n                        prop.key.name !== splitType\n                      ) {\n                        return\n                      }\n\n                      const value = prop.value\n\n                      // If the value for the `key` is `undefined`, then we don't need to include it\n                      // in the split file, so we can just return, since it will kept in-place in the\n                      // reference file\n                      // This is useful for cases like: `createFileRoute('/')({ component: undefined })`\n                      if (t.isIdentifier(value) && value.name === 'undefined') {\n                        return\n                      }\n\n                      let isExported = false\n                      if (t.isIdentifier(value)) {\n                        isExported = hasExport(ast, value)\n                        if (isExported) {\n                          knownExportedIdents.add(value.name)\n                        }\n                      }\n\n                      // If the node is exported, we need to remove\n                      // the export from the split file\n                      if (isExported && t.isIdentifier(value)) {\n                        removeExports(ast, value)\n                      } else {\n                        const meta = SPLIT_NODES_CONFIG.get(splitType)!\n                        trackedNodesToSplitByType[splitType] = {\n                          node: prop.value,\n                          meta,\n                        }\n                      }\n                    })\n                  }\n                })\n\n                // Remove all of the options\n                options.properties = []\n              }\n            }\n\n            if (t.isCallExpression(path.parentPath.node)) {\n              // createFileRoute('/')({ ... })\n              const options = resolveIdentifier(\n                path,\n                path.parentPath.node.arguments[0],\n              )\n\n              babelHandleVirtual(options)\n            } else if (t.isVariableDeclarator(path.parentPath.node)) {\n              // createFileRoute({ ... })\n              const caller = resolveIdentifier(path, path.parentPath.node.init)\n\n              if (t.isCallExpression(caller)) {\n                const options = resolveIdentifier(path, caller.arguments[0])\n                babelHandleVirtual(options)\n              }\n            }\n          },\n        })\n\n        // Start the transformation to only exported the intended split nodes\n        intendedSplitNodes.forEach((SPLIT_TYPE) => {\n          const splitKey = trackedNodesToSplitByType[SPLIT_TYPE]\n\n          if (!splitKey) {\n            return\n          }\n\n          let splitNode = splitKey.node\n          const splitMeta = splitKey.meta\n\n          while (t.isIdentifier(splitNode)) {\n            const binding = programPath.scope.getBinding(splitNode.name)\n            splitNode = binding?.path.node\n          }\n\n          // Add the node to the program\n          if (splitNode) {\n            if (t.isFunctionDeclaration(splitNode)) {\n              programPath.pushContainer(\n                'body',\n                t.variableDeclaration('const', [\n                  t.variableDeclarator(\n                    t.identifier(splitMeta.localExporterIdent),\n                    t.functionExpression(\n                      splitNode.id || null, // Anonymize the function expression\n                      splitNode.params,\n                      splitNode.body,\n                      splitNode.generator,\n                      splitNode.async,\n                    ),\n                  ),\n                ]),\n              )\n            } else if (\n              t.isFunctionExpression(splitNode) ||\n              t.isArrowFunctionExpression(splitNode)\n            ) {\n              programPath.pushContainer(\n                'body',\n                t.variableDeclaration('const', [\n                  t.variableDeclarator(\n                    t.identifier(splitMeta.localExporterIdent),\n                    splitNode as any,\n                  ),\n                ]),\n              )\n            } else if (\n              t.isImportSpecifier(splitNode) ||\n              t.isImportDefaultSpecifier(splitNode)\n            ) {\n              programPath.pushContainer(\n                'body',\n                t.variableDeclaration('const', [\n                  t.variableDeclarator(\n                    t.identifier(splitMeta.localExporterIdent),\n                    splitNode.local,\n                  ),\n                ]),\n              )\n            } else if (t.isVariableDeclarator(splitNode)) {\n              programPath.pushContainer(\n                'body',\n                t.variableDeclaration('const', [\n                  t.variableDeclarator(\n                    t.identifier(splitMeta.localExporterIdent),\n                    splitNode.init,\n                  ),\n                ]),\n              )\n            } else if (t.isCallExpression(splitNode)) {\n              const outputSplitNodeCode = generateFromAst(splitNode).code\n              const splitNodeAst = babel.parse(outputSplitNodeCode)\n\n              if (!splitNodeAst) {\n                throw new Error(\n                  `Failed to parse the generated code for \"${SPLIT_TYPE}\" in the node type \"${splitNode.type}\"`,\n                )\n              }\n\n              const statement = splitNodeAst.program.body[0]\n\n              if (!statement) {\n                throw new Error(\n                  `Failed to parse the generated code for \"${SPLIT_TYPE}\" in the node type \"${splitNode.type}\" as no statement was found in the program body`,\n                )\n              }\n\n              if (t.isExpressionStatement(statement)) {\n                const expression = statement.expression\n                programPath.pushContainer(\n                  'body',\n                  t.variableDeclaration('const', [\n                    t.variableDeclarator(\n                      t.identifier(splitMeta.localExporterIdent),\n                      expression,\n                    ),\n                  ]),\n                )\n              } else {\n                throw new Error(\n                  `Unexpected expression type encounter for \"${SPLIT_TYPE}\" in the node type \"${splitNode.type}\"`,\n                )\n              }\n            } else if (t.isConditionalExpression(splitNode)) {\n              programPath.pushContainer(\n                'body',\n                t.variableDeclaration('const', [\n                  t.variableDeclarator(\n                    t.identifier(splitMeta.localExporterIdent),\n                    splitNode,\n                  ),\n                ]),\n              )\n            } else if (t.isTSAsExpression(splitNode)) {\n              // remove the type assertion\n              splitNode = splitNode.expression\n              programPath.pushContainer(\n                'body',\n                t.variableDeclaration('const', [\n                  t.variableDeclarator(\n                    t.identifier(splitMeta.localExporterIdent),\n                    splitNode,\n                  ),\n                ]),\n              )\n            } else {\n              console.info('Unexpected splitNode type:', splitNode)\n              throw new Error(`Unexpected splitNode type ☝️: ${splitNode.type}`)\n            }\n          }\n\n          // If the splitNode exists at the top of the program\n          // then we need to remove that copy\n          programPath.node.body = programPath.node.body.filter((node) => {\n            return node !== splitNode\n          })\n\n          // Export the node\n          programPath.pushContainer('body', [\n            t.exportNamedDeclaration(null, [\n              t.exportSpecifier(\n                t.identifier(splitMeta.localExporterIdent), // local variable name\n                t.identifier(splitMeta.exporterIdent), // as what name it should be exported as\n              ),\n            ]),\n          ])\n        })\n\n        // convert exports to imports from the original file\n        programPath.traverse({\n          ExportNamedDeclaration(path) {\n            // e.g. export const x = 1 or export { x }\n            // becomes\n            // import { x } from '${opts.id}'\n\n            if (path.node.declaration) {\n              if (t.isVariableDeclaration(path.node.declaration)) {\n                path.replaceWith(\n                  t.importDeclaration(\n                    path.node.declaration.declarations.map((decl) =>\n                      t.importSpecifier(\n                        t.identifier((decl.id as any).name),\n                        t.identifier((decl.id as any).name),\n                      ),\n                    ),\n                    t.stringLiteral(\n                      removeSplitSearchParamFromFilename(opts.filename),\n                    ),\n                  ),\n                )\n              }\n            }\n          },\n        })\n      },\n    },\n  })\n\n  deadCodeElimination(ast, refIdents)\n\n  // if there are exported identifiers, then we need to add a warning\n  // to the file to let the user know that the exported identifiers\n  // will not in the split file but in the original file, therefore\n  // increasing the bundle size\n  if (knownExportedIdents.size > 0) {\n    const list = Array.from(knownExportedIdents).reduce((str, ident) => {\n      str += `\\n- ${ident}`\n      return str\n    }, '')\n\n    const warningMessage = `These exports from \"${opts.filename}\" are not being code-split and will increase your bundle size: ${list}\\nThese should either have their export statements removed or be imported from another file that is not a route.`\n    console.warn(warningMessage)\n\n    // append this warning to the file using a template\n    if (process.env.NODE_ENV !== 'production') {\n      const warningTemplate = template.statement(\n        `console.warn(${JSON.stringify(warningMessage)})`,\n      )()\n      ast.program.body.unshift(warningTemplate)\n    }\n  }\n\n  return generateFromAst(ast, {\n    sourceMaps: true,\n    sourceFileName: opts.filename,\n    filename: opts.filename,\n  })\n}\n\n/**\n * This function should read get the options from by searching for the key `codeSplitGroupings`\n * on createFileRoute and return it's values if it exists, else return undefined\n */\nexport function detectCodeSplitGroupingsFromRoute(opts: ParseAstOptions): {\n  groupings: CodeSplitGroupings | undefined\n} {\n  const ast = parseAst(opts)\n\n  let codeSplitGroupings: CodeSplitGroupings | undefined = undefined\n\n  babel.traverse(ast, {\n    Program: {\n      enter(programPath) {\n        programPath.traverse({\n          CallExpression(path) {\n            if (!t.isIdentifier(path.node.callee)) {\n              return\n            }\n\n            if (\n              !(\n                path.node.callee.name === 'createRoute' ||\n                path.node.callee.name === 'createFileRoute'\n              )\n            ) {\n              return\n            }\n\n            function babelHandleSplittingGroups(\n              routeOptions: t.Node | undefined,\n            ) {\n              if (t.isObjectExpression(routeOptions)) {\n                routeOptions.properties.forEach((prop) => {\n                  if (t.isObjectProperty(prop)) {\n                    if (t.isIdentifier(prop.key)) {\n                      if (prop.key.name === 'codeSplitGroupings') {\n                        const value = prop.value\n\n                        if (t.isArrayExpression(value)) {\n                          codeSplitGroupings = value.elements.map((group) => {\n                            if (t.isArrayExpression(group)) {\n                              return group.elements.map((node) => {\n                                if (!t.isStringLiteral(node)) {\n                                  throw new Error(\n                                    'You must provide a string literal for the codeSplitGroupings',\n                                  )\n                                }\n\n                                return node.value\n                              }) as Array<SplitRouteIdentNodes>\n                            }\n\n                            throw new Error(\n                              'You must provide arrays with codeSplitGroupings options.',\n                            )\n                          })\n                        } else {\n                          throw new Error(\n                            'You must provide an array of arrays for the codeSplitGroupings.',\n                          )\n                        }\n                      }\n                    }\n                  }\n                })\n              }\n            }\n\n            // Extracting the codeSplitGroupings\n            if (t.isCallExpression(path.parentPath.node)) {\n              // createFileRoute('/')({ ... })\n              const options = resolveIdentifier(\n                path,\n                path.parentPath.node.arguments[0],\n              )\n\n              babelHandleSplittingGroups(options)\n            } else if (t.isVariableDeclarator(path.parentPath.node)) {\n              // createFileRoute({ ... })\n              const caller = resolveIdentifier(path, path.parentPath.node.init)\n\n              if (t.isCallExpression(caller)) {\n                const options = resolveIdentifier(path, caller.arguments[0])\n                babelHandleSplittingGroups(options)\n              }\n            }\n          },\n        })\n      },\n    },\n  })\n\n  return { groupings: codeSplitGroupings }\n}\n\nfunction getImportSpecifierAndPathFromLocalName(\n  programPath: babel.NodePath<t.Program>,\n  name: string,\n): {\n  specifier:\n    | t.ImportSpecifier\n    | t.ImportDefaultSpecifier\n    | t.ImportNamespaceSpecifier\n    | null\n  path: string | null\n} {\n  let specifier:\n    | t.ImportSpecifier\n    | t.ImportDefaultSpecifier\n    | t.ImportNamespaceSpecifier\n    | null = null\n  let path: string | null = null\n\n  programPath.traverse({\n    ImportDeclaration(importPath) {\n      const found = importPath.node.specifiers.find(\n        (targetSpecifier) => targetSpecifier.local.name === name,\n      )\n      if (found) {\n        specifier = found\n        path = importPath.node.source.value\n      }\n    },\n  })\n\n  return { specifier, path }\n}\n\n// Reusable function to get literal value or resolve variable to literal\nfunction resolveIdentifier(path: any, node: any): t.Node | undefined {\n  if (t.isIdentifier(node)) {\n    const binding = path.scope.getBinding(node.name)\n    if (\n      binding\n      // && binding.kind === 'const'\n    ) {\n      const declarator = binding.path.node\n      if (t.isObjectExpression(declarator.init)) {\n        return declarator.init\n      } else if (t.isFunctionDeclaration(declarator.init)) {\n        return declarator.init\n      }\n    }\n    return undefined\n  }\n\n  return node\n}\n\nfunction removeIdentifierLiteral(path: babel.NodePath, node: t.Identifier) {\n  const binding = path.scope.getBinding(node.name)\n  if (binding) {\n    binding.path.remove()\n  }\n}\n\nfunction hasExport(ast: t.File, node: t.Identifier): boolean {\n  let found = false\n\n  babel.traverse(ast, {\n    ExportNamedDeclaration(path) {\n      if (path.node.declaration) {\n        // declared as `const loaderFn = () => {}`\n        if (t.isVariableDeclaration(path.node.declaration)) {\n          path.node.declaration.declarations.forEach((decl) => {\n            if (t.isVariableDeclarator(decl)) {\n              if (t.isIdentifier(decl.id)) {\n                if (decl.id.name === node.name) {\n                  found = true\n                }\n              }\n            }\n          })\n        }\n\n        // declared as `function loaderFn() {}`\n        if (t.isFunctionDeclaration(path.node.declaration)) {\n          if (t.isIdentifier(path.node.declaration.id)) {\n            if (path.node.declaration.id.name === node.name) {\n              found = true\n            }\n          }\n        }\n      }\n    },\n    ExportDefaultDeclaration(path) {\n      // declared as `export default loaderFn`\n      if (t.isIdentifier(path.node.declaration)) {\n        if (path.node.declaration.name === node.name) {\n          found = true\n        }\n      }\n\n      // declared as `export default function loaderFn() {}`\n      if (t.isFunctionDeclaration(path.node.declaration)) {\n        if (t.isIdentifier(path.node.declaration.id)) {\n          if (path.node.declaration.id.name === node.name) {\n            found = true\n          }\n        }\n      }\n    },\n  })\n\n  return found\n}\n\nfunction removeExports(ast: t.File, node: t.Identifier): boolean {\n  let removed = false\n\n  // The checks use sequential if/else if statements since it\n  // directly mutates the AST and as such doing normal checks\n  // (using only if statements) could lead to a situation where\n  // `path.node` is null since it has been already removed from\n  // the program tree but typescript doesn't know that.\n  babel.traverse(ast, {\n    ExportNamedDeclaration(path) {\n      if (path.node.declaration) {\n        if (t.isVariableDeclaration(path.node.declaration)) {\n          // declared as `const loaderFn = () => {}`\n          path.node.declaration.declarations.forEach((decl) => {\n            if (t.isVariableDeclarator(decl)) {\n              if (t.isIdentifier(decl.id)) {\n                if (decl.id.name === node.name) {\n                  path.remove()\n                  removed = true\n                }\n              }\n            }\n          })\n        } else if (t.isFunctionDeclaration(path.node.declaration)) {\n          // declared as `export const loaderFn = () => {}`\n          if (t.isIdentifier(path.node.declaration.id)) {\n            if (path.node.declaration.id.name === node.name) {\n              path.remove()\n              removed = true\n            }\n          }\n        }\n      }\n    },\n    ExportDefaultDeclaration(path) {\n      // declared as `export default loaderFn`\n      if (t.isIdentifier(path.node.declaration)) {\n        if (path.node.declaration.name === node.name) {\n          path.remove()\n          removed = true\n        }\n      } else if (t.isFunctionDeclaration(path.node.declaration)) {\n        // declared as `export default function loaderFn() {}`\n        if (t.isIdentifier(path.node.declaration.id)) {\n          if (path.node.declaration.id.name === node.name) {\n            path.remove()\n            removed = true\n          }\n        }\n      }\n    },\n  })\n\n  return removed\n}\n"], "names": ["tsrSplit", "createIdentifier", "parseAst", "findReferencedIdentifiers", "frameworkOptions", "getFrameworkOptions", "t", "routeHmrStatement", "template", "deadCodeElimination", "generateFromAst"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,MAAM,yCAAyB,IAAyC;AAAA,EACtE;AAAA,IACE;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,oBAAoB;AAAA;AAAA,MACpB,eAAe;AAAA,MACf,oBAAoB;AAAA;AAAA,MACpB,eAAe;AAAA;AAAA,IAAA;AAAA,EAEnB;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,oBAAoB;AAAA;AAAA,MACpB,eAAe;AAAA,MACf,oBAAoB;AAAA;AAAA,MACpB,eAAe;AAAA;AAAA,IAAA;AAAA,EAEnB;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,oBAAoB;AAAA;AAAA,MACpB,eAAe;AAAA,MACf,oBAAoB;AAAA;AAAA,MACpB,eAAe;AAAA;AAAA,IAAA;AAAA,EAEnB;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,oBAAoB;AAAA;AAAA,MACpB,eAAe;AAAA,MACf,oBAAoB;AAAA;AAAA,MACpB,eAAe;AAAA;AAAA,IAAA;AAAA,EAEnB;AAAA,EACA;AAAA,IACE;AAAA,IACA;AAAA,MACE,YAAY;AAAA,MACZ,oBAAoB;AAAA;AAAA,MACpB,eAAe;AAAA,MACf,oBAAoB;AAAA;AAAA,MACpB,eAAe;AAAA;AAAA,IAAA;AAAA,EACjB;AAEJ,CAAC;AACD,MAAM,2BAA2B,CAAC,GAAG,mBAAmB,MAAM;AAE9D,SAAS,8BACP,UACA,UACA;AACA,QAAM,CAAC,YAAY,IAAI,SAAS,MAAM,GAAG;AAEnC,QAAA,SAAS,IAAI,gBAAgB;AACnC,SAAO,OAAOA,UAAAA,UAAUC,QAAiB,iBAAA,QAAQ,CAAC;AAElD,SAAO,GAAG,YAAY,IAAI,OAAO,SAAU,CAAA;AAC7C;AAEA,SAAS,mCAAmC,UAAkB;AAC5D,QAAM,CAAC,YAAY,IAAI,SAAS,MAAM,GAAG;AAClC,SAAA;AACT;AAEA,MAAM,2BAA2B,CAAC,iBAAiB;AACnD,MAAM,6BAA6B;AAAA,EACjC;AAAA,EACA;AACF;AACA,MAAM,oBAAoB;AAAA,EACxB,GAAG;AAAA,EACH,GAAG;AACL;AAEO,SAAS,+BACd,MAQiB;AACX,QAAA,MAAMC,qBAAS,IAAI;AAEnB,QAAA,YAAYC,mDAA0B,GAAG;AAE/C,WAAS,sBAAsB,KAAa;AAC1C,WAAO,KAAK,mBAAmB;AAAA,MAAU,CAAC,UACxC,MAAM,SAAS,GAAU;AAAA,IAC3B;AAAA,EAAA;AAGI,QAAAC,qBAAmBC,iBAAAA,oBAAoB,KAAK,eAAe;AACjE,QAAM,UAAUD,mBAAiB;AAC3B,QAAA,6BAA6BA,mBAAiB,OAAO;AACrD,QAAA,gBAAgBA,mBAAiB,OAAO;AAE1C,MAAA;AAEJ,QAAM,SAAS,KAAK;AAAA,IAClB,SAAS;AAAA,MACP,MAAM,aAAa;AAUjB,cAAM,uBAAuB,oBAAI,IAAY,EAAE;AAE/C,oBAAY,SAAS;AAAA,UACnB,gBAAgB,CAAC,SAAS;AACxB,gBAAI,CAACE,aAAE,aAAa,KAAK,KAAK,MAAM,GAAG;AACrC;AAAA,YAAA;AAGF,gBAAI,CAAC,kBAAkB,SAAS,KAAK,KAAK,OAAO,IAAI,GAAG;AACtD;AAAA,YAAA;AAGc,4BAAA,KAAK,KAAK,OAAO;AAEjC,qBAAS,qBAAqB,cAAkC;AACxD,oBAAA,iCAAiC,CAAC,SAAiB;AAChD,uBAAA,YAAY,MAAM,WAAW,IAAI;AAAA,cAC1C;AAEI,kBAAAA,aAAE,mBAAmB,YAAY,GAAG;AACtC,oBAAI,KAAK,eAAe,KAAK,YAAY,OAAO,GAAG;AACpC,+BAAA,aAAa,aAAa,WAAW;AAAA,oBAChD,CAAC,SAAS;;AACJ,0BAAAA,aAAE,iBAAiB,IAAI,GAAG;AAC5B,4BAAIA,aAAE,aAAa,KAAK,GAAG,GAAG;AAC5B,+BAAI,UAAK,gBAAL,mBAAkB,IAAI,KAAK,IAAI,OAAc;AACxC,mCAAA;AAAA,0BAAA;AAAA,wBACT;AAAA,sBACF;AAEK,6BAAA;AAAA,oBAAA;AAAA,kBAEX;AAAA,gBAAA;AAEF,oBAAI,CAAC,yBAAyB,SAAS,aAAa,GAAG;AAErD,sBAAI,KAAK,QAAQ;AACH,gCAAA,cAAc,QAAQC,mCAAiB;AAAA,kBAAA;AAGrD,yBAAO,YAAY,KAAK;AAAA,gBAAA;AAEb,6BAAA,WAAW,QAAQ,CAAC,SAAS;AACpC,sBAAAD,aAAE,iBAAiB,IAAI,GAAG;AAC5B,wBAAIA,aAAE,aAAa,KAAK,GAAG,GAAG;AACtB,4BAAA,MAAM,KAAK,IAAI;AAIf,4BAAA,yBAAyB,sBAAsB,GAAG;AACxD,0BAAI,2BAA2B,IAAI;AACjC;AAAA,sBAAA;AAEF,4BAAM,iBAAiB;AAAA,wBACrB,GAAG,IAAI;AAAA,0BACL,KAAK,mBAAmB,sBAAsB;AAAA,wBAAA;AAAA,sBAElD;AAGA,4BAAM,wBAAwB,mBAAmB;AAAA,wBAC/C;AAAA,sBACF;AAEA,0BAAI,CAAC,uBAAuB;AAC1B;AAAA,sBAAA;AAOA,0BAAAA,aAAE,aAAa,KAAK,KAAK,KACzB,KAAK,MAAM,SAAS,aACpB;AACA;AAAA,sBAAA;AAGI,4BAAA,gBAAgB,mBAAmB,IAAI,GAAU;AAIvD,4BAAM,WAAW;AAAA,wBACf,KAAK;AAAA,wBACL;AAAA,sBACF;AAGE,0BAAA,cAAc,kBAAkB,sBAChC;AACA,8BAAM,QAAQ,KAAK;AAEnB,4BAAI,cAAc;AAEd,4BAAAA,aAAE,aAAa,KAAK,GAAG;AACzB,gCAAM,qBACJ;AAAA,4BACE;AAAA,4BACA,MAAM;AAAA,0BAAA,EACN;AACJ,8BAAI,oBAAoB;AACtB,iDAAqB,IAAI,kBAAkB;AAAA,0BAAA;AAMvC,gCAAA,aAAa,UAAU,KAAK,KAAK;AACvC,wCAAc,CAAC;AAEf,8BAAI,aAAa;AACf,oDAAwB,MAAM,KAAK;AAAA,0BAAA;AAAA,wBACrC;AAGF,4BAAI,CAAC,aAAa;AAChB;AAAA,wBAAA;AAMF,4BACE,CAAC;AAAA,0BACC;AAAA,wBAAA,GAEF;AACA,sCAAY,iBAAiB,QAAQ;AAAA,4BACnCE,oBAAS;AAAA,8BACP,YAAY,0BAA0B,YAAY,OAAO;AAAA,4BACzD,EAAA;AAAA,0BAAA,CACH;AAAA,wBAAA;AAKH,4BACE,CAAC;AAAA,0BACC,cAAc;AAAA,wBAAA,GAEhB;AACA,sCAAY,iBAAiB,QAAQ;AAAA,4BACnCA,oBAAS;AAAA,8BACP,SAAS,cAAc,kBAAkB,oBAAoB,QAAQ;AAAA,4BACrE,EAAA;AAAA,0BAAA,CACH;AAAA,wBAAA;AAGH,6BAAK,QAAQA,oBAAS;AAAA,0BACpB,GAAG,0BAA0B,IAAI,cAAc,kBAAkB,MAAM,cAAc,aAAa;AAAA,wBAAA,EAClG;AAGF,4BAAI,KAAK,QAAQ;AACH,sCAAA,cAAc,QAAQD,mCAAiB;AAAA,wBAAA;AAAA,sBACrD;AAGE,0BAAA,cAAc,kBAAkB,UAAU;AAC5C,8BAAM,QAAQ,KAAK;AAEnB,4BAAI,cAAc;AAEd,4BAAAD,aAAE,aAAa,KAAK,GAAG;AACzB,gCAAM,qBACJ;AAAA,4BACE;AAAA,4BACA,MAAM;AAAA,0BAAA,EACN;AACJ,8BAAI,oBAAoB;AACtB,iDAAqB,IAAI,kBAAkB;AAAA,0BAAA;AAMvC,gCAAA,aAAa,UAAU,KAAK,KAAK;AACvC,wCAAc,CAAC;AAEf,8BAAI,aAAa;AACf,oDAAwB,MAAM,KAAK;AAAA,0BAAA;AAAA,wBACrC;AAGF,4BAAI,CAAC,aAAa;AAChB;AAAA,wBAAA;AAIE,4BAAA,CAAC,+BAA+B,aAAa,GAAG;AACtC,sCAAA;AAAA,4BACV;AAAA,4BACAE,oBAAS;AAAA,8BACP,YAAY,aAAa,YAAY,OAAO;AAAA,4BAC5C,EAAA;AAAA,0BACJ;AAAA,wBAAA;AAKF,4BACE,CAAC;AAAA,0BACC,cAAc;AAAA,wBAAA,GAEhB;AACA,sCAAY,iBAAiB,QAAQ;AAAA,4BACnCA,oBAAS;AAAA,8BACP,SAAS,cAAc,kBAAkB,oBAAoB,QAAQ;AAAA,4BACrE,EAAA;AAAA,0BAAA,CACH;AAAA,wBAAA;AAIH,6BAAK,QAAQA,oBAAS;AAAA,0BACpB,GAAG,aAAa,IAAI,cAAc,kBAAkB,MAAM,cAAc,aAAa;AAAA,wBAAA,EACrF;AAAA,sBAAA;AAAA,oBACJ;AAAA,kBACF;AAGF,8BAAY,MAAM,MAAM;AAAA,gBAAA,CACzB;AAAA,cAAA;AAAA,YACH;AAGF,gBAAIF,aAAE,iBAAiB,KAAK,WAAW,IAAI,GAAG;AAE5C,oBAAM,UAAU;AAAA,gBACd;AAAA,gBACA,KAAK,WAAW,KAAK,UAAU,CAAC;AAAA,cAClC;AAEA,mCAAqB,OAAO;AAAA,YAAA,WACnBA,aAAE,qBAAqB,KAAK,WAAW,IAAI,GAAG;AAEvD,oBAAM,SAAS,kBAAkB,MAAM,KAAK,WAAW,KAAK,IAAI;AAE5D,kBAAAA,aAAE,iBAAiB,MAAM,GAAG;AAC9B,sBAAM,UAAU,kBAAkB,MAAM,OAAO,UAAU,CAAC,CAAC;AAC3D,qCAAqB,OAAO;AAAA,cAAA;AAAA,YAC9B;AAAA,UACF;AAAA,QACF,CACD;AAQG,YAAA,qBAAqB,OAAO,GAAG;AACjC,sBAAY,SAAS;AAAA,YACnB,kBAAkB,MAAM;AACtB,kBAAI,KAAK,KAAK,WAAW,SAAS,EAAG;AACrC,kBAAI,qBAAqB,IAAI,KAAK,KAAK,OAAO,KAAK,GAAG;AACpD,qBAAK,OAAO;AAAA,cAAA;AAAA,YACd;AAAA,UACF,CACD;AAAA,QAAA;AAAA,MACH;AAAA,IACF;AAAA,EACF,CACD;AAEDG,2BAAA,oBAAoB,KAAK,SAAS;AAElC,SAAOC,YAAAA,gBAAgB,KAAK;AAAA,IAC1B,YAAY;AAAA,IACZ,gBAAgB,KAAK;AAAA,IACrB,UAAU,KAAK;AAAA,EAAA,CAChB;AACH;AAEO,SAAS,6BACd,MAIiB;AACX,QAAA,MAAMR,qBAAS,IAAI;AACnB,QAAA,YAAYC,mDAA0B,GAAG;AAE/C,QAAM,qBAAqB,IAAI,IAAI,KAAK,YAAY;AAE9C,QAAA,0CAA0B,IAAY;AAE5C,QAAM,SAAS,KAAK;AAAA,IAClB,SAAS;AAAA,MACP,MAAM,aAAa;AACjB,cAAM,4BAGF;AAAA,UACF,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,kBAAkB;AAAA,UAClB,gBAAgB;AAAA,UAChB,mBAAmB;AAAA,QACrB;AAGA,oBAAY,SAAS;AAAA,UACnB,gBAAgB,CAAC,SAAS;AACxB,gBAAI,CAACG,aAAE,aAAa,KAAK,KAAK,MAAM,GAAG;AACrC;AAAA,YAAA;AAGF,gBAAI,CAAC,yBAAyB,SAAS,KAAK,KAAK,OAAO,IAAI,GAAG;AAC7D;AAAA,YAAA;AAGF,qBAAS,mBAAmB,SAA6B;AACnD,kBAAAA,aAAE,mBAAmB,OAAO,GAAG;AACzB,wBAAA,WAAW,QAAQ,CAAC,SAAS;AAC/B,sBAAAA,aAAE,iBAAiB,IAAI,GAAG;AAIH,6CAAA,QAAQ,CAAC,cAAc;AAE5C,0BAAA,CAACA,aAAE,aAAa,KAAK,GAAG,KACxB,KAAK,IAAI,SAAS,WAClB;AACA;AAAA,sBAAA;AAGF,4BAAM,QAAQ,KAAK;AAMnB,0BAAIA,aAAE,aAAa,KAAK,KAAK,MAAM,SAAS,aAAa;AACvD;AAAA,sBAAA;AAGF,0BAAI,aAAa;AACb,0BAAAA,aAAE,aAAa,KAAK,GAAG;AACZ,qCAAA,UAAU,KAAK,KAAK;AACjC,4BAAI,YAAY;AACM,8CAAA,IAAI,MAAM,IAAI;AAAA,wBAAA;AAAA,sBACpC;AAKF,0BAAI,cAAcA,aAAE,aAAa,KAAK,GAAG;AACvC,sCAAc,KAAK,KAAK;AAAA,sBAAA,OACnB;AACC,8BAAA,OAAO,mBAAmB,IAAI,SAAS;AAC7C,kDAA0B,SAAS,IAAI;AAAA,0BACrC,MAAM,KAAK;AAAA,0BACX;AAAA,wBACF;AAAA,sBAAA;AAAA,oBACF,CACD;AAAA,kBAAA;AAAA,gBACH,CACD;AAGD,wBAAQ,aAAa,CAAC;AAAA,cAAA;AAAA,YACxB;AAGF,gBAAIA,aAAE,iBAAiB,KAAK,WAAW,IAAI,GAAG;AAE5C,oBAAM,UAAU;AAAA,gBACd;AAAA,gBACA,KAAK,WAAW,KAAK,UAAU,CAAC;AAAA,cAClC;AAEA,iCAAmB,OAAO;AAAA,YAAA,WACjBA,aAAE,qBAAqB,KAAK,WAAW,IAAI,GAAG;AAEvD,oBAAM,SAAS,kBAAkB,MAAM,KAAK,WAAW,KAAK,IAAI;AAE5D,kBAAAA,aAAE,iBAAiB,MAAM,GAAG;AAC9B,sBAAM,UAAU,kBAAkB,MAAM,OAAO,UAAU,CAAC,CAAC;AAC3D,mCAAmB,OAAO;AAAA,cAAA;AAAA,YAC5B;AAAA,UACF;AAAA,QACF,CACD;AAGkB,2BAAA,QAAQ,CAAC,eAAe;AACnC,gBAAA,WAAW,0BAA0B,UAAU;AAErD,cAAI,CAAC,UAAU;AACb;AAAA,UAAA;AAGF,cAAI,YAAY,SAAS;AACzB,gBAAM,YAAY,SAAS;AAEpB,iBAAAA,aAAE,aAAa,SAAS,GAAG;AAChC,kBAAM,UAAU,YAAY,MAAM,WAAW,UAAU,IAAI;AAC3D,wBAAY,mCAAS,KAAK;AAAA,UAAA;AAI5B,cAAI,WAAW;AACT,gBAAAA,aAAE,sBAAsB,SAAS,GAAG;AAC1B,0BAAA;AAAA,gBACV;AAAA,gBACAA,aAAE,oBAAoB,SAAS;AAAA,kBAC7BA,aAAE;AAAA,oBACAA,aAAE,WAAW,UAAU,kBAAkB;AAAA,oBACzCA,aAAE;AAAA,sBACA,UAAU,MAAM;AAAA;AAAA,sBAChB,UAAU;AAAA,sBACV,UAAU;AAAA,sBACV,UAAU;AAAA,sBACV,UAAU;AAAA,oBAAA;AAAA,kBACZ;AAAA,gBAEH,CAAA;AAAA,cACH;AAAA,YAAA,WAEAA,aAAE,qBAAqB,SAAS,KAChCA,aAAE,0BAA0B,SAAS,GACrC;AACY,0BAAA;AAAA,gBACV;AAAA,gBACAA,aAAE,oBAAoB,SAAS;AAAA,kBAC7BA,aAAE;AAAA,oBACAA,aAAE,WAAW,UAAU,kBAAkB;AAAA,oBACzC;AAAA,kBAAA;AAAA,gBAEH,CAAA;AAAA,cACH;AAAA,YAAA,WAEAA,aAAE,kBAAkB,SAAS,KAC7BA,aAAE,yBAAyB,SAAS,GACpC;AACY,0BAAA;AAAA,gBACV;AAAA,gBACAA,aAAE,oBAAoB,SAAS;AAAA,kBAC7BA,aAAE;AAAA,oBACAA,aAAE,WAAW,UAAU,kBAAkB;AAAA,oBACzC,UAAU;AAAA,kBAAA;AAAA,gBAEb,CAAA;AAAA,cACH;AAAA,YACS,WAAAA,aAAE,qBAAqB,SAAS,GAAG;AAChC,0BAAA;AAAA,gBACV;AAAA,gBACAA,aAAE,oBAAoB,SAAS;AAAA,kBAC7BA,aAAE;AAAA,oBACAA,aAAE,WAAW,UAAU,kBAAkB;AAAA,oBACzC,UAAU;AAAA,kBAAA;AAAA,gBAEb,CAAA;AAAA,cACH;AAAA,YACS,WAAAA,aAAE,iBAAiB,SAAS,GAAG;AAClC,oBAAA,sBAAsBI,YAAAA,gBAAgB,SAAS,EAAE;AACjD,oBAAA,eAAe,MAAM,MAAM,mBAAmB;AAEpD,kBAAI,CAAC,cAAc;AACjB,sBAAM,IAAI;AAAA,kBACR,2CAA2C,UAAU,uBAAuB,UAAU,IAAI;AAAA,gBAC5F;AAAA,cAAA;AAGF,oBAAM,YAAY,aAAa,QAAQ,KAAK,CAAC;AAE7C,kBAAI,CAAC,WAAW;AACd,sBAAM,IAAI;AAAA,kBACR,2CAA2C,UAAU,uBAAuB,UAAU,IAAI;AAAA,gBAC5F;AAAA,cAAA;AAGE,kBAAAJ,aAAE,sBAAsB,SAAS,GAAG;AACtC,sBAAM,aAAa,UAAU;AACjB,4BAAA;AAAA,kBACV;AAAA,kBACAA,aAAE,oBAAoB,SAAS;AAAA,oBAC7BA,aAAE;AAAA,sBACAA,aAAE,WAAW,UAAU,kBAAkB;AAAA,sBACzC;AAAA,oBAAA;AAAA,kBAEH,CAAA;AAAA,gBACH;AAAA,cAAA,OACK;AACL,sBAAM,IAAI;AAAA,kBACR,6CAA6C,UAAU,uBAAuB,UAAU,IAAI;AAAA,gBAC9F;AAAA,cAAA;AAAA,YAEO,WAAAA,aAAE,wBAAwB,SAAS,GAAG;AACnC,0BAAA;AAAA,gBACV;AAAA,gBACAA,aAAE,oBAAoB,SAAS;AAAA,kBAC7BA,aAAE;AAAA,oBACAA,aAAE,WAAW,UAAU,kBAAkB;AAAA,oBACzC;AAAA,kBAAA;AAAA,gBAEH,CAAA;AAAA,cACH;AAAA,YACS,WAAAA,aAAE,iBAAiB,SAAS,GAAG;AAExC,0BAAY,UAAU;AACV,0BAAA;AAAA,gBACV;AAAA,gBACAA,aAAE,oBAAoB,SAAS;AAAA,kBAC7BA,aAAE;AAAA,oBACAA,aAAE,WAAW,UAAU,kBAAkB;AAAA,oBACzC;AAAA,kBAAA;AAAA,gBAEH,CAAA;AAAA,cACH;AAAA,YAAA,OACK;AACG,sBAAA,KAAK,8BAA8B,SAAS;AACpD,oBAAM,IAAI,MAAM,iCAAiC,UAAU,IAAI,EAAE;AAAA,YAAA;AAAA,UACnE;AAKF,sBAAY,KAAK,OAAO,YAAY,KAAK,KAAK,OAAO,CAAC,SAAS;AAC7D,mBAAO,SAAS;AAAA,UAAA,CACjB;AAGD,sBAAY,cAAc,QAAQ;AAAA,YAChCA,aAAE,uBAAuB,MAAM;AAAA,cAC7BA,aAAE;AAAA,gBACAA,aAAE,WAAW,UAAU,kBAAkB;AAAA;AAAA,gBACzCA,aAAE,WAAW,UAAU,aAAa;AAAA;AAAA,cAAA;AAAA,YAEvC,CAAA;AAAA,UAAA,CACF;AAAA,QAAA,CACF;AAGD,oBAAY,SAAS;AAAA,UACnB,uBAAuB,MAAM;AAKvB,gBAAA,KAAK,KAAK,aAAa;AACzB,kBAAIA,aAAE,sBAAsB,KAAK,KAAK,WAAW,GAAG;AAC7C,qBAAA;AAAA,kBACHA,aAAE;AAAA,oBACA,KAAK,KAAK,YAAY,aAAa;AAAA,sBAAI,CAAC,SACtCA,aAAE;AAAA,wBACAA,aAAE,WAAY,KAAK,GAAW,IAAI;AAAA,wBAClCA,aAAE,WAAY,KAAK,GAAW,IAAI;AAAA,sBAAA;AAAA,oBAEtC;AAAA,oBACAA,aAAE;AAAA,sBACA,mCAAmC,KAAK,QAAQ;AAAA,oBAAA;AAAA,kBAClD;AAAA,gBAEJ;AAAA,cAAA;AAAA,YACF;AAAA,UACF;AAAA,QACF,CACD;AAAA,MAAA;AAAA,IACH;AAAA,EACF,CACD;AAEDG,2BAAA,oBAAoB,KAAK,SAAS;AAM9B,MAAA,oBAAoB,OAAO,GAAG;AAC1B,UAAA,OAAO,MAAM,KAAK,mBAAmB,EAAE,OAAO,CAAC,KAAK,UAAU;AAC3D,aAAA;AAAA,IAAO,KAAK;AACZ,aAAA;AAAA,OACN,EAAE;AAEL,UAAM,iBAAiB,uBAAuB,KAAK,QAAQ,kEAAkE,IAAI;AAAA;AACjI,YAAQ,KAAK,cAAc;AAGvB,QAAA,QAAQ,IAAI,aAAa,cAAc;AACzC,YAAM,kBAAkBD,oBAAS;AAAA,QAC/B,gBAAgB,KAAK,UAAU,cAAc,CAAC;AAAA,MAAA,EAC9C;AACE,UAAA,QAAQ,KAAK,QAAQ,eAAe;AAAA,IAAA;AAAA,EAC1C;AAGF,SAAOE,YAAAA,gBAAgB,KAAK;AAAA,IAC1B,YAAY;AAAA,IACZ,gBAAgB,KAAK;AAAA,IACrB,UAAU,KAAK;AAAA,EAAA,CAChB;AACH;AAMO,SAAS,kCAAkC,MAEhD;AACM,QAAA,MAAMR,qBAAS,IAAI;AAEzB,MAAI,qBAAqD;AAEzD,QAAM,SAAS,KAAK;AAAA,IAClB,SAAS;AAAA,MACP,MAAM,aAAa;AACjB,oBAAY,SAAS;AAAA,UACnB,eAAe,MAAM;AACnB,gBAAI,CAACI,aAAE,aAAa,KAAK,KAAK,MAAM,GAAG;AACrC;AAAA,YAAA;AAIA,gBAAA,EACE,KAAK,KAAK,OAAO,SAAS,iBAC1B,KAAK,KAAK,OAAO,SAAS,oBAE5B;AACA;AAAA,YAAA;AAGF,qBAAS,2BACP,cACA;AACI,kBAAAA,aAAE,mBAAmB,YAAY,GAAG;AACzB,6BAAA,WAAW,QAAQ,CAAC,SAAS;AACpC,sBAAAA,aAAE,iBAAiB,IAAI,GAAG;AAC5B,wBAAIA,aAAE,aAAa,KAAK,GAAG,GAAG;AACxB,0BAAA,KAAK,IAAI,SAAS,sBAAsB;AAC1C,8BAAM,QAAQ,KAAK;AAEf,4BAAAA,aAAE,kBAAkB,KAAK,GAAG;AAC9B,+CAAqB,MAAM,SAAS,IAAI,CAAC,UAAU;AAC7C,gCAAAA,aAAE,kBAAkB,KAAK,GAAG;AAC9B,qCAAO,MAAM,SAAS,IAAI,CAAC,SAAS;AAClC,oCAAI,CAACA,aAAE,gBAAgB,IAAI,GAAG;AAC5B,wCAAM,IAAI;AAAA,oCACR;AAAA,kCACF;AAAA,gCAAA;AAGF,uCAAO,KAAK;AAAA,8BAAA,CACb;AAAA,4BAAA;AAGH,kCAAM,IAAI;AAAA,8BACR;AAAA,4BACF;AAAA,0BAAA,CACD;AAAA,wBAAA,OACI;AACL,gCAAM,IAAI;AAAA,4BACR;AAAA,0BACF;AAAA,wBAAA;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF,CACD;AAAA,cAAA;AAAA,YACH;AAIF,gBAAIA,aAAE,iBAAiB,KAAK,WAAW,IAAI,GAAG;AAE5C,oBAAM,UAAU;AAAA,gBACd;AAAA,gBACA,KAAK,WAAW,KAAK,UAAU,CAAC;AAAA,cAClC;AAEA,yCAA2B,OAAO;AAAA,YAAA,WACzBA,aAAE,qBAAqB,KAAK,WAAW,IAAI,GAAG;AAEvD,oBAAM,SAAS,kBAAkB,MAAM,KAAK,WAAW,KAAK,IAAI;AAE5D,kBAAAA,aAAE,iBAAiB,MAAM,GAAG;AAC9B,sBAAM,UAAU,kBAAkB,MAAM,OAAO,UAAU,CAAC,CAAC;AAC3D,2CAA2B,OAAO;AAAA,cAAA;AAAA,YACpC;AAAA,UACF;AAAA,QACF,CACD;AAAA,MAAA;AAAA,IACH;AAAA,EACF,CACD;AAEM,SAAA,EAAE,WAAW,mBAAmB;AACzC;AAEA,SAAS,uCACP,aACA,MAQA;AACA,MAAI,YAIO;AACX,MAAI,OAAsB;AAE1B,cAAY,SAAS;AAAA,IACnB,kBAAkB,YAAY;AACtB,YAAA,QAAQ,WAAW,KAAK,WAAW;AAAA,QACvC,CAAC,oBAAoB,gBAAgB,MAAM,SAAS;AAAA,MACtD;AACA,UAAI,OAAO;AACG,oBAAA;AACL,eAAA,WAAW,KAAK,OAAO;AAAA,MAAA;AAAA,IAChC;AAAA,EACF,CACD;AAEM,SAAA,EAAE,WAAW,KAAK;AAC3B;AAGA,SAAS,kBAAkB,MAAW,MAA+B;AAC/D,MAAAA,aAAE,aAAa,IAAI,GAAG;AACxB,UAAM,UAAU,KAAK,MAAM,WAAW,KAAK,IAAI;AAC/C,QACE,SAEA;AACM,YAAA,aAAa,QAAQ,KAAK;AAChC,UAAIA,aAAE,mBAAmB,WAAW,IAAI,GAAG;AACzC,eAAO,WAAW;AAAA,MACT,WAAAA,aAAE,sBAAsB,WAAW,IAAI,GAAG;AACnD,eAAO,WAAW;AAAA,MAAA;AAAA,IACpB;AAEK,WAAA;AAAA,EAAA;AAGF,SAAA;AACT;AAEA,SAAS,wBAAwB,MAAsB,MAAoB;AACzE,QAAM,UAAU,KAAK,MAAM,WAAW,KAAK,IAAI;AAC/C,MAAI,SAAS;AACX,YAAQ,KAAK,OAAO;AAAA,EAAA;AAExB;AAEA,SAAS,UAAU,KAAa,MAA6B;AAC3D,MAAI,QAAQ;AAEZ,QAAM,SAAS,KAAK;AAAA,IAClB,uBAAuB,MAAM;AACvB,UAAA,KAAK,KAAK,aAAa;AAEzB,YAAIA,aAAE,sBAAsB,KAAK,KAAK,WAAW,GAAG;AAClD,eAAK,KAAK,YAAY,aAAa,QAAQ,CAAC,SAAS;AAC/C,gBAAAA,aAAE,qBAAqB,IAAI,GAAG;AAChC,kBAAIA,aAAE,aAAa,KAAK,EAAE,GAAG;AAC3B,oBAAI,KAAK,GAAG,SAAS,KAAK,MAAM;AACtB,0BAAA;AAAA,gBAAA;AAAA,cACV;AAAA,YACF;AAAA,UACF,CACD;AAAA,QAAA;AAIH,YAAIA,aAAE,sBAAsB,KAAK,KAAK,WAAW,GAAG;AAClD,cAAIA,aAAE,aAAa,KAAK,KAAK,YAAY,EAAE,GAAG;AAC5C,gBAAI,KAAK,KAAK,YAAY,GAAG,SAAS,KAAK,MAAM;AACvC,sBAAA;AAAA,YAAA;AAAA,UACV;AAAA,QACF;AAAA,MACF;AAAA,IAEJ;AAAA,IACA,yBAAyB,MAAM;AAE7B,UAAIA,aAAE,aAAa,KAAK,KAAK,WAAW,GAAG;AACzC,YAAI,KAAK,KAAK,YAAY,SAAS,KAAK,MAAM;AACpC,kBAAA;AAAA,QAAA;AAAA,MACV;AAIF,UAAIA,aAAE,sBAAsB,KAAK,KAAK,WAAW,GAAG;AAClD,YAAIA,aAAE,aAAa,KAAK,KAAK,YAAY,EAAE,GAAG;AAC5C,cAAI,KAAK,KAAK,YAAY,GAAG,SAAS,KAAK,MAAM;AACvC,oBAAA;AAAA,UAAA;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF,CACD;AAEM,SAAA;AACT;AAEA,SAAS,cAAc,KAAa,MAA6B;AAC/D,MAAI,UAAU;AAOd,QAAM,SAAS,KAAK;AAAA,IAClB,uBAAuB,MAAM;AACvB,UAAA,KAAK,KAAK,aAAa;AACzB,YAAIA,aAAE,sBAAsB,KAAK,KAAK,WAAW,GAAG;AAElD,eAAK,KAAK,YAAY,aAAa,QAAQ,CAAC,SAAS;AAC/C,gBAAAA,aAAE,qBAAqB,IAAI,GAAG;AAChC,kBAAIA,aAAE,aAAa,KAAK,EAAE,GAAG;AAC3B,oBAAI,KAAK,GAAG,SAAS,KAAK,MAAM;AAC9B,uBAAK,OAAO;AACF,4BAAA;AAAA,gBAAA;AAAA,cACZ;AAAA,YACF;AAAA,UACF,CACD;AAAA,QAAA,WACQA,aAAE,sBAAsB,KAAK,KAAK,WAAW,GAAG;AAEzD,cAAIA,aAAE,aAAa,KAAK,KAAK,YAAY,EAAE,GAAG;AAC5C,gBAAI,KAAK,KAAK,YAAY,GAAG,SAAS,KAAK,MAAM;AAC/C,mBAAK,OAAO;AACF,wBAAA;AAAA,YAAA;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAAA,IAEJ;AAAA,IACA,yBAAyB,MAAM;AAE7B,UAAIA,aAAE,aAAa,KAAK,KAAK,WAAW,GAAG;AACzC,YAAI,KAAK,KAAK,YAAY,SAAS,KAAK,MAAM;AAC5C,eAAK,OAAO;AACF,oBAAA;AAAA,QAAA;AAAA,MACZ,WACSA,aAAE,sBAAsB,KAAK,KAAK,WAAW,GAAG;AAEzD,YAAIA,aAAE,aAAa,KAAK,KAAK,YAAY,EAAE,GAAG;AAC5C,cAAI,KAAK,KAAK,YAAY,GAAG,SAAS,KAAK,MAAM;AAC/C,iBAAK,OAAO;AACF,sBAAA;AAAA,UAAA;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,EACF,CACD;AAEM,SAAA;AACT;;;;"}