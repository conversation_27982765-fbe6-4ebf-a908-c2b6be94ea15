{"version": 3, "file": "constants.cjs", "sources": ["../../../src/core/constants.ts"], "sourcesContent": ["export const tsrSplit = 'tsr-split'\n\nexport const splitRouteIdentNodes = [\n  'loader',\n  'component',\n  'pendingComponent',\n  'errorComponent',\n  'notFoundComponent',\n] as const\nexport type SplitRouteIdentNodes = (typeof splitRouteIdentNodes)[number]\nexport type CodeSplitGroupings = Array<Array<SplitRouteIdentNodes>>\n\nexport const defaultCodeSplitGroupings: CodeSplitGroupings = [\n  ['component'],\n  ['errorComponent'],\n  ['notFoundComponent'],\n]\n"], "names": [], "mappings": ";;AAAO,MAAM,WAAW;AAEjB,MAAM,uBAAuB;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAIO,MAAM,4BAAgD;AAAA,EAC3D,CAAC,WAAW;AAAA,EACZ,CAAC,gBAAgB;AAAA,EACjB,CAAC,mBAAmB;AACtB;;;;"}