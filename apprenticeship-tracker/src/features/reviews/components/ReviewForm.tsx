import React from 'react'
import { <PERSON><PERSON> } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Select } from '../../../components/ui/select'
import { DatePicker } from '../../../components/ui/date-picker'
import { Textarea } from '../../../components/ui/textarea'
import { useFormValidation, validationRules } from '../../../lib/validation'
import { QuarterlyReview } from '../../../types'

interface ReviewFormProps {
  review?: QuarterlyReview
  onSubmit: (data: Partial<QuarterlyReview>) => void
  onCancel: () => void
  isLoading?: boolean
}

interface ReviewFormData {
  apprenticeId: string
  quarter: 1 | 2 | 3 | 4
  year: number
  reviewDate: string
  mentorId: string
  mentorName: string
  technicalSkills: number
  communication: number
  teamwork: number
  initiative: number
  punctuality: number
  strengths: string
  areasForImprovement: string
  goals: string
  mentorComments: string
  apprenticeComments: string
  status: 'scheduled' | 'in-progress' | 'completed' | 'overdue'
}

const quarterOptions = [
  { value: '1', label: 'Q1 (January - March)' },
  { value: '2', label: 'Q2 (April - June)' },
  { value: '3', label: 'Q3 (July - September)' },
  { value: '4', label: 'Q4 (October - December)' },
]

const statusOptions = [
  { value: 'scheduled', label: 'Scheduled' },
  { value: 'in-progress', label: 'In Progress' },
  { value: 'completed', label: 'Completed' },
  { value: 'overdue', label: 'Overdue' },
]

const mentorOptions = [
  { value: 'john-smith', label: 'John Smith' },
  { value: 'sarah-johnson', label: 'Sarah Johnson' },
  { value: 'mike-brown', label: 'Mike Brown' },
  { value: 'emily-davis', label: 'Emily Davis' },
  { value: 'david-wilson', label: 'David Wilson' },
]

const apprenticeOptions = [
  { value: 'apprentice-1', label: 'Alice Johnson' },
  { value: 'apprentice-2', label: 'Bob Smith' },
  { value: 'apprentice-3', label: 'Carol Brown' },
  { value: 'apprentice-4', label: 'David Wilson' },
  { value: 'apprentice-5', label: 'Emma Davis' },
]

export const ReviewForm: React.FC<ReviewFormProps> = ({
  review,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const currentYear = new Date().getFullYear()
  
  const initialData: ReviewFormData = {
    apprenticeId: review?.apprenticeId || '',
    quarter: review?.quarter || 1,
    year: review?.year || currentYear,
    reviewDate: review?.reviewDate ? review.reviewDate.split('T')[0] : '',
    mentorId: review?.mentorId || '',
    mentorName: review?.mentorName || '',
    technicalSkills: review?.scores?.technicalSkills || 0,
    communication: review?.scores?.communication || 0,
    teamwork: review?.scores?.teamwork || 0,
    initiative: review?.scores?.initiative || 0,
    punctuality: review?.scores?.punctuality || 0,
    strengths: review?.strengths?.join(', ') || '',
    areasForImprovement: review?.areasForImprovement?.join(', ') || '',
    goals: review?.goals?.join(', ') || '',
    mentorComments: review?.mentorComments || '',
    apprenticeComments: review?.apprenticeComments || '',
    status: review?.status || 'scheduled',
  }

  const validationConfig = {
    apprenticeId: {
      required: true
    },
    quarter: {
      required: true
    },
    year: {
      required: true,
      rules: [
        validationRules.minValue(2020),
        validationRules.maxValue(2030)
      ]
    },
    reviewDate: {
      required: true
    },
    mentorId: {
      required: true
    },
    mentorName: {
      required: true
    },
    technicalSkills: {
      required: true,
      rules: [
        validationRules.minValue(1),
        validationRules.maxValue(5)
      ]
    },
    communication: {
      required: true,
      rules: [
        validationRules.minValue(1),
        validationRules.maxValue(5)
      ]
    },
    teamwork: {
      required: true,
      rules: [
        validationRules.minValue(1),
        validationRules.maxValue(5)
      ]
    },
    initiative: {
      required: true,
      rules: [
        validationRules.minValue(1),
        validationRules.maxValue(5)
      ]
    },
    punctuality: {
      required: true,
      rules: [
        validationRules.minValue(1),
        validationRules.maxValue(5)
      ]
    },
    strengths: {
      required: true,
      rules: [validationRules.minLength(10)]
    },
    areasForImprovement: {
      required: true,
      rules: [validationRules.minLength(10)]
    },
    goals: {
      required: true,
      rules: [validationRules.minLength(10)]
    },
    mentorComments: {
      required: true,
      rules: [validationRules.minLength(20)]
    },
    apprenticeComments: {
      required: false
    },
    status: {
      required: true
    }
  }

  const {
    data,
    errors,
    touched,
    updateField,
    touchField,
    validateForm,
    isValid
  } = useFormValidation(initialData, validationConfig)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (validateForm()) {
      const overallRating = Math.round(
        (data.technicalSkills + data.communication + data.teamwork + data.initiative + data.punctuality) / 5
      )

      const formattedData = {
        ...data,
        reviewDate: new Date(data.reviewDate).toISOString(),
        scores: {
          technicalSkills: data.technicalSkills,
          communication: data.communication,
          teamwork: data.teamwork,
          initiative: data.initiative,
          punctuality: data.punctuality,
          overallRating
        },
        strengths: data.strengths.split(',').map(s => s.trim()).filter(s => s),
        areasForImprovement: data.areasForImprovement.split(',').map(s => s.trim()).filter(s => s),
        goals: data.goals.split(',').map(s => s.trim()).filter(s => s),
        ...(review?.id && { id: review.id })
      }
      
      onSubmit(formattedData)
    }
  }

  const calculateOverallRating = () => {
    if (data.technicalSkills && data.communication && data.teamwork && data.initiative && data.punctuality) {
      return Math.round((data.technicalSkills + data.communication + data.teamwork + data.initiative + data.punctuality) / 5)
    }
    return 0
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6 p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Select
          label="Apprentice"
          options={apprenticeOptions}
          value={data.apprenticeId}
          onChange={(value) => updateField('apprenticeId', value)}
          onBlur={() => touchField('apprenticeId')}
          error={touched.apprenticeId ? errors.apprenticeId : undefined}
          disabled={isLoading}
          required
        />
        
        <Select
          label="Quarter"
          options={quarterOptions}
          value={data.quarter.toString()}
          onChange={(value) => updateField('quarter', parseInt(value) as 1 | 2 | 3 | 4)}
          onBlur={() => touchField('quarter')}
          error={touched.quarter ? errors.quarter : undefined}
          disabled={isLoading}
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label="Year"
          type="number"
          value={data.year}
          onChange={(e) => updateField('year', parseInt(e.target.value))}
          onBlur={() => touchField('year')}
          error={touched.year ? errors.year : undefined}
          disabled={isLoading}
          required
        />
        
        <DatePicker
          label="Review Date"
          value={data.reviewDate}
          onChange={(value) => updateField('reviewDate', value)}
          onBlur={() => touchField('reviewDate')}
          error={touched.reviewDate ? errors.reviewDate : undefined}
          disabled={isLoading}
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Select
          label="Mentor"
          options={mentorOptions}
          value={data.mentorId}
          onChange={(value) => {
            updateField('mentorId', value)
            const mentor = mentorOptions.find(m => m.value === value)
            if (mentor) {
              updateField('mentorName', mentor.label)
            }
          }}
          onBlur={() => touchField('mentorId')}
          error={touched.mentorId ? errors.mentorId : undefined}
          disabled={isLoading}
          required
        />
        
        <Select
          label="Status"
          options={statusOptions}
          value={data.status}
          onChange={(value) => updateField('status', value as any)}
          onBlur={() => touchField('status')}
          error={touched.status ? errors.status : undefined}
          disabled={isLoading}
          required
        />
      </div>

      {/* Scores Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Performance Scores (1-5)</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Input
            label="Technical Skills"
            type="number"
            min="1"
            max="5"
            value={data.technicalSkills}
            onChange={(e) => updateField('technicalSkills', parseInt(e.target.value))}
            onBlur={() => touchField('technicalSkills')}
            error={touched.technicalSkills ? errors.technicalSkills : undefined}
            disabled={isLoading}
            required
          />
          
          <Input
            label="Communication"
            type="number"
            min="1"
            max="5"
            value={data.communication}
            onChange={(e) => updateField('communication', parseInt(e.target.value))}
            onBlur={() => touchField('communication')}
            error={touched.communication ? errors.communication : undefined}
            disabled={isLoading}
            required
          />
          
          <Input
            label="Teamwork"
            type="number"
            min="1"
            max="5"
            value={data.teamwork}
            onChange={(e) => updateField('teamwork', parseInt(e.target.value))}
            onBlur={() => touchField('teamwork')}
            error={touched.teamwork ? errors.teamwork : undefined}
            disabled={isLoading}
            required
          />
          
          <Input
            label="Initiative"
            type="number"
            min="1"
            max="5"
            value={data.initiative}
            onChange={(e) => updateField('initiative', parseInt(e.target.value))}
            onBlur={() => touchField('initiative')}
            error={touched.initiative ? errors.initiative : undefined}
            disabled={isLoading}
            required
          />
          
          <Input
            label="Punctuality"
            type="number"
            min="1"
            max="5"
            value={data.punctuality}
            onChange={(e) => updateField('punctuality', parseInt(e.target.value))}
            onBlur={() => touchField('punctuality')}
            error={touched.punctuality ? errors.punctuality : undefined}
            disabled={isLoading}
            required
          />
          
          <div className="flex items-center justify-center">
            <div className="text-center">
              <p className="text-sm text-gray-500">Overall Rating</p>
              <p className="text-2xl font-bold text-blue-600">{calculateOverallRating()}/5</p>
            </div>
          </div>
        </div>
      </div>

      {/* Feedback Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Feedback</h3>
        <div className="space-y-4">
          <Textarea
            label="Strengths (comma-separated)"
            value={data.strengths}
            onChange={(e) => updateField('strengths', e.target.value)}
            onBlur={() => touchField('strengths')}
            error={touched.strengths ? errors.strengths : undefined}
            disabled={isLoading}
            rows={3}
            placeholder="e.g., Strong technical skills, Good communication, Proactive attitude"
            required
          />
          
          <Textarea
            label="Areas for Improvement (comma-separated)"
            value={data.areasForImprovement}
            onChange={(e) => updateField('areasForImprovement', e.target.value)}
            onBlur={() => touchField('areasForImprovement')}
            error={touched.areasForImprovement ? errors.areasForImprovement : undefined}
            disabled={isLoading}
            rows={3}
            placeholder="e.g., Time management, Documentation, Code review process"
            required
          />
          
          <Textarea
            label="Goals for Next Quarter (comma-separated)"
            value={data.goals}
            onChange={(e) => updateField('goals', e.target.value)}
            onBlur={() => touchField('goals')}
            error={touched.goals ? errors.goals : undefined}
            disabled={isLoading}
            rows={3}
            placeholder="e.g., Complete advanced JavaScript course, Lead a small project, Improve test coverage"
            required
          />
        </div>
      </div>

      {/* Comments Section */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Comments</h3>
        <div className="space-y-4">
          <Textarea
            label="Mentor Comments"
            value={data.mentorComments}
            onChange={(e) => updateField('mentorComments', e.target.value)}
            onBlur={() => touchField('mentorComments')}
            error={touched.mentorComments ? errors.mentorComments : undefined}
            disabled={isLoading}
            rows={4}
            placeholder="Detailed feedback from mentor..."
            required
          />
          
          <Textarea
            label="Apprentice Comments (Optional)"
            value={data.apprenticeComments}
            onChange={(e) => updateField('apprenticeComments', e.target.value)}
            onBlur={() => touchField('apprenticeComments')}
            error={touched.apprenticeComments ? errors.apprenticeComments : undefined}
            disabled={isLoading}
            rows={4}
            placeholder="Self-reflection and feedback from apprentice..."
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-6 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={!isValid || isLoading}
        >
          {isLoading ? 'Saving...' : (review ? 'Update Review' : 'Create Review')}
        </Button>
      </div>
    </form>
  )
}