import * as React from "react"
import { cn } from "@/lib/utils"

const Skeleton = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    variant?: "default" | "shimmer" | "pulse" | "wave"
    rounded?: "none" | "sm" | "md" | "lg" | "full"
  }
>(({ className, variant = "shimmer", rounded = "md", ...props }, ref) => {
  const baseClasses = "bg-muted animate-pulse"
  const variantClasses = {
    default: "animate-pulse",
    shimmer: "loading-shimmer relative overflow-hidden",
    pulse: "loading-pulse",
    wave: "animate-bounce-in"
  }
  const roundedClasses = {
    none: "rounded-none",
    sm: "rounded-sm",
    md: "rounded-md",
    lg: "rounded-lg",
    full: "rounded-full"
  }
  
  return (
    <div
      ref={ref}
      className={cn(
        baseClasses,
        variantClasses[variant],
        roundedClasses[rounded],
        className
      )}
      {...props}
    />
  )
})
Skeleton.displayName = "Skeleton"

const SkeletonCard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "rounded-2xl border bg-card p-6 space-y-4",
      className
    )}
    {...props}
  >
    <div className="space-y-2">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-3 w-1/2" />
    </div>
    <div className="space-y-2">
      <Skeleton className="h-3 w-full" />
      <Skeleton className="h-3 w-5/6" />
      <Skeleton className="h-3 w-2/3" />
    </div>
    <div className="flex space-x-2">
      <Skeleton className="h-8 w-16" />
      <Skeleton className="h-8 w-20" />
    </div>
  </div>
))
SkeletonCard.displayName = "SkeletonCard"

const SkeletonList = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    count?: number
  }
>(({ className, count = 3, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("space-y-4", className)}
    {...props}
  >
    {Array.from({ length: count }).map((_, i) => (
      <div key={i} className="flex items-center space-x-4">
        <Skeleton className="h-10 w-10" rounded="full" />
        <div className="space-y-2 flex-1">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-3 w-3/4" />
        </div>
      </div>
    ))}
  </div>
))
SkeletonList.displayName = "SkeletonList"

const SkeletonChart = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "rounded-2xl border bg-card p-6 space-y-4",
      className
    )}
    {...props}
  >
    <div className="space-y-2">
      <Skeleton className="h-6 w-1/3" />
      <Skeleton className="h-4 w-1/2" />
    </div>
    <div className="space-y-2">
      <div className="flex items-end space-x-2 h-32">
        <Skeleton className="h-12 w-8" />
        <Skeleton className="h-20 w-8" />
        <Skeleton className="h-16 w-8" />
        <Skeleton className="h-24 w-8" />
        <Skeleton className="h-18 w-8" />
        <Skeleton className="h-28 w-8" />
      </div>
    </div>
  </div>
))
SkeletonChart.displayName = "SkeletonChart"

const SkeletonTable = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    rows?: number
    columns?: number
  }
>(({ className, rows = 5, columns = 4, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "rounded-2xl border bg-card overflow-hidden",
      className
    )}
    {...props}
  >
    <div className="p-4 border-b bg-muted/50">
      <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, i) => (
          <Skeleton key={i} className="h-4 w-full" />
        ))}
      </div>
    </div>
    <div className="p-4">
      <div className="space-y-3">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
            {Array.from({ length: columns }).map((_, colIndex) => (
              <Skeleton key={colIndex} className="h-4 w-full" />
            ))}
          </div>
        ))}
      </div>
    </div>
  </div>
))
SkeletonTable.displayName = "SkeletonTable"

export { 
  Skeleton, 
  SkeletonCard, 
  SkeletonList, 
  SkeletonChart, 
  SkeletonTable 
}