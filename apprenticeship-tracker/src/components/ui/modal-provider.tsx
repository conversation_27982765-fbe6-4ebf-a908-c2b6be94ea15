import React, { createContext, useContext, useState, useCallback } from 'react'
import { Modal } from './modal'

interface ModalConfig {
  id: string
  title?: string
  description?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  className?: string
  showCloseButton?: boolean
  closeOnOverlayClick?: boolean
  closeOnEscapeKey?: boolean
  content: React.ReactNode
}

interface ModalContextType {
  openModal: (config: Omit<ModalConfig, 'id'>) => string
  closeModal: (id: string) => void
  closeAllModals: () => void
  isModalOpen: (id: string) => boolean
}

const ModalContext = createContext<ModalContextType | undefined>(undefined)

export const useModal = () => {
  const context = useContext(ModalContext)
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider')
  }
  return context
}

interface ModalProviderProps {
  children: React.ReactNode
}

export const ModalProvider: React.FC<ModalProviderProps> = ({ children }) => {
  const [modals, setModals] = useState<Map<string, ModalConfig>>(new Map())

  const openModal = useCallback((config: Omit<ModalConfig, 'id'>) => {
    const id = `modal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const modalConfig: ModalConfig = {
      id,
      ...config,
    }
    
    setModals(prev => new Map(prev).set(id, modalConfig))
    return id
  }, [])

  const closeModal = useCallback((id: string) => {
    setModals(prev => {
      const newModals = new Map(prev)
      newModals.delete(id)
      return newModals
    })
  }, [])

  const closeAllModals = useCallback(() => {
    setModals(new Map())
  }, [])

  const isModalOpen = useCallback((id: string) => {
    return modals.has(id)
  }, [modals])

  const value: ModalContextType = {
    openModal,
    closeModal,
    closeAllModals,
    isModalOpen,
  }

  return (
    <ModalContext.Provider value={value}>
      {children}
      
      {/* Render all open modals */}
      {Array.from(modals.values()).map(modal => (
        <Modal
          key={modal.id}
          isOpen={true}
          onClose={() => closeModal(modal.id)}
          title={modal.title}
          description={modal.description}
          size={modal.size}
          className={modal.className}
          showCloseButton={modal.showCloseButton}
          closeOnOverlayClick={modal.closeOnOverlayClick}
          closeOnEscapeKey={modal.closeOnEscapeKey}
        >
          {modal.content}
        </Modal>
      ))}
    </ModalContext.Provider>
  )
}

// Convenience hook for common modal operations
export const useModalActions = () => {
  const { openModal, closeModal } = useModal()
  
  const confirmModal = useCallback((config: {
    title: string
    message: string
    onConfirm: () => void
    onCancel?: () => void
    confirmText?: string
    cancelText?: string
  }) => {
    const modalId = openModal({
      title: config.title,
      size: 'sm',
      content: (
        <div className="p-6">
          <p className="text-gray-700 mb-6">{config.message}</p>
          <div className="flex justify-end space-x-3">
            <button
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              onClick={() => {
                config.onCancel?.()
                closeModal(modalId)
              }}
            >
              {config.cancelText || 'Cancel'}
            </button>
            <button
              className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700"
              onClick={() => {
                config.onConfirm()
                closeModal(modalId)
              }}
            >
              {config.confirmText || 'Confirm'}
            </button>
          </div>
        </div>
      ),
    })
    
    return modalId
  }, [openModal, closeModal])

  return {
    openModal,
    closeModal,
    confirmModal,
  }
}