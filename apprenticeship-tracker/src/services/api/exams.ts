import { apiClient } from './client'

export interface Exam {
  id: string
  title: string
  description: string
  date: string
  duration: number
  passingScore: number
  apprenticeId: string
  status: 'scheduled' | 'completed' | 'cancelled'
  score?: number
}

export const examsApi = {
  getAll: () => apiClient.get<Exam[]>('/exams'),
  getById: (id: string) => apiClient.get<Exam>(`/exams/${id}`),
  getByApprentice: (apprenticeId: string) => apiClient.get<Exam[]>(`/exams?apprenticeId=${apprenticeId}`),
  create: (data: Omit<Exam, 'id'>) => apiClient.post<Exam>('/exams', data),
  update: (id: string, data: Partial<Exam>) => apiClient.put<Exam>(`/exams/${id}`, data),
  delete: (id: string) => apiClient.delete<void>(`/exams/${id}`),
}