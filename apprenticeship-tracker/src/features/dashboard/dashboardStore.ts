import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { DashboardStats, APIResponse, AnalyticsData } from '../../types'

interface DashboardState {
  stats: DashboardStats | null
  analytics: AnalyticsData | null
  loading: boolean
  error: string | null
}

interface DashboardActions {
  setStats: (stats: DashboardStats) => void
  setAnalytics: (analytics: AnalyticsData) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  fetchStats: () => Promise<void>
  fetchAnalytics: () => Promise<void>
  refreshStats: () => Promise<void>
  refreshAnalytics: () => Promise<void>
}

const initialState: DashboardState = {
  stats: null,
  analytics: null,
  loading: false,
  error: null
}

export const useDashboardStore = create<DashboardState & DashboardActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

      setStats: (stats) => set({ stats }),
      
      setAnalytics: (analytics) => set({ analytics }),
      
      setLoading: (loading) => set({ loading }),
      
      setError: (error) => set({ error }),

      fetchStats: async () => {
        set({ loading: true, error: null })
        try {
          const response = await fetch('/api/dashboard/stats')
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const apiResponse: APIResponse<DashboardStats> = await response.json()
          set({ stats: apiResponse.data, loading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch dashboard stats'
          console.error('Dashboard stats fetch error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      fetchAnalytics: async () => {
        set({ loading: true, error: null })
        try {
          const response = await fetch('/api/analytics/dashboard')
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const apiResponse: APIResponse<AnalyticsData> = await response.json()
          set({ analytics: apiResponse.data, loading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch analytics data'
          console.error('Dashboard analytics fetch error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      refreshStats: async () => {
        await get().fetchStats()
      },

      refreshAnalytics: async () => {
        await get().fetchAnalytics()
      }
    }),
    { name: 'dashboard-store' }
  )
)