import React, { useState } from 'react'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Select } from '../../../components/ui/select'
import { DatePicker } from '../../../components/ui/date-picker'
import { Textarea } from '../../../components/ui/textarea'
import { Checkbox } from '../../../components/ui/checkbox'
import { useFormValidation, validationRules } from '../../../lib/validation'
import { Exam } from '../../../types'

interface ExamFormProps {
  exam?: Exam
  onSubmit: (data: Partial<Exam>) => void
  onCancel: () => void
  isLoading?: boolean
}

interface ExamFormData {
  title: string
  description: string
  type: 'written' | 'practical' | 'oral' | 'project'
  subject: string
  maxScore: number
  passingScore: number
  duration: number
  scheduledDate: string
  status: 'upcoming' | 'active' | 'completed' | 'cancelled'
  instructions: string
}

const examTypeOptions = [
  { value: 'written', label: 'Written Exam' },
  { value: 'practical', label: 'Practical Assessment' },
  { value: 'oral', label: 'Oral Examination' },
  { value: 'project', label: 'Project Assessment' },
]

const statusOptions = [
  { value: 'upcoming', label: 'Upcoming' },
  { value: 'active', label: 'Active' },
  { value: 'completed', label: 'Completed' },
  { value: 'cancelled', label: 'Cancelled' },
]

const subjectOptions = [
  { value: 'JavaScript', label: 'JavaScript' },
  { value: 'React', label: 'React' },
  { value: 'Node.js', label: 'Node.js' },
  { value: 'Python', label: 'Python' },
  { value: 'Database', label: 'Database' },
  { value: 'System Design', label: 'System Design' },
  { value: 'DevOps', label: 'DevOps' },
  { value: 'Testing', label: 'Testing' },
  { value: 'Security', label: 'Security' },
  { value: 'General', label: 'General Knowledge' },
]

export const ExamForm: React.FC<ExamFormProps> = ({
  exam,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const initialData: ExamFormData = {
    title: exam?.title || '',
    description: exam?.description || '',
    type: exam?.type || 'written',
    subject: exam?.subject || '',
    maxScore: exam?.maxScore || 100,
    passingScore: exam?.passingScore || 60,
    duration: exam?.duration || 60,
    scheduledDate: exam?.scheduledDate ? exam.scheduledDate.split('T')[0] : '',
    status: exam?.status || 'upcoming',
    instructions: exam?.instructions || '',
  }

  const validationConfig = {
    title: {
      required: true,
      rules: [validationRules.minLength(3), validationRules.maxLength(200)]
    },
    description: {
      required: true,
      rules: [validationRules.minLength(10), validationRules.maxLength(1000)]
    },
    type: {
      required: true
    },
    subject: {
      required: true
    },
    maxScore: {
      required: true,
      rules: [
        validationRules.minValue(1),
        validationRules.maxValue(1000)
      ]
    },
    passingScore: {
      required: true,
      rules: [
        validationRules.minValue(1),
        validationRules.maxValue(1000)
      ]
    },
    duration: {
      required: true,
      rules: [
        validationRules.minValue(5),
        validationRules.maxValue(480) // 8 hours max
      ]
    },
    scheduledDate: {
      required: true,
      rules: [
        validationRules.custom(
          (value: string) => new Date(value) > new Date(),
          'Scheduled date must be in the future'
        )
      ]
    },
    status: {
      required: true
    },
    instructions: {
      required: false,
      rules: [validationRules.maxLength(2000)]
    }
  }

  const {
    data,
    errors,
    touched,
    updateField,
    touchField,
    validateForm,
    isValid
  } = useFormValidation(initialData, validationConfig)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (validateForm()) {
      const formattedData = {
        ...data,
        scheduledDate: new Date(data.scheduledDate).toISOString(),
        ...(exam?.id && { id: exam.id })
      }
      onSubmit(formattedData)
    }
  }

  // Validate passing score is not greater than max score
  const validateScores = () => {
    return data.passingScore <= data.maxScore
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6 p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <Input
            label="Exam Title"
            value={data.title}
            onChange={(e) => updateField('title', e.target.value)}
            onBlur={() => touchField('title')}
            error={touched.title ? errors.title : undefined}
            disabled={isLoading}
            required
            placeholder="e.g., JavaScript Fundamentals Assessment"
          />
        </div>
        
        <Select
          label="Exam Type"
          options={examTypeOptions}
          value={data.type}
          onChange={(value) => updateField('type', value as any)}
          onBlur={() => touchField('type')}
          error={touched.type ? errors.type : undefined}
          disabled={isLoading}
          required
        />
        
        <Select
          label="Subject"
          options={subjectOptions}
          value={data.subject}
          onChange={(value) => updateField('subject', value)}
          onBlur={() => touchField('subject')}
          error={touched.subject ? errors.subject : undefined}
          disabled={isLoading}
          required
        />
      </div>

      <div className="md:col-span-2">
        <Textarea
          label="Description"
          value={data.description}
          onChange={(e) => updateField('description', e.target.value)}
          onBlur={() => touchField('description')}
          error={touched.description ? errors.description : undefined}
          disabled={isLoading}
          rows={4}
          placeholder="Describe the exam content, objectives, and what will be assessed..."
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Input
          label="Maximum Score"
          type="number"
          value={data.maxScore}
          onChange={(e) => updateField('maxScore', parseInt(e.target.value))}
          onBlur={() => touchField('maxScore')}
          error={touched.maxScore ? errors.maxScore : undefined}
          disabled={isLoading}
          required
          min="1"
        />
        
        <Input
          label="Passing Score"
          type="number"
          value={data.passingScore}
          onChange={(e) => updateField('passingScore', parseInt(e.target.value))}
          onBlur={() => touchField('passingScore')}
          error={touched.passingScore ? errors.passingScore : (!validateScores() ? 'Passing score cannot exceed maximum score' : undefined)}
          disabled={isLoading}
          required
          min="1"
        />
        
        <Input
          label="Duration (minutes)"
          type="number"
          value={data.duration}
          onChange={(e) => updateField('duration', parseInt(e.target.value))}
          onBlur={() => touchField('duration')}
          error={touched.duration ? errors.duration : undefined}
          disabled={isLoading}
          required
          min="5"
          helperText="Duration in minutes"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <DatePicker
          label="Scheduled Date"
          format="datetime-local"
          value={data.scheduledDate}
          onChange={(value) => updateField('scheduledDate', value)}
          onBlur={() => touchField('scheduledDate')}
          error={touched.scheduledDate ? errors.scheduledDate : undefined}
          disabled={isLoading}
          required
        />
        
        <Select
          label="Status"
          options={statusOptions}
          value={data.status}
          onChange={(value) => updateField('status', value as any)}
          onBlur={() => touchField('status')}
          error={touched.status ? errors.status : undefined}
          disabled={isLoading}
          required
        />
      </div>

      <div>
        <Textarea
          label="Instructions (Optional)"
          value={data.instructions}
          onChange={(e) => updateField('instructions', e.target.value)}
          onBlur={() => touchField('instructions')}
          error={touched.instructions ? errors.instructions : undefined}
          disabled={isLoading}
          rows={6}
          placeholder="Provide detailed instructions for the exam, including what materials are allowed, format, etc."
        />
      </div>

      {/* Exam Summary */}
      <div className="bg-gray-50 rounded-lg p-4 space-y-2">
        <h3 className="font-semibold text-gray-900">Exam Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Type:</span> {data.type ? examTypeOptions.find(opt => opt.value === data.type)?.label : 'Not selected'}
          </div>
          <div>
            <span className="text-gray-500">Subject:</span> {data.subject || 'Not selected'}
          </div>
          <div>
            <span className="text-gray-500">Score Range:</span> {data.passingScore}/{data.maxScore} ({data.maxScore > 0 ? Math.round((data.passingScore / data.maxScore) * 100) : 0}% to pass)
          </div>
          <div>
            <span className="text-gray-500">Duration:</span> {data.duration} minutes ({Math.floor(data.duration / 60)}h {data.duration % 60}m)
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-6 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={!isValid || !validateScores() || isLoading}
        >
          {isLoading ? 'Saving...' : (exam ? 'Update Exam' : 'Create Exam')}
        </Button>
      </div>
    </form>
  )
}