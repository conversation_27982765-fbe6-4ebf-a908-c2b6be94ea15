{"version": 3, "file": "rspack.cjs", "sources": ["../../src/rspack.ts"], "sourcesContent": ["import { createRspackPlugin } from 'unplugin'\n\nimport { configSchema } from './core/config'\nimport { unpluginRouterCodeSplitterFactory } from './core/router-code-splitter-plugin'\nimport { unpluginRouterGeneratorFactory } from './core/router-generator-plugin'\nimport { unpluginRouterComposedFactory } from './core/router-composed-plugin'\nimport type { Config } from './core/config'\n\n/**\n * @example\n * ```ts\n * export default defineConfig({\n *   // ...\n *   tools: {\n *     rspack: {\n *       plugins: [TanStackRouterGeneratorRspack()],\n *     },\n *   },\n * })\n * ```\n */\nconst TanStackRouterGeneratorRspack = /* #__PURE__ */ createRspackPlugin(\n  unpluginRouterGeneratorFactory,\n)\n\n/**\n * @example\n * ```ts\n * export default defineConfig({\n *   // ...\n *   tools: {\n *     rspack: {\n *       plugins: [TanStackRouterCodeSplitterRspack()],\n *     },\n *   },\n * })\n * ```\n */\nconst TanStackRouterCodeSplitterRspack = /* #__PURE__ */ createRspackPlugin(\n  unpluginRouterCodeSplitterFactory,\n)\n\n/**\n * @example\n * ```ts\n * export default defineConfig({\n *   // ...\n *   tools: {\n *     rspack: {\n *       plugins: [tanstackRouter()],\n *     },\n *   },\n * })\n * ```\n */\nconst TanStackRouterRspack = /* #__PURE__ */ createRspackPlugin(\n  unpluginRouterComposedFactory,\n)\nconst tanstackRouter = TanStackRouterRspack\nexport default TanStackRouterRspack\nexport {\n  configSchema,\n  TanStackRouterRspack,\n  TanStackRouterGeneratorRspack,\n  TanStackRouterCodeSplitterRspack,\n  tanstackRouter,\n}\nexport type { Config }\n"], "names": ["createRspackPlugin", "unpluginRouterGeneratorFactory", "unpluginRouterCodeSplitterFactory", "unpluginRouterComposedFactory"], "mappings": ";;;;;;;AAqBA,MAAM,gCAAgDA,yBAAA;AAAA,EACpDC,sBAAAA;AACF;AAeA,MAAM,mCAAmDD,yBAAA;AAAA,EACvDE,yBAAAA;AACF;AAeA,MAAM,uBAAuCF,yBAAA;AAAA,EAC3CG,qBAAAA;AACF;AACA,MAAM,iBAAiB;;;;;;;"}