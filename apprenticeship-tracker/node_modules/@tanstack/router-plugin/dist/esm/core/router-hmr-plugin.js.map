{"version": 3, "file": "router-hmr-plugin.js", "sources": ["../../../src/core/router-hmr-plugin.ts"], "sourcesContent": ["import { generateFromAst, logDiff, parseAst } from '@tanstack/router-utils'\nimport { routeHmrStatement } from './route-hmr-statement'\nimport { debug } from './utils'\nimport { getConfig } from './config'\nimport type { UnpluginFactory } from 'unplugin'\nimport type { Config } from './config'\n\n/**\n * This plugin adds HMR support for file routes.\n * It is only added to the composed plugin in dev when autoCodeSplitting is disabled, since the code splitting plugin\n * handles HMR for code-split routes itself.\n */\n\nconst includeCode = [\n  'createFileRoute(',\n  'createRootRoute(',\n  'createRootRouteWithContext(',\n]\nexport const unpluginRouterHmrFactory: UnpluginFactory<\n  Partial<Config> | undefined\n> = (options = {}) => {\n  let ROOT: string = process.cwd()\n  let userConfig = options as Config\n\n  return {\n    name: 'tanstack-router:hmr',\n    enforce: 'pre',\n    transform: {\n      filter: {\n        // this is necessary for webpack / rspack to avoid matching .html files\n        id: /\\.(m|c)?(j|t)sx?$/,\n        code: {\n          include: includeCode,\n        },\n      },\n      handler(code, id) {\n        if (!globalThis.TSR_ROUTES_BY_ID_MAP?.has(id)) {\n          return null\n        }\n\n        if (debug) console.info('Adding HMR handling to route ', id)\n\n        const ast = parseAst({ code })\n        ast.program.body.push(routeHmrStatement)\n        const result = generateFromAst(ast, {\n          sourceMaps: true,\n          filename: id,\n          sourceFileName: id,\n        })\n        if (debug) {\n          logDiff(code, result.code)\n          console.log('Output:\\n', result.code + '\\n\\n')\n        }\n        return result\n      },\n    },\n    vite: {\n      configResolved(config) {\n        ROOT = config.root\n        userConfig = getConfig(options, ROOT)\n      },\n      applyToEnvironment(environment) {\n        if (userConfig.plugin?.vite?.environmentName) {\n          return userConfig.plugin.vite.environmentName === environment.name\n        }\n        return true\n      },\n    },\n  }\n}\n"], "names": [], "mappings": ";;;;AAaA,MAAM,cAAc;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AACF;AACO,MAAM,2BAET,CAAC,UAAU,OAAO;AAChB,MAAA,OAAe,QAAQ,IAAI;AAC/B,MAAI,aAAa;AAEV,SAAA;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,IACT,WAAW;AAAA,MACT,QAAQ;AAAA;AAAA,QAEN,IAAI;AAAA,QACJ,MAAM;AAAA,UACJ,SAAS;AAAA,QAAA;AAAA,MAEb;AAAA,MACA,QAAQ,MAAM,IAAI;;AAChB,YAAI,GAAC,gBAAW,yBAAX,mBAAiC,IAAI,MAAK;AACtC,iBAAA;AAAA,QAAA;AAGT,YAAI,MAAO,SAAQ,KAAK,iCAAiC,EAAE;AAE3D,cAAM,MAAM,SAAS,EAAE,MAAM;AACzB,YAAA,QAAQ,KAAK,KAAK,iBAAiB;AACjC,cAAA,SAAS,gBAAgB,KAAK;AAAA,UAClC,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,gBAAgB;AAAA,QAAA,CACjB;AACD,YAAI,OAAO;AACD,kBAAA,MAAM,OAAO,IAAI;AACzB,kBAAQ,IAAI,aAAa,OAAO,OAAO,MAAM;AAAA,QAAA;AAExC,eAAA;AAAA,MAAA;AAAA,IAEX;AAAA,IACA,MAAM;AAAA,MACJ,eAAe,QAAQ;AACrB,eAAO,OAAO;AACD,qBAAA,UAAU,SAAS,IAAI;AAAA,MACtC;AAAA,MACA,mBAAmB,aAAa;;AAC1B,aAAA,sBAAW,WAAX,mBAAmB,SAAnB,mBAAyB,iBAAiB;AAC5C,iBAAO,WAAW,OAAO,KAAK,oBAAoB,YAAY;AAAA,QAAA;AAEzD,eAAA;AAAA,MAAA;AAAA,IACT;AAAA,EAEJ;AACF;"}