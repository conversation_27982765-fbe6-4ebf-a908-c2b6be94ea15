import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { useReviewStore } from '../reviewStore'
import { useModal } from '../../../components/ui/modal-provider'
import { ReviewForm } from './ReviewForm'
import { ReviewDetailsModal } from './ReviewDetailsModal'

export function Reviews() {
  const { reviews, loading, error, fetchReviews, createReview } = useReviewStore()
  const { openModal } = useModal()
  const [isCreating, setIsCreating] = useState(false)

  useEffect(() => {
    fetchReviews()
  }, [fetchReviews])

  const handleScheduleReview = () => {
    openModal({
      title: 'Schedule New Review',
      size: 'xl',
      content: (
        <ReviewForm
          onSubmit={async (data) => {
            await createReview(data)
            await fetchReviews()
          }}
          onCancel={() => {}}
        />
      )
    })
  }

  const handleViewFullReview = (reviewId: string) => {
    const review = reviews.find(r => r.id === reviewId)
    if (review) {
      openModal({
        title: 'Review Details',
        size: 'xl',
        content: (
          <ReviewDetailsModal
            review={review}
            onClose={() => {}}
          />
        )
      })
    }
  }

  if (loading && !isCreating) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading reviews...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-red-600">Error: {error}</div>
      </div>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in-progress':
        return 'bg-blue-100 text-blue-800'
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={`text-sm ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}>
        ★
      </span>
    ))
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between px-4 sm:px-0">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Quarterly Reviews</h1>
          <p className="mt-2 text-sm text-gray-700">
            Track and manage apprentice performance reviews.
          </p>
        </div>
        <Button onClick={handleScheduleReview}>
          Schedule Review
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {reviews.map((review) => (
          <Card key={review.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">
                  Q{review.quarter} {review.year} Review
                </CardTitle>
                <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getStatusColor(review.status)}`}>
                  {review.status}
                </span>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-gray-900">Apprentice</p>
                  <p className="text-sm text-gray-600">Apprentice ID: {review.apprenticeId}</p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-gray-900">Mentor</p>
                  <p className="text-sm text-gray-600">{review.mentorName}</p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-900">Overall Rating</p>
                  <div className="flex items-center space-x-2">
                    <div className="flex">
                      {renderStars(Math.round(review.scores.overallRating))}
                    </div>
                    <span className="text-sm text-gray-600">{review.scores.overallRating}/10</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <p className="text-gray-500">Technical Skills</p>
                    <p className="font-medium">{review.scores.technicalSkills}/10</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Communication</p>
                    <p className="font-medium">{review.scores.communication}/10</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Teamwork</p>
                    <p className="font-medium">{review.scores.teamwork}/10</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Initiative</p>
                    <p className="font-medium">{review.scores.initiative}/10</p>
                  </div>
                </div>

                {review.strengths.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-gray-900 mb-1">Key Strengths</p>
                    <div className="flex flex-wrap gap-1">
                      {review.strengths.slice(0, 2).map((strength, index) => (
                        <span key={index} className="inline-flex rounded bg-green-100 px-2 py-1 text-xs text-green-800">
                          {strength}
                        </span>
                      ))}
                      {review.strengths.length > 2 && (
                        <span className="inline-flex rounded bg-gray-100 px-2 py-1 text-xs text-gray-600">
                          +{review.strengths.length - 2} more
                        </span>
                      )}
                    </div>
                  </div>
                )}

                <div className="pt-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full"
                    onClick={() => handleViewFullReview(review.id)}
                  >
                    View Full Review
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {reviews.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No reviews found.</p>
          <Button className="mt-4" onClick={handleScheduleReview}>
            Schedule First Review
          </Button>
        </div>
      )}
    </div>
  )
}