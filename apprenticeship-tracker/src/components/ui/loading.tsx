import * as React from "react"
import { cn } from "@/lib/utils"

const Spinner = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    size?: "sm" | "md" | "lg" | "xl"
    variant?: "default" | "dots" | "pulse" | "bars"
  }
>(({ className, size = "md", variant = "default", ...props }, ref) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
    xl: "w-12 h-12"
  }
  
  const variants = {
    default: (
      <div className={cn("animate-spin rounded-full border-2 border-primary border-t-transparent", sizeClasses[size])} />
    ),
    dots: (
      <div className="flex space-x-1">
        {[0, 1, 2].map(i => (
          <div
            key={i}
            className={cn(
              "rounded-full bg-primary animate-bounce",
              size === "sm" ? "w-1.5 h-1.5" : size === "md" ? "w-2 h-2" : size === "lg" ? "w-3 h-3" : "w-4 h-4"
            )}
            style={{ animationDelay: `${i * 0.1}s` }}
          />
        ))}
      </div>
    ),
    pulse: (
      <div className={cn("animate-pulse rounded-full bg-primary", sizeClasses[size])} />
    ),
    bars: (
      <div className="flex space-x-1">
        {[0, 1, 2, 3].map(i => (
          <div
            key={i}
            className={cn(
              "bg-primary animate-pulse",
              size === "sm" ? "w-1 h-3" : size === "md" ? "w-1.5 h-4" : size === "lg" ? "w-2 h-6" : "w-3 h-8"
            )}
            style={{ animationDelay: `${i * 0.1}s` }}
          />
        ))}
      </div>
    )
  }
  
  return (
    <div
      ref={ref}
      className={cn("flex items-center justify-center", className)}
      {...props}
    >
      {variants[variant]}
    </div>
  )
})
Spinner.displayName = "Spinner"

const LoadingScreen = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    message?: string
    variant?: "default" | "glass" | "minimal"
  }
>(({ className, message = "Loading...", variant = "default", ...props }, ref) => {
  const variantClasses = {
    default: "bg-background/80 backdrop-blur-sm",
    glass: "glass-subtle",
    minimal: "bg-transparent"
  }
  
  return (
    <div
      ref={ref}
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center",
        variantClasses[variant],
        className
      )}
      {...props}
    >
      <div className="text-center space-y-4">
        <Spinner size="lg" />
        <p className="text-muted-foreground font-medium">{message}</p>
      </div>
    </div>
  )
})
LoadingScreen.displayName = "LoadingScreen"

const LoadingButton = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    loading?: boolean
    loadingText?: string
    variant?: "default" | "glass" | "gradient"
  }
>(({ 
  className, 
  loading = false, 
  loadingText = "Loading...", 
  variant = "default",
  children, 
  disabled,
  ...props 
}, ref) => {
  const variantClasses = {
    default: "bg-primary text-primary-foreground hover:bg-primary/90",
    glass: "btn-glass",
    gradient: "btn-gradient"
  }
  
  return (
    <button
      ref={ref}
      className={cn(
        "inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",
        variantClasses[variant],
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && <Spinner size="sm" className="mr-2" />}
      {loading ? loadingText : children}
    </button>
  )
})
LoadingButton.displayName = "LoadingButton"

const LoadingCard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    title?: string
    description?: string
  }
>(({ className, title = "Loading", description = "Please wait...", ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "card-modern flex items-center space-x-4 p-6",
      className
    )}
    {...props}
  >
    <Spinner size="lg" />
    <div>
      <h3 className="font-medium">{title}</h3>
      <p className="text-sm text-muted-foreground">{description}</p>
    </div>
  </div>
))
LoadingCard.displayName = "LoadingCard"

const LoadingDots = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    size?: "sm" | "md" | "lg"
  }
>(({ className, size = "md", ...props }, ref) => {
  const sizeClasses = {
    sm: "w-1 h-1",
    md: "w-2 h-2",
    lg: "w-3 h-3"
  }
  
  return (
    <div
      ref={ref}
      className={cn("flex space-x-1", className)}
      {...props}
    >
      {[0, 1, 2].map(i => (
        <div
          key={i}
          className={cn(
            "rounded-full bg-current animate-bounce",
            sizeClasses[size]
          )}
          style={{ animationDelay: `${i * 0.1}s` }}
        />
      ))}
    </div>
  )
})
LoadingDots.displayName = "LoadingDots"

export { 
  Spinner, 
  LoadingScreen, 
  LoadingButton, 
  LoadingCard, 
  LoadingDots 
}