export { configSchema, getConfig } from './core/config.cjs';
export { unpluginRouterCodeSplitterFactory } from './core/router-code-splitter-plugin.cjs';
export { unpluginRouterGeneratorFactory } from './core/router-generator-plugin.cjs';
export type { Config, ConfigInput, ConfigOutput } from './core/config.cjs';
export { tsrSplit, splitRouteIdentNodes, defaultCodeSplitGroupings, } from './core/constants.cjs';
