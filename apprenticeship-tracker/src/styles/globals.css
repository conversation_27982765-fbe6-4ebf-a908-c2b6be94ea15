@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@plugin 'tailwindcss-animate';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --color-background: var(--background);
  --color-foreground: var(--foreground);

  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);

  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);

  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);

  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);

  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);

  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);

  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);

  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
}

:root {
  --radius: 0.75rem;
  
  /* Enhanced color palette */
  --background: oklch(0.99 0.001 106.423);
  --foreground: oklch(0.15 0.008 285.823);
  --card: oklch(0.99 0.001 106.423);
  --card-foreground: oklch(0.15 0.008 285.823);
  --popover: oklch(0.99 0.001 106.423);
  --popover-foreground: oklch(0.15 0.008 285.823);
  
  /* Modern gradient primary */
  --primary: oklch(0.65 0.25 295.5);
  --primary-foreground: oklch(0.98 0.01 295.5);
  
  /* Cool blue secondary */
  --secondary: oklch(0.96 0.005 250);
  --secondary-foreground: oklch(0.25 0.01 250);
  
  /* Soft muted tones */
  --muted: oklch(0.96 0.005 250);
  --muted-foreground: oklch(0.5 0.01 250);
  
  /* Vibrant accent */
  --accent: oklch(0.75 0.2 60);
  --accent-foreground: oklch(0.98 0.01 60);
  
  /* Modern destructive */
  --destructive: oklch(0.65 0.25 15);
  --destructive-foreground: oklch(0.98 0.01 15);
  
  /* Subtle borders */
  --border: oklch(0.92 0.005 250);
  --input: oklch(0.96 0.005 250);
  --ring: oklch(0.65 0.25 295.5);
  
  /* Enhanced chart colors */
  --chart-1: oklch(0.65 0.25 295.5);
  --chart-2: oklch(0.6 0.2 200);
  --chart-3: oklch(0.7 0.25 120);
  --chart-4: oklch(0.75 0.2 60);
  --chart-5: oklch(0.55 0.25 30);
  
  /* Sidebar colors */
  --sidebar: oklch(0.98 0.005 250);
  --sidebar-foreground: oklch(0.15 0.008 285.823);
  --sidebar-primary: oklch(0.65 0.25 295.5);
  --sidebar-primary-foreground: oklch(0.98 0.01 295.5);
  --sidebar-accent: oklch(0.96 0.005 250);
  --sidebar-accent-foreground: oklch(0.25 0.01 250);
  --sidebar-border: oklch(0.92 0.005 250);
  --sidebar-ring: oklch(0.65 0.25 295.5);
  
  /* Glassmorphism variables */
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  
  /* Animation variables */
  --ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
}

.dark {
  --background: oklch(0.1 0.005 285.823);
  --foreground: oklch(0.98 0.001 106.423);
  --card: oklch(0.15 0.008 285.823);
  --card-foreground: oklch(0.98 0.001 106.423);
  --popover: oklch(0.15 0.008 285.823);
  --popover-foreground: oklch(0.98 0.001 106.423);
  
  --primary: oklch(0.7 0.25 295.5);
  --primary-foreground: oklch(0.1 0.005 295.5);
  
  --secondary: oklch(0.2 0.01 250);
  --secondary-foreground: oklch(0.98 0.001 250);
  
  --muted: oklch(0.2 0.01 250);
  --muted-foreground: oklch(0.7 0.01 250);
  
  --accent: oklch(0.8 0.2 60);
  --accent-foreground: oklch(0.1 0.005 60);
  
  --destructive: oklch(0.7 0.25 15);
  --destructive-foreground: oklch(0.98 0.01 15);
  
  --border: oklch(0.25 0.01 250);
  --input: oklch(0.2 0.01 250);
  --ring: oklch(0.7 0.25 295.5);
  
  --chart-1: oklch(0.7 0.25 295.5);
  --chart-2: oklch(0.65 0.2 200);
  --chart-3: oklch(0.75 0.25 120);
  --chart-4: oklch(0.8 0.2 60);
  --chart-5: oklch(0.6 0.25 30);
  
  --sidebar: oklch(0.15 0.008 285.823);
  --sidebar-foreground: oklch(0.98 0.001 106.423);
  --sidebar-primary: oklch(0.7 0.25 295.5);
  --sidebar-primary-foreground: oklch(0.1 0.005 295.5);
  --sidebar-accent: oklch(0.2 0.01 250);
  --sidebar-accent-foreground: oklch(0.98 0.001 250);
  --sidebar-border: oklch(0.25 0.01 250);
  --sidebar-ring: oklch(0.7 0.25 295.5);
  
  --glass-bg: rgba(255, 255, 255, 0.02);
  --glass-border: rgba(255, 255, 255, 0.05);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
}

@layer base {
  * {
    @apply border-border;
  }
  
  html {
    @apply antialiased;
  }
  
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }
  
  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
  
  /* Selection styles */
  ::selection {
    @apply bg-primary/20 text-primary;
  }
  
  /* Focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }
}

@layer components {
  /* Glassmorphism utility classes */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }
  
  .glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    @apply rounded-2xl;
  }
  
  .glass-subtle {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  /* Modern gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);
  }
  
  .gradient-secondary {
    background: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--secondary)) 100%);
  }
  
  .gradient-cosmic {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .gradient-sunset {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }
  
  .gradient-ocean {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }
  
  .gradient-forest {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }
  
  .gradient-aurora {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  }
  
  /* Neumorphism styles */
  .neomorphism {
    background: var(--background);
    box-shadow: 
      8px 8px 16px rgba(0, 0, 0, 0.1),
      -8px -8px 16px rgba(255, 255, 255, 0.8);
  }
  
  .neomorphism-inset {
    background: var(--background);
    box-shadow: 
      inset 8px 8px 16px rgba(0, 0, 0, 0.1),
      inset -8px -8px 16px rgba(255, 255, 255, 0.8);
  }
  
  /* Modern shadows */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(var(--primary), 0.3);
  }
  
  .shadow-float {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
  
  .shadow-intense {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }
  
  /* Modern borders */
  .border-gradient {
    border: 1px solid transparent;
    background: linear-gradient(hsl(var(--background)), hsl(var(--background))) padding-box,
                linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary))) border-box;
  }
  
  /* Loading animations */
  .loading-shimmer {
    background: linear-gradient(90deg, 
      transparent 0%, 
      rgba(255, 255, 255, 0.4) 50%, 
      transparent 100%
    );
    animation: shimmer 2s infinite;
  }
  
  .loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  /* Scroll animations */
  .scroll-reveal {
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--duration-slow) var(--ease-smooth);
  }
  
  .scroll-reveal.visible {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Hover effects */
  .hover-lift {
    transition: all var(--duration-normal) var(--ease-smooth);
  }
  
  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  }
  
  .hover-scale {
    transition: all var(--duration-normal) var(--ease-smooth);
  }
  
  .hover-scale:hover {
    transform: scale(1.02);
  }
  
  /* Button variants */
  .btn-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    @apply px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200;
  }
  
  .btn-glass:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-1px);
  }
  
  .btn-gradient {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);
    @apply text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200;
  }
  
  .btn-gradient:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 30px rgba(var(--primary), 0.3);
  }
  
  /* Card variants */
  .card-modern {
    @apply bg-card border border-border rounded-2xl p-6 shadow-md hover:shadow-lg transition-all duration-300;
  }
  
  .card-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    @apply rounded-2xl p-6 transition-all duration-300;
  }
  
  .card-glass:hover {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .card-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    @apply text-white p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300;
  }
  
  /* Text effects */
  .text-gradient {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-glow {
    text-shadow: 0 0 10px currentColor;
  }
  
  /* Modern form elements */
  .input-modern {
    @apply bg-input border border-border rounded-xl px-4 py-3 text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200;
  }
  
  .input-glass {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    @apply px-4 py-3 rounded-xl text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring transition-all duration-200;
  }
  
  /* Layout utilities */
  .container-modern {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-12 sm:py-16 lg:py-20;
  }
  
  /* Responsive grid */
  .grid-auto-fit {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }
  
  .grid-auto-fill {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
}

@layer utilities {
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn var(--duration-slow) var(--ease-smooth) forwards;
  }
  
  .animate-slide-up {
    animation: slideUp var(--duration-slow) var(--ease-smooth) forwards;
  }
  
  .animate-slide-down {
    animation: slideDown var(--duration-slow) var(--ease-smooth) forwards;
  }
  
  .animate-scale-in {
    animation: scaleIn var(--duration-slow) var(--ease-spring) forwards;
  }
  
  .animate-bounce-in {
    animation: bounceIn var(--duration-slow) var(--ease-spring) forwards;
  }
  
  /* Backdrop blur utilities */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
  }
  
  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }
  
  .backdrop-blur-md {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
  
  .backdrop-blur-lg {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }
  
  .backdrop-blur-xl {
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
  }
  
  /* Transition utilities */
  .transition-smooth {
    transition: all var(--duration-normal) var(--ease-smooth);
  }
  
  .transition-spring {
    transition: all var(--duration-normal) var(--ease-spring);
  }
  
  .transition-fast {
    transition: all var(--duration-fast) var(--ease-smooth);
  }
  
  .transition-slow {
    transition: all var(--duration-slow) var(--ease-smooth);
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}

/* Dark mode specific animations */
.dark .loading-shimmer {
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.1) 50%, 
    transparent 100%
  );
}

/* Responsive breakpoints for animations */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass,
  .glass-card,
  .glass-subtle {
    background: var(--card);
    border: 2px solid var(--border);
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
}

/* Print styles */
@media print {
  .glass,
  .glass-card,
  .glass-subtle {
    background: white;
    border: 1px solid #ccc;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
  
  .gradient-primary,
  .gradient-secondary,
  .gradient-cosmic,
  .gradient-sunset,
  .gradient-ocean,
  .gradient-forest,
  .gradient-aurora {
    background: white;
    color: black;
  }
}
