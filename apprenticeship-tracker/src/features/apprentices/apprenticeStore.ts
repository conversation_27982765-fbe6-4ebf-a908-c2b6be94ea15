import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { Apprentice, PaginationInfo, FilterOptions, SearchFilters, SortOptions, APIResponse } from '../../types'

interface ApprenticeState {
  apprentices: Apprentice[]
  selectedApprentice: Apprentice | null
  loading: boolean
  error: string | null
  pagination: PaginationInfo
  filters: SearchFilters
  sort: SortOptions
}

interface ApprenticeActions {
  setApprentices: (apprentices: Apprentice[]) => void
  setSelectedApprentice: (apprentice: Apprentice | null) => void
  addApprentice: (apprentice: Apprentice) => void
  updateApprentice: (id: string, updates: Partial<Apprentice>) => void
  removeApprentice: (id: string) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setPagination: (pagination: PaginationInfo) => void
  setFilters: (filters: SearchFilters) => void
  setSort: (sort: SortOptions) => void
  resetFilters: () => void
  fetchApprentices: (page?: number, filters?: SearchFilters, sort?: SortOptions) => Promise<void>
  fetchApprenticeById: (id: string) => Promise<void>
  createApprentice: (apprentice: Omit<Apprentice, 'id' | 'overallProgress'>) => Promise<void>
  updateApprenticeData: (id: string, updates: Partial<Apprentice>) => Promise<void>
  deleteApprentice: (id: string) => Promise<void>
  searchApprentices: (searchTerm: string, page?: number) => Promise<void>
}

const initialState: ApprenticeState = {
  apprentices: [],
  selectedApprentice: null,
  loading: false,
  error: null,
  pagination: {
    page: 1,
    pageSize: 10,
    totalItems: 0,
    totalPages: 0
  },
  filters: {},
  sort: {
    field: 'firstName',
    direction: 'asc'
  }
}

export const useApprenticeStore = create<ApprenticeState & ApprenticeActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

      setApprentices: (apprentices) => set({ apprentices }),
      
      setSelectedApprentice: (apprentice) => set({ selectedApprentice: apprentice }),
      
      addApprentice: (apprentice) => 
        set((state) => ({ 
          apprentices: [...state.apprentices, apprentice] 
        })),
      
      updateApprentice: (id, updates) =>
        set((state) => ({
          apprentices: state.apprentices.map(a => 
            a.id === id ? { ...a, ...updates } : a
          ),
          selectedApprentice: state.selectedApprentice?.id === id 
            ? { ...state.selectedApprentice, ...updates } 
            : state.selectedApprentice
        })),
      
      removeApprentice: (id) =>
        set((state) => ({
          apprentices: state.apprentices.filter(a => a.id !== id),
          selectedApprentice: state.selectedApprentice?.id === id ? null : state.selectedApprentice
        })),
      
      setLoading: (loading) => set({ loading }),
      
      setError: (error) => set({ error }),
      
      setPagination: (pagination) => set({ pagination }),
      
      setFilters: (filters) => set({ filters }),
      
      setSort: (sort) => set({ sort }),
      
      resetFilters: () => set({ filters: {} }),

      fetchApprentices: async (page = 1, filters = {}, sort) => {
        set({ loading: true, error: null })
        try {
          const { pagination: currentPagination, sort: currentSort } = get()
          const sortToUse = sort || currentSort
          
          const params = new URLSearchParams({
            page: page.toString(),
            pageSize: currentPagination.pageSize.toString(),
            sortField: sortToUse.field,
            sortDirection: sortToUse.direction,
            ...Object.entries(filters).reduce((acc, [key, value]) => {
              if (value !== undefined && value !== null) {
                if (Array.isArray(value)) {
                  acc[key] = value.join(',')
                } else if (typeof value === 'object' && value !== null) {
                  // Handle nested objects like progress range
                  Object.entries(value).forEach(([nestedKey, nestedValue]) => {
                    if (nestedValue !== undefined && nestedValue !== null) {
                      acc[`${key}.${nestedKey}`] = nestedValue.toString()
                    }
                  })
                } else {
                  acc[key] = value.toString()
                }
              }
              return acc
            }, {} as Record<string, string>)
          })

          const response = await fetch(`/api/apprentices?${params}`)
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<Apprentice[]> = await response.json()
          set({ 
            apprentices: result.data, 
            pagination: result.pagination!,
            filters,
            sort: sortToUse,
            loading: false 
          })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch apprentices'
          console.error('Apprentices fetch error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      searchApprentices: async (searchTerm, page = 1) => {
        const filters = { ...get().filters, searchTerm }
        await get().fetchApprentices(page, filters)
      },

      fetchApprenticeById: async (id) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch(`/api/apprentices/${id}`)
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<Apprentice> = await response.json()
          set({ selectedApprentice: result.data, loading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch apprentice'
          console.error('Apprentice fetch error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      createApprentice: async (apprenticeData) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch('/api/apprentices', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(apprenticeData)
          })
          
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<Apprentice> = await response.json()
          set((state) => ({ 
            apprentices: [...state.apprentices, result.data],
            loading: false 
          }))
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to create apprentice'
          console.error('Apprentice create error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      updateApprenticeData: async (id, updates) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch(`/api/apprentices/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(updates)
          })
          
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<Apprentice> = await response.json()
          get().updateApprentice(id, result.data)
          set({ loading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to update apprentice'
          console.error('Apprentice update error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      deleteApprentice: async (id) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch(`/api/apprentices/${id}`, {
            method: 'DELETE'
          })
          
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          get().removeApprentice(id)
          set({ loading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to delete apprentice'
          console.error('Apprentice delete error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      }
    }),
    { name: 'apprentice-store' }
  )
)