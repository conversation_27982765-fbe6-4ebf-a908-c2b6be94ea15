import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { QuarterlyReview, PaginationInfo, SearchFilters, SortOptions, APIResponse } from '../../types'

interface ReviewState {
  reviews: QuarterlyReview[]
  selectedReview: QuarterlyReview | null
  loading: boolean
  error: string | null
  pagination: PaginationInfo
  filters: SearchFilters
  sort: SortOptions
}

interface ReviewActions {
  setReviews: (reviews: QuarterlyReview[]) => void
  setSelectedReview: (review: QuarterlyReview | null) => void
  addReview: (review: QuarterlyReview) => void
  updateReview: (id: string, updates: Partial<QuarterlyReview>) => void
  removeReview: (id: string) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setPagination: (pagination: PaginationInfo) => void
  setFilters: (filters: SearchFilters) => void
  setSort: (sort: SortOptions) => void
  resetFilters: () => void
  fetchReviews: (page?: number, apprenticeId?: string, filters?: SearchFilters, sort?: SortOptions) => Promise<void>
  fetchReviewById: (id: string) => Promise<void>
  createReview: (review: Omit<QuarterlyReview, 'id'>) => Promise<void>
  updateReviewData: (id: string, updates: Partial<QuarterlyReview>) => Promise<void>
  deleteReview: (id: string) => Promise<void>
  getReviewsByApprentice: (apprenticeId: string) => QuarterlyReview[]
  searchReviews: (searchTerm: string, page?: number) => Promise<void>
}

const initialState: ReviewState = {
  reviews: [],
  selectedReview: null,
  loading: false,
  error: null,
  pagination: {
    page: 1,
    pageSize: 10,
    totalItems: 0,
    totalPages: 0
  },
  filters: {},
  sort: {
    field: 'reviewDate',
    direction: 'desc'
  }
}

export const useReviewStore = create<ReviewState & ReviewActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

      setReviews: (reviews) => set({ reviews }),
      
      setSelectedReview: (review) => set({ selectedReview: review }),
      
      addReview: (review) => 
        set((state) => ({ 
          reviews: [...state.reviews, review] 
        })),
      
      updateReview: (id, updates) =>
        set((state) => ({
          reviews: state.reviews.map(r => 
            r.id === id ? { ...r, ...updates } : r
          ),
          selectedReview: state.selectedReview?.id === id 
            ? { ...state.selectedReview, ...updates } 
            : state.selectedReview
        })),
      
      removeReview: (id) =>
        set((state) => ({
          reviews: state.reviews.filter(r => r.id !== id),
          selectedReview: state.selectedReview?.id === id ? null : state.selectedReview
        })),
      
      setLoading: (loading) => set({ loading }),
      
      setError: (error) => set({ error }),
      
      setPagination: (pagination) => set({ pagination }),
      
      setFilters: (filters) => set({ filters }),
      
      setSort: (sort) => set({ sort }),
      
      resetFilters: () => set({ filters: {} }),

      fetchReviews: async (page = 1, apprenticeId, filters = {}, sort) => {
        set({ loading: true, error: null })
        try {
          const { pagination: currentPagination, sort: currentSort } = get()
          const sortToUse = sort || currentSort
          
          const params = new URLSearchParams({
            page: page.toString(),
            pageSize: currentPagination.pageSize.toString(),
            sortField: sortToUse.field,
            sortDirection: sortToUse.direction,
            ...Object.entries(filters).reduce((acc, [key, value]) => {
              if (value !== undefined && value !== null) {
                if (Array.isArray(value)) {
                  acc[key] = value.join(',')
                } else if (typeof value === 'object' && value !== null) {
                  Object.entries(value).forEach(([nestedKey, nestedValue]) => {
                    if (nestedValue !== undefined && nestedValue !== null) {
                      acc[`${key}.${nestedKey}`] = nestedValue.toString()
                    }
                  })
                } else {
                  acc[key] = value.toString()
                }
              }
              return acc
            }, {} as Record<string, string>)
          })
          
          if (apprenticeId) {
            params.append('apprenticeId', apprenticeId)
          }

          const response = await fetch(`/api/reviews?${params}`)
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<QuarterlyReview[]> = await response.json()
          set({ 
            reviews: result.data, 
            pagination: result.pagination!,
            filters,
            sort: sortToUse,
            loading: false 
          })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch reviews'
          console.error('Reviews fetch error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      searchReviews: async (searchTerm, page = 1) => {
        const filters = { ...get().filters, searchTerm }
        await get().fetchReviews(page, undefined, filters)
      },

      fetchReviewById: async (id) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch(`/api/reviews/${id}`)
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<QuarterlyReview> = await response.json()
          set({ selectedReview: result.data, loading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch review'
          console.error('Review fetch error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      createReview: async (reviewData) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch('/api/reviews', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(reviewData)
          })
          
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<QuarterlyReview> = await response.json()
          set((state) => ({ 
            reviews: [...state.reviews, result.data],
            loading: false 
          }))
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to create review'
          console.error('Review create error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      updateReviewData: async (id, updates) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch(`/api/reviews/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(updates)
          })
          
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          const result: APIResponse<QuarterlyReview> = await response.json()
          get().updateReview(id, result.data)
          set({ loading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to update review'
          console.error('Review update error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      deleteReview: async (id) => {
        set({ loading: true, error: null })
        try {
          const response = await fetch(`/api/reviews/${id}`, {
            method: 'DELETE'
          })
          
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`)
          
          get().removeReview(id)
          set({ loading: false })
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to delete review'
          console.error('Review delete error:', error)
          set({ 
            error: errorMessage,
            loading: false 
          })
        }
      },

      getReviewsByApprentice: (apprenticeId) => {
        return get().reviews.filter(r => r.apprenticeId === apprenticeId)
      }
    }),
    { name: 'review-store' }
  )
)