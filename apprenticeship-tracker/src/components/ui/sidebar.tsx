import * as React from "react"
import { Link, useRouterState } from "@tanstack/react-router"
import { cn } from "@/lib/utils"

interface SidebarItem {
  label: string
  href: string
  icon: React.ReactNode
  badge?: number
}

interface SidebarProps {
  items: SidebarItem[]
  collapsed?: boolean
  onCollapse?: (collapsed: boolean) => void
  variant?: "default" | "glass" | "modern"
}

const Sidebar = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & SidebarProps
>(({ 
  className, 
  items, 
  collapsed = false, 
  onCollapse,
  variant = "default",
  ...props 
}, ref) => {
  const routerState = useRouterState()
  const currentPath = routerState.location.pathname
  
  const variantClasses = {
    default: "bg-sidebar border-r border-sidebar-border",
    glass: "glass-subtle border-r border-white/10",
    modern: "bg-gradient-to-b from-sidebar to-sidebar/80 shadow-xl"
  }
  
  return (
    <div
      ref={ref}
      className={cn(
        "flex flex-col h-full transition-all duration-300 ease-in-out",
        collapsed ? "w-16" : "w-64",
        variantClasses[variant],
        className
      )}
      {...props}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-sidebar-border">
        {!collapsed && (
          <h1 className="text-xl font-bold text-sidebar-foreground">
            Apprenticeship Tracker
          </h1>
        )}
        {onCollapse && (
          <button
            onClick={() => onCollapse(!collapsed)}
            className="p-2 rounded-lg hover:bg-sidebar-accent transition-colors"
          >
            <svg
              className={cn(
                "w-4 h-4 transition-transform duration-200",
                collapsed ? "rotate-180" : ""
              )}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M11 19l-7-7 7-7m8 14l-7-7 7-7"
              />
            </svg>
          </button>
        )}
      </div>
      
      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {items.map((item) => {
          const isActive = currentPath === item.href
          
          return (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                isActive 
                  ? "bg-sidebar-primary text-sidebar-primary-foreground shadow-md" 
                  : "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                collapsed && "justify-center"
              )}
            >
              <span className="flex-shrink-0">{item.icon}</span>
              {!collapsed && (
                <>
                  <span className="flex-1">{item.label}</span>
                  {item.badge && (
                    <span className="bg-sidebar-primary text-sidebar-primary-foreground text-xs rounded-full px-2 py-1 min-w-[1.5rem] text-center">
                      {item.badge}
                    </span>
                  )}
                </>
              )}
            </Link>
          )
        })}
      </nav>
      
      {/* Footer */}
      <div className="p-4 border-t border-sidebar-border">
        <div className={cn(
          "flex items-center space-x-3",
          collapsed && "justify-center"
        )}>
          <div className="w-8 h-8 rounded-full bg-sidebar-primary flex items-center justify-center">
            <span className="text-sm font-medium text-sidebar-primary-foreground">
              U
            </span>
          </div>
          {!collapsed && (
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-sidebar-foreground truncate">
                User Name
              </p>
              <p className="text-xs text-sidebar-foreground/70 truncate">
                Admin
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
})
Sidebar.displayName = "Sidebar"

export { Sidebar }