import React, { useState } from 'react'
import { Button } from './button'
import { ChevronUp, ChevronDown, ChevronsUpDown } from 'lucide-react'

export type SortDirection = 'asc' | 'desc' | null

export interface ColumnDef<T> {
  key: keyof T
  label: string
  sortable?: boolean
  render?: (value: any, row: T) => React.ReactNode
  width?: string
  className?: string
}

export interface SortConfig<T> {
  key: keyof T
  direction: SortDirection
}

interface SortableTableProps<T> {
  data: T[]
  columns: ColumnDef<T>[]
  onSort?: (sortConfig: SortConfig<T>) => void
  className?: string
  emptyMessage?: string
}

export function SortableTable<T>({
  data,
  columns,
  onSort,
  className = '',
  emptyMessage = 'No data available'
}: SortableTableProps<T>) {
  const [sortConfig, setSortConfig] = useState<SortConfig<T>>({
    key: columns[0]?.key,
    direction: null
  })

  const handleSort = (key: keyof T) => {
    const column = columns.find(col => col.key === key)
    if (!column?.sortable) return

    let direction: SortDirection = 'asc'
    
    if (sortConfig.key === key) {
      if (sortConfig.direction === 'asc') {
        direction = 'desc'
      } else if (sortConfig.direction === 'desc') {
        direction = null
      }
    }

    const newSortConfig = { key, direction }
    setSortConfig(newSortConfig)
    onSort?.(newSortConfig)
  }

  const getSortIcon = (column: ColumnDef<T>) => {
    if (!column.sortable) return null

    if (sortConfig.key !== column.key || sortConfig.direction === null) {
      return <ChevronsUpDown className="w-4 h-4 text-gray-400" />
    }

    return sortConfig.direction === 'asc' ? (
      <ChevronUp className="w-4 h-4 text-blue-600" />
    ) : (
      <ChevronDown className="w-4 h-4 text-blue-600" />
    )
  }

  if (data.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">{emptyMessage}</p>
      </div>
    )
  }

  return (
    <div className={`overflow-x-auto ${className}`}>
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            {columns.map((column) => (
              <th
                key={String(column.key)}
                className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                  column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                } ${column.className || ''}`}
                style={{ width: column.width }}
                onClick={() => column.sortable && handleSort(column.key)}
              >
                <div className="flex items-center space-x-1">
                  <span>{column.label}</span>
                  {getSortIcon(column)}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {data.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              {columns.map((column) => (
                <td
                  key={String(column.key)}
                  className={`px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${column.className || ''}`}
                  style={{ width: column.width }}
                >
                  {column.render
                    ? column.render(row[column.key], row)
                    : String(row[column.key] || '')}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

// Hook for managing sorting state
export function useSorting<T>(
  data: T[],
  onExternalSort?: (sortConfig: SortConfig<T>) => void
) {
  const [sortConfig, setSortConfig] = useState<SortConfig<T>>({
    key: '' as keyof T,
    direction: null
  })

  const handleSort = (newSortConfig: SortConfig<T>) => {
    setSortConfig(newSortConfig)
    
    if (onExternalSort) {
      onExternalSort(newSortConfig)
    }
  }

  const sortedData = React.useMemo(() => {
    if (!sortConfig.direction || onExternalSort) {
      return data
    }

    return [...data].sort((a, b) => {
      const aValue = a[sortConfig.key]
      const bValue = b[sortConfig.key]

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1
      }
      return 0
    })
  }, [data, sortConfig, onExternalSort])

  return {
    sortedData,
    sortConfig,
    handleSort
  }
}

// Utility function for sorting data
export function sortData<T>(
  data: T[],
  sortConfig: SortConfig<T>
): T[] {
  if (!sortConfig.direction) {
    return data
  }

  return [...data].sort((a, b) => {
    const aValue = a[sortConfig.key]
    const bValue = b[sortConfig.key]

    // Handle null/undefined values
    if (aValue == null && bValue == null) return 0
    if (aValue == null) return 1
    if (bValue == null) return -1

    // Handle different data types
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortConfig.direction === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue)
    }

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortConfig.direction === 'asc'
        ? aValue - bValue
        : bValue - aValue
    }

    if (aValue instanceof Date && bValue instanceof Date) {
      return sortConfig.direction === 'asc'
        ? aValue.getTime() - bValue.getTime()
        : bValue.getTime() - aValue.getTime()
    }

    // Fallback to string comparison
    const aString = String(aValue)
    const bString = String(bValue)
    return sortConfig.direction === 'asc'
      ? aString.localeCompare(bString)
      : bString.localeCompare(aString)
  })
}