export const appConfig = {
  name: 'Apprenticeship Tracker',
  version: '1.0.0',
  api: {
    baseUrl: import.meta.env.VITE_API_URL || '/api',
    timeout: 30000,
  },
  features: {
    enableMocking: import.meta.env.VITE_ENABLE_MOCKING === 'true',
    enableDevtools: import.meta.env.DEV,
  },
  pagination: {
    defaultPageSize: 20,
    pageSizeOptions: [10, 20, 50, 100],
  },
  dateFormats: {
    display: 'DD/MM/YYYY',
    input: 'YYYY-MM-DD',
    datetime: 'DD/MM/YYYY HH:mm',
  },
} as const