import React, { useState } from 'react'
import { QuarterlyReview } from '../../../types'
import { Button } from '../../../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { useModal } from '../../../components/ui/modal-provider'
import { ReviewForm } from './ReviewForm'
import { useReviewStore } from '../reviewStore'
import { 
  Calendar, 
  User, 
  Award, 
  Target,
  MessageCircle,
  TrendingUp,
  TrendingDown,
  Edit3,
  Trash2,
  Star
} from 'lucide-react'

interface ReviewDetailsModalProps {
  review: QuarterlyReview
  onClose: () => void
}

export const ReviewDetailsModal: React.FC<ReviewDetailsModalProps> = ({
  review,
  onClose
}) => {
  const [isEditing, setIsEditing] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const { updateReviewData, deleteReview } = useReviewStore()
  const { confirmModal } = useModal()

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleUpdate = async (data: Partial<QuarterlyReview>) => {
    try {
      await updateReviewData(review.id, data)
      setIsEditing(false)
      onClose()
    } catch (error) {
      console.error('Failed to update review:', error)
    }
  }

  const handleDelete = () => {
    confirmModal({
      title: 'Delete Review',
      message: `Are you sure you want to delete this Q${review.quarter} ${review.year} review? This action cannot be undone.`,
      onConfirm: async () => {
        setIsDeleting(true)
        try {
          await deleteReview(review.id)
          onClose()
        } catch (error) {
          console.error('Failed to delete review:', error)
        } finally {
          setIsDeleting(false)
        }
      }
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in-progress':
        return 'bg-blue-100 text-blue-800'
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getScoreColor = (score: number) => {
    if (score >= 4) return 'text-green-600'
    if (score >= 3) return 'text-yellow-600'
    return 'text-red-600'
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ))
  }

  if (isEditing) {
    return (
      <div className="max-w-4xl">
        <ReviewForm
          review={review}
          onSubmit={handleUpdate}
          onCancel={() => setIsEditing(false)}
        />
      </div>
    )
  }

  return (
    <div className="max-w-4xl">
      {/* Header */}
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              Q{review.quarter} {review.year} Review
            </h2>
            <p className="text-gray-500 mt-1">
              Review Date: {formatDate(review.reviewDate)}
            </p>
            <span className={`inline-flex rounded-full px-3 py-1 text-sm font-semibold mt-2 ${getStatusColor(review.status)}`}>
              {review.status}
            </span>
          </div>
          <div className="flex space-x-2">
            <Button onClick={handleEdit} variant="outline" size="sm">
              <Edit3 className="w-4 h-4 mr-2" />
              Edit
            </Button>
            <Button 
              onClick={handleDelete} 
              variant="destructive" 
              size="sm"
              disabled={isDeleting}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              {isDeleting ? 'Deleting...' : 'Delete'}
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6 space-y-6">
        {/* Mentor Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="w-5 h-5 mr-2" />
              Mentor Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Mentor Name</p>
                <p className="font-medium">{review.mentorName}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Mentor ID</p>
                <p className="font-medium">{review.mentorId}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance Scores */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="w-5 h-5 mr-2" />
              Performance Scores
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="text-center">
                <p className="text-sm text-gray-500 mb-2">Technical Skills</p>
                <p className={`text-2xl font-bold ${getScoreColor(review.scores.technicalSkills)}`}>
                  {review.scores.technicalSkills}/5
                </p>
                <div className="flex justify-center mt-1">
                  {renderStars(review.scores.technicalSkills)}
                </div>
              </div>
              
              <div className="text-center">
                <p className="text-sm text-gray-500 mb-2">Communication</p>
                <p className={`text-2xl font-bold ${getScoreColor(review.scores.communication)}`}>
                  {review.scores.communication}/5
                </p>
                <div className="flex justify-center mt-1">
                  {renderStars(review.scores.communication)}
                </div>
              </div>
              
              <div className="text-center">
                <p className="text-sm text-gray-500 mb-2">Teamwork</p>
                <p className={`text-2xl font-bold ${getScoreColor(review.scores.teamwork)}`}>
                  {review.scores.teamwork}/5
                </p>
                <div className="flex justify-center mt-1">
                  {renderStars(review.scores.teamwork)}
                </div>
              </div>
              
              <div className="text-center">
                <p className="text-sm text-gray-500 mb-2">Initiative</p>
                <p className={`text-2xl font-bold ${getScoreColor(review.scores.initiative)}`}>
                  {review.scores.initiative}/5
                </p>
                <div className="flex justify-center mt-1">
                  {renderStars(review.scores.initiative)}
                </div>
              </div>
              
              <div className="text-center">
                <p className="text-sm text-gray-500 mb-2">Punctuality</p>
                <p className={`text-2xl font-bold ${getScoreColor(review.scores.punctuality)}`}>
                  {review.scores.punctuality}/5
                </p>
                <div className="flex justify-center mt-1">
                  {renderStars(review.scores.punctuality)}
                </div>
              </div>
              
              <div className="text-center">
                <p className="text-sm text-gray-500 mb-2">Overall Rating</p>
                <p className={`text-3xl font-bold ${getScoreColor(review.scores.overallRating)}`}>
                  {review.scores.overallRating}/5
                </p>
                <div className="flex justify-center mt-1">
                  {renderStars(review.scores.overallRating)}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Strengths */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              Strengths
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {review.strengths.map((strength, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>{strength}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Areas for Improvement */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingDown className="w-5 h-5 mr-2" />
              Areas for Improvement
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {review.areasForImprovement.map((area, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <span>{area}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Goals */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="w-5 h-5 mr-2" />
              Goals for Next Quarter
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {review.goals.map((goal, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>{goal}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Comments */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageCircle className="w-5 h-5 mr-2" />
              Comments
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Mentor Comments</h4>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-gray-700">{review.mentorComments}</p>
                </div>
              </div>
              
              {review.apprenticeComments && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Apprentice Comments</h4>
                  <div className="bg-blue-50 rounded-lg p-4">
                    <p className="text-gray-700">{review.apprenticeComments}</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Review Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Review Timeline
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium">Review Scheduled</p>
                  <p className="text-xs text-gray-500">
                    {formatDate(review.reviewDate)}
                  </p>
                </div>
              </div>
              
              {review.status === 'completed' && (
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium">Review Completed</p>
                    <p className="text-xs text-gray-500">
                      Overall Rating: {review.scores.overallRating}/5
                    </p>
                  </div>
                </div>
              )}
              
              {review.status === 'in-progress' && (
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium">Review In Progress</p>
                    <p className="text-xs text-gray-500">
                      Being conducted by {review.mentorName}
                    </p>
                  </div>
                </div>
              )}
              
              {review.status === 'overdue' && (
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium">Review Overdue</p>
                    <p className="text-xs text-gray-500">
                      Please schedule and complete as soon as possible
                    </p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}