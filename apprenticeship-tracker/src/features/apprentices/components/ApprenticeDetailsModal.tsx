import React, { useState } from 'react'
import { Apprentice } from '../../../types'
import { Button } from '../../../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { useModal } from '../../../components/ui/modal-provider'
import { ApprenticeForm } from './ApprenticeForm'
import { useApprenticeStore } from '../apprenticeStore'
import { 
  Mail, 
  Phone, 
  Calendar, 
  User, 
  Building, 
  Award, 
  Clock,
  Edit3,
  Trash2
} from 'lucide-react'

interface ApprenticeDetailsModalProps {
  apprentice: Apprentice
  onClose: () => void
}

export const ApprenticeDetailsModal: React.FC<ApprenticeDetailsModalProps> = ({
  apprentice,
  onClose
}) => {
  const [isEditing, setIsEditing] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const { updateApprenticeData, deleteApprentice } = useApprenticeStore()
  const { confirmModal } = useModal()

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleUpdate = async (data: Partial<Apprentice>) => {
    try {
      await updateApprenticeData(apprentice.id, data)
      setIsEditing(false)
      onClose()
    } catch (error) {
      console.error('Failed to update apprentice:', error)
    }
  }

  const handleDelete = () => {
    confirmModal({
      title: 'Delete Apprentice',
      message: `Are you sure you want to delete ${apprentice.firstName} ${apprentice.lastName}? This action cannot be undone.`,
      onConfirm: async () => {
        setIsDeleting(true)
        try {
          await deleteApprentice(apprentice.id)
          onClose()
        } catch (error) {
          console.error('Failed to delete apprentice:', error)
        } finally {
          setIsDeleting(false)
        }
      }
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      case 'suspended':
        return 'bg-yellow-100 text-yellow-800'
      case 'withdrawn':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const calculateDuration = () => {
    const start = new Date(apprentice.startDate)
    const end = new Date(apprentice.endDate)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    const months = Math.floor(diffDays / 30)
    return `${months} months`
  }

  const calculateTimeRemaining = () => {
    const now = new Date()
    const end = new Date(apprentice.endDate)
    const diffTime = end.getTime() - now.getTime()
    
    if (diffTime < 0) return 'Completed'
    
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    const months = Math.floor(diffDays / 30)
    const days = diffDays % 30
    
    if (months > 0) {
      return `${months} months, ${days} days remaining`
    } else {
      return `${days} days remaining`
    }
  }

  if (isEditing) {
    return (
      <div className="max-w-4xl">
        <ApprenticeForm
          apprentice={apprentice}
          onSubmit={handleUpdate}
          onCancel={() => setIsEditing(false)}
        />
      </div>
    )
  }

  return (
    <div className="max-w-4xl">
      {/* Header */}
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <User className="w-8 h-8 text-blue-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {apprentice.firstName} {apprentice.lastName}
              </h2>
              <p className="text-gray-500">{apprentice.position}</p>
              <span className={`inline-flex rounded-full px-3 py-1 text-sm font-semibold ${getStatusColor(apprentice.status)}`}>
                {apprentice.status}
              </span>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button onClick={handleEdit} variant="outline" size="sm">
              <Edit3 className="w-4 h-4 mr-2" />
              Edit
            </Button>
            <Button 
              onClick={handleDelete} 
              variant="destructive" 
              size="sm"
              disabled={isDeleting}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              {isDeleting ? 'Deleting...' : 'Delete'}
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6 space-y-6">
        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Mail className="w-5 h-5 mr-2" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-3">
                <Mail className="w-4 h-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Email</p>
                  <p className="font-medium">{apprentice.email}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="w-4 h-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Phone</p>
                  <p className="font-medium">{apprentice.phone}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Program Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building className="w-5 h-5 mr-2" />
              Program Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Department</p>
                <p className="font-medium">{apprentice.department}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Mentor</p>
                <p className="font-medium">{apprentice.mentor}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Duration</p>
                <p className="font-medium">{calculateDuration()}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Time Remaining</p>
                <p className="font-medium">{calculateTimeRemaining()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Timeline
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div>
                  <p className="text-sm text-gray-500">Start Date</p>
                  <p className="font-medium">{formatDate(apprentice.startDate)}</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div>
                  <p className="text-sm text-gray-500">End Date</p>
                  <p className="font-medium">{formatDate(apprentice.endDate)}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="w-5 h-5 mr-2" />
              Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium">Overall Progress</span>
                  <span className="text-sm text-gray-500">{apprentice.overallProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                    style={{ width: `${apprentice.overallProgress}%` }}
                  />
                </div>
              </div>
              
              {/* Mock module progress */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
                <div>
                  <p className="text-sm text-gray-500">Modules Completed</p>
                  <p className="font-medium">8 of 12</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Reviews Completed</p>
                  <p className="font-medium">2 of 4</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Exams Passed</p>
                  <p className="font-medium">5 of 6</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Hours Logged</p>
                  <p className="font-medium">320 of 400</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="w-5 h-5 mr-2" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium">Module 8 completed</p>
                  <p className="text-xs text-gray-500">2 days ago</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium">Quarterly review scheduled</p>
                  <p className="text-xs text-gray-500">1 week ago</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium">Exam submitted</p>
                  <p className="text-xs text-gray-500">2 weeks ago</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}