import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Exam } from '../../../types'

export function Exams() {
  const [exams, setExams] = useState<Exam[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isCreating, setIsCreating] = useState(false)

  useEffect(() => {
    fetchExams()
  }, [])

  const fetchExams = async () => {
    try {
      const response = await fetch('/api/exams')
      if (!response.ok) throw new Error('Failed to fetch exams')
      const apiResponse = await response.json()
      // Handle the API response format which wraps data in a 'data' property
      const data = apiResponse.data || apiResponse
      setExams(Array.isArray(data) ? data : [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateExam = async () => {
    setIsCreating(true)
    try {
      const response = await fetch('/api/exams', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: 'Sample Programming Assessment',
          description: 'Assess programming fundamentals and problem-solving skills',
          subject: 'Software Development',
          type: 'practical',
          duration: 180,
          maxScore: 100,
          passingScore: 70,
          status: 'upcoming',
          scheduledDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
        })
      })
      
      if (!response.ok) throw new Error('Failed to create exam')
      await fetchExams()
      alert('Sample exam created! In a real app, this would open a form.')
    } catch (error) {
      console.error('Failed to create exam:', error)
    } finally {
      setIsCreating(false)
    }
  }

  const handleViewDetails = (examId: string) => {
    alert(`View details for exam ${examId}. In a real app, this would navigate to the exam details page.`)
  }

  const handleStartExam = async (examId: string) => {
    alert(`Starting exam ${examId}. In a real app, this would navigate to the exam taking interface.`)
    
    // Update exam status to active
    try {
      await fetch(`/api/exams/${examId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'active' })
      })
      await fetchExams()
    } catch (error) {
      console.error('Failed to update exam status:', error)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading exams...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-red-600">Error: {error}</div>
      </div>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'active':
        return 'bg-blue-100 text-blue-800'
      case 'upcoming':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'written':
        return '📝'
      case 'practical':
        return '🔧'
      case 'oral':
        return '🎤'
      case 'project':
        return '💻'
      default:
        return '📋'
    }
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} min`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between px-4 sm:px-0">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Exams</h1>
          <p className="mt-2 text-sm text-gray-700">
            Manage and track apprentice examinations and assessments.
          </p>
        </div>
        <Button onClick={handleCreateExam} disabled={isCreating}>
          {isCreating ? 'Creating...' : 'Create Exam'}
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {exams.map((exam) => (
          <Card key={exam.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg flex items-center space-x-2">
                  <span>{getTypeIcon(exam.type)}</span>
                  <span>{exam.title}</span>
                </CardTitle>
                <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getStatusColor(exam.status)}`}>
                  {exam.status}
                </span>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600">{exam.description}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <p className="text-gray-500">Subject</p>
                    <p className="font-medium">{exam.subject}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Type</p>
                    <p className="font-medium capitalize">{exam.type}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Duration</p>
                    <p className="font-medium">{formatDuration(exam.duration)}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Passing Score</p>
                    <p className="font-medium">{exam.passingScore}/{exam.maxScore}</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-500">Scheduled Date</p>
                  <p className="text-sm font-medium">
                    {new Date(exam.scheduledDate).toLocaleString()}
                  </p>
                </div>

                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${(exam.passingScore / exam.maxScore) * 100}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-500">
                  {Math.round((exam.passingScore / exam.maxScore) * 100)}% required to pass
                </p>

                <div className="pt-2 space-y-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full"
                    onClick={() => handleViewDetails(exam.id)}
                  >
                    View Details
                  </Button>
                  {exam.status === 'upcoming' && (
                    <Button 
                      size="sm" 
                      className="w-full"
                      onClick={() => handleStartExam(exam.id)}
                    >
                      Start Exam
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {exams.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No exams found.</p>
          <Button className="mt-4" onClick={handleCreateExam}>
            Create First Exam
          </Button>
        </div>
      )}
    </div>
  )
}