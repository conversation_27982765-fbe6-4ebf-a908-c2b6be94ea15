import * as React from "react"
import { cn } from "@/lib/utils"

const Progress = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    value?: number
    max?: number
    variant?: "default" | "gradient" | "glass" | "neon"
    size?: "sm" | "md" | "lg"
    showValue?: boolean
    animated?: boolean
  }
>(({ 
  className, 
  value = 0, 
  max = 100, 
  variant = "default", 
  size = "md",
  showValue = false,
  animated = true,
  ...props 
}, ref) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  
  const sizeClasses = {
    sm: "h-2",
    md: "h-3",
    lg: "h-4"
  }
  
  const variantClasses = {
    default: "bg-secondary",
    gradient: "bg-gradient-to-r from-primary to-secondary",
    glass: "glass-subtle",
    neon: "bg-primary shadow-neon"
  }
  
  const fillVariantClasses = {
    default: "bg-primary",
    gradient: "gradient-primary",
    glass: "glass bg-primary/50",
    neon: "bg-primary shadow-neon-sm"
  }
  
  return (
    <div
      ref={ref}
      className={cn(
        "relative w-full rounded-full overflow-hidden",
        sizeClasses[size],
        variantClasses[variant],
        className
      )}
      {...props}
    >
      <div
        className={cn(
          "h-full transition-all duration-500 ease-out rounded-full",
          fillVariantClasses[variant],
          animated && "animate-pulse"
        )}
        style={{ width: `${percentage}%` }}
      />
      {showValue && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-xs font-medium text-white drop-shadow-sm">
            {Math.round(percentage)}%
          </span>
        </div>
      )}
    </div>
  )
})
Progress.displayName = "Progress"

const CircularProgress = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    value?: number
    max?: number
    size?: number
    strokeWidth?: number
    variant?: "default" | "gradient" | "neon"
    showValue?: boolean
  }
>(({ 
  className, 
  value = 0, 
  max = 100, 
  size = 120,
  strokeWidth = 8,
  variant = "default",
  showValue = true,
  ...props 
}, ref) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  const radius = (size - strokeWidth) / 2
  const circumference = 2 * Math.PI * radius
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (percentage / 100) * circumference
  
  const variantClasses = {
    default: "stroke-primary",
    gradient: "stroke-primary",
    neon: "stroke-primary drop-shadow-glow"
  }
  
  return (
    <div
      ref={ref}
      className={cn(
        "relative flex items-center justify-center",
        className
      )}
      style={{ width: size, height: size }}
      {...props}
    >
      <svg
        className="transform -rotate-90"
        width={size}
        height={size}
      >
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          className="text-muted opacity-20"
        />
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={cn(
            "transition-all duration-500 ease-out",
            variantClasses[variant]
          )}
        />
      </svg>
      {showValue && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-2xl font-bold text-foreground">
            {Math.round(percentage)}%
          </span>
        </div>
      )}
    </div>
  )
})
CircularProgress.displayName = "CircularProgress"

const StepProgress = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    steps: Array<{
      label: string
      completed: boolean
      current?: boolean
    }>
    variant?: "default" | "modern" | "glass"
  }
>(({ className, steps, variant = "default", ...props }, ref) => {
  const variantClasses = {
    default: "",
    modern: "glass-subtle p-4 rounded-2xl",
    glass: "glass-card p-6"
  }
  
  return (
    <div
      ref={ref}
      className={cn(
        "flex items-center space-x-4",
        variantClasses[variant],
        className
      )}
      {...props}
    >
      {steps.map((step, index) => (
        <React.Fragment key={index}>
          <div className="flex items-center space-x-2">
            <div
              className={cn(
                "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300",
                step.completed 
                  ? "bg-primary text-primary-foreground" 
                  : step.current 
                  ? "bg-primary/20 text-primary border-2 border-primary" 
                  : "bg-muted text-muted-foreground"
              )}
            >
              {step.completed ? "✓" : index + 1}
            </div>
            <span
              className={cn(
                "text-sm font-medium",
                step.completed 
                  ? "text-primary" 
                  : step.current 
                  ? "text-foreground" 
                  : "text-muted-foreground"
              )}
            >
              {step.label}
            </span>
          </div>
          {index < steps.length - 1 && (
            <div
              className={cn(
                "h-0.5 bg-muted rounded-full flex-1 transition-all duration-300",
                step.completed ? "bg-primary" : ""
              )}
            />
          )}
        </React.Fragment>
      ))}
    </div>
  )
})
StepProgress.displayName = "StepProgress"

export { Progress, CircularProgress, StepProgress }