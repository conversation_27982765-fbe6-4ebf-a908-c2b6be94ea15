/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as ReviewsRouteRouteImport } from './routes/reviews.route'
import { Route as ExamsRouteRouteImport } from './routes/exams.route'
import { Route as ApprenticesRouteRouteImport } from './routes/apprentices.route'
import { Route as IndexRouteImport } from './routes/index'
import { Route as ReviewsIndexRouteImport } from './routes/reviews.index'
import { Route as ExamsIndexRouteImport } from './routes/exams.index'
import { Route as ApprenticesIndexRouteImport } from './routes/apprentices.index'

const ReviewsRouteRoute = ReviewsRouteRouteImport.update({
  id: '/reviews',
  path: '/reviews',
  getParentRoute: () => rootRouteImport,
} as any)
const ExamsRouteRoute = ExamsRouteRouteImport.update({
  id: '/exams',
  path: '/exams',
  getParentRoute: () => rootRouteImport,
} as any)
const ApprenticesRouteRoute = ApprenticesRouteRouteImport.update({
  id: '/apprentices',
  path: '/apprentices',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const ReviewsIndexRoute = ReviewsIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => ReviewsRouteRoute,
} as any)
const ExamsIndexRoute = ExamsIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => ExamsRouteRoute,
} as any)
const ApprenticesIndexRoute = ApprenticesIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => ApprenticesRouteRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/apprentices': typeof ApprenticesRouteRouteWithChildren
  '/exams': typeof ExamsRouteRouteWithChildren
  '/reviews': typeof ReviewsRouteRouteWithChildren
  '/apprentices/': typeof ApprenticesIndexRoute
  '/exams/': typeof ExamsIndexRoute
  '/reviews/': typeof ReviewsIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/apprentices': typeof ApprenticesIndexRoute
  '/exams': typeof ExamsIndexRoute
  '/reviews': typeof ReviewsIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/apprentices': typeof ApprenticesRouteRouteWithChildren
  '/exams': typeof ExamsRouteRouteWithChildren
  '/reviews': typeof ReviewsRouteRouteWithChildren
  '/apprentices/': typeof ApprenticesIndexRoute
  '/exams/': typeof ExamsIndexRoute
  '/reviews/': typeof ReviewsIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/apprentices'
    | '/exams'
    | '/reviews'
    | '/apprentices/'
    | '/exams/'
    | '/reviews/'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/apprentices' | '/exams' | '/reviews'
  id:
    | '__root__'
    | '/'
    | '/apprentices'
    | '/exams'
    | '/reviews'
    | '/apprentices/'
    | '/exams/'
    | '/reviews/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  ApprenticesRouteRoute: typeof ApprenticesRouteRouteWithChildren
  ExamsRouteRoute: typeof ExamsRouteRouteWithChildren
  ReviewsRouteRoute: typeof ReviewsRouteRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/reviews': {
      id: '/reviews'
      path: '/reviews'
      fullPath: '/reviews'
      preLoaderRoute: typeof ReviewsRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/exams': {
      id: '/exams'
      path: '/exams'
      fullPath: '/exams'
      preLoaderRoute: typeof ExamsRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/apprentices': {
      id: '/apprentices'
      path: '/apprentices'
      fullPath: '/apprentices'
      preLoaderRoute: typeof ApprenticesRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/reviews/': {
      id: '/reviews/'
      path: '/'
      fullPath: '/reviews/'
      preLoaderRoute: typeof ReviewsIndexRouteImport
      parentRoute: typeof ReviewsRouteRoute
    }
    '/exams/': {
      id: '/exams/'
      path: '/'
      fullPath: '/exams/'
      preLoaderRoute: typeof ExamsIndexRouteImport
      parentRoute: typeof ExamsRouteRoute
    }
    '/apprentices/': {
      id: '/apprentices/'
      path: '/'
      fullPath: '/apprentices/'
      preLoaderRoute: typeof ApprenticesIndexRouteImport
      parentRoute: typeof ApprenticesRouteRoute
    }
  }
}

interface ApprenticesRouteRouteChildren {
  ApprenticesIndexRoute: typeof ApprenticesIndexRoute
}

const ApprenticesRouteRouteChildren: ApprenticesRouteRouteChildren = {
  ApprenticesIndexRoute: ApprenticesIndexRoute,
}

const ApprenticesRouteRouteWithChildren =
  ApprenticesRouteRoute._addFileChildren(ApprenticesRouteRouteChildren)

interface ExamsRouteRouteChildren {
  ExamsIndexRoute: typeof ExamsIndexRoute
}

const ExamsRouteRouteChildren: ExamsRouteRouteChildren = {
  ExamsIndexRoute: ExamsIndexRoute,
}

const ExamsRouteRouteWithChildren = ExamsRouteRoute._addFileChildren(
  ExamsRouteRouteChildren,
)

interface ReviewsRouteRouteChildren {
  ReviewsIndexRoute: typeof ReviewsIndexRoute
}

const ReviewsRouteRouteChildren: ReviewsRouteRouteChildren = {
  ReviewsIndexRoute: ReviewsIndexRoute,
}

const ReviewsRouteRouteWithChildren = ReviewsRouteRoute._addFileChildren(
  ReviewsRouteRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  ApprenticesRouteRoute: ApprenticesRouteRouteWithChildren,
  ExamsRouteRoute: ExamsRouteRouteWithChildren,
  ReviewsRouteRoute: ReviewsRouteRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
