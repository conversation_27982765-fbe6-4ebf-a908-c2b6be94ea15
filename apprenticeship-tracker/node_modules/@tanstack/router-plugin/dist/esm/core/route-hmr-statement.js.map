{"version": 3, "file": "route-hmr-statement.js", "sources": ["../../../src/core/route-hmr-statement.ts"], "sourcesContent": ["import * as template from '@babel/template'\n\nexport const routeHmrStatement = template.statement(\n  `\nif (import.meta.hot) {\n  import.meta.hot.accept((newModule) => {\n    if (newModule && newModule.Route && typeof newModule.Route.clone === 'function') {\n      newModule.Route.clone(Route)\n    }\n   })\n}\n`,\n)()\n"], "names": [], "mappings": ";AAEO,MAAM,oBAAoB,SAAS;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASF,EAAE;"}