"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const tsrSplit = "tsr-split";
const splitRouteIdentNodes = [
  "loader",
  "component",
  "pendingComponent",
  "errorComponent",
  "notFoundComponent"
];
const defaultCodeSplitGroupings = [
  ["component"],
  ["errorComponent"],
  ["notFoundComponent"]
];
exports.defaultCodeSplitGroupings = defaultCodeSplitGroupings;
exports.splitRouteIdentNodes = splitRouteIdentNodes;
exports.tsrSplit = tsrSplit;
//# sourceMappingURL=constants.cjs.map
