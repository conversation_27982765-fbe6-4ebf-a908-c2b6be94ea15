import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apprenticesApi, type Apprentice } from '../../services/api/apprentices'

export function useApprentices() {
  return useQuery({
    queryKey: ['apprentices'],
    queryFn: apprenticesApi.getAll,
  })
}

export function useApprentice(id: string) {
  return useQuery({
    queryKey: ['apprentices', id],
    queryFn: () => apprenticesApi.getById(id),
    enabled: !!id,
  })
}

export function useCreateApprentice() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: apprenticesApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['apprentices'] })
    },
  })
}

export function useUpdateApprentice() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Apprentice> }) =>
      apprenticesApi.update(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['apprentices'] })
      queryClient.invalidateQueries({ queryKey: ['apprentices', id] })
    },
  })
}