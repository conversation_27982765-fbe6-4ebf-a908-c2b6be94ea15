export { configSchema, getConfig } from './core/config.js';
export { unpluginRouterCodeSplitterFactory } from './core/router-code-splitter-plugin.js';
export { unpluginRouterGeneratorFactory } from './core/router-generator-plugin.js';
export type { Config, ConfigInput, ConfigOutput } from './core/config.js';
export { tsrSplit, splitRouteIdentNodes, defaultCodeSplitGroupings, } from './core/constants.js';
