import React, { useState } from 'react'
import { Apprentice, QuarterlyReview, ExamResult, ApprenticeProgress, ProgressModule } from '../../../types'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Badge } from '../../../components/ui/badge'
import { Progress } from '../../../components/ui/progress'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '../../../components/ui/tabs'
import { 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  Building, 
  Award, 
  BookOpen, 
  Target,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Star,
  FileText,
  BarChart3
} from 'lucide-react'

interface ApprenticeDetailViewProps {
  apprentice: Apprentice
  reviews?: QuarterlyReview[]
  examResults?: ExamResult[]
  moduleProgress?: ApprenticeProgress[]
  modules?: ProgressModule[]
}

// Mock data for demonstration
const mockReviews: QuarterlyReview[] = [
  {
    id: '1',
    apprenticeId: '1',
    quarter: 1,
    year: 2024,
    reviewDate: '2024-03-15T10:00:00Z',
    mentorId: 'mentor-1',
    mentorName: 'John Smith',
    scores: {
      technicalSkills: 4,
      communication: 4,
      teamwork: 5,
      initiative: 3,
      punctuality: 5,
      overallRating: 4
    },
    strengths: ['Strong technical foundation', 'Great team player', 'Excellent attendance'],
    areasForImprovement: ['Needs to take more initiative', 'Improve presentation skills'],
    goals: ['Complete advanced JavaScript course', 'Lead a small project'],
    mentorComments: 'Solid progress this quarter. Focus on taking more initiative in meetings.',
    apprenticeComments: 'Enjoying the learning process and feel more confident with React.',
    status: 'completed'
  }
]

const mockExamResults: ExamResult[] = [
  {
    id: '1',
    examId: 'exam-1',
    apprenticeId: '1',
    score: 85,
    completedAt: '2024-02-20T14:30:00Z',
    timeSpent: 120,
    feedback: 'Excellent understanding of core concepts.',
    passed: true,
    retakeAllowed: false,
    retakeCount: 0
  },
  {
    id: '2',
    examId: 'exam-2',
    apprenticeId: '1',
    score: 75,
    completedAt: '2024-01-15T11:15:00Z',
    timeSpent: 90,
    feedback: 'Good effort, minor improvements needed.',
    passed: true,
    retakeAllowed: false,
    retakeCount: 0
  }
]

const mockModules: ProgressModule[] = [
  {
    id: '1',
    title: 'JavaScript Fundamentals',
    description: 'Core JavaScript concepts and ES6+ features',
    category: 'Programming',
    requiredHours: 40,
    order: 1,
    prerequisites: [],
    isRequired: true
  },
  {
    id: '2',
    title: 'React Development',
    description: 'Modern React development with hooks and context',
    category: 'Frontend',
    requiredHours: 60,
    order: 2,
    prerequisites: ['1'],
    isRequired: true
  },
  {
    id: '3',
    title: 'Node.js & Express',
    description: 'Backend development with Node.js',
    category: 'Backend',
    requiredHours: 50,
    order: 3,
    prerequisites: ['1'],
    isRequired: true
  }
]

const mockModuleProgress: ApprenticeProgress[] = [
  {
    id: '1',
    apprenticeId: '1',
    moduleId: '1',
    hoursCompleted: 40,
    status: 'completed',
    startDate: '2024-01-01T00:00:00Z',
    completedDate: '2024-02-01T00:00:00Z',
    notes: 'Completed all assignments and projects.',
    mentorApproved: true
  },
  {
    id: '2',
    apprenticeId: '1',
    moduleId: '2',
    hoursCompleted: 45,
    status: 'in-progress',
    startDate: '2024-02-01T00:00:00Z',
    notes: 'Working on final project.',
    mentorApproved: false
  },
  {
    id: '3',
    apprenticeId: '1',
    moduleId: '3',
    hoursCompleted: 0,
    status: 'not-started',
    notes: '',
    mentorApproved: false
  }
]

export const ApprenticeDetailView: React.FC<ApprenticeDetailViewProps> = ({
  apprentice,
  reviews = mockReviews,
  examResults = mockExamResults,
  moduleProgress = mockModuleProgress,
  modules = mockModules
}) => {
  const [activeTab, setActiveTab] = useState('overview')

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'completed': return 'bg-blue-100 text-blue-800'
      case 'suspended': return 'bg-yellow-100 text-yellow-800'
      case 'withdrawn': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getModuleStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'in-progress': return 'bg-blue-100 text-blue-800'
      case 'not-started': return 'bg-gray-100 text-gray-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const calculateTimeRemaining = () => {
    const now = new Date()
    const end = new Date(apprentice.endDate)
    const diffTime = end.getTime() - now.getTime()
    
    if (diffTime < 0) return 'Completed'
    
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    const months = Math.floor(diffDays / 30)
    const days = diffDays % 30
    
    if (months > 0) {
      return `${months} months, ${days} days remaining`
    } else {
      return `${days} days remaining`
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ))
  }

  const getModuleProgress = (moduleId: string) => {
    const progress = moduleProgress.find(p => p.moduleId === moduleId)
    const module = modules.find(m => m.id === moduleId)
    
    if (!progress || !module) return { percentage: 0, status: 'not-started' }
    
    const percentage = Math.round((progress.hoursCompleted / module.requiredHours) * 100)
    return { percentage, status: progress.status }
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
              <User className="w-10 h-10 text-blue-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {apprentice.firstName} {apprentice.lastName}
              </h1>
              <p className="text-lg text-gray-600">{apprentice.position}</p>
              <p className="text-sm text-gray-500">{apprentice.department}</p>
              <span className={`inline-flex rounded-full px-3 py-1 text-sm font-semibold mt-2 ${getStatusColor(apprentice.status)}`}>
                {apprentice.status}
              </span>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500 mb-1">Overall Progress</div>
            <div className="text-3xl font-bold text-blue-600">{apprentice.overallProgress}%</div>
            <div className="w-32 bg-gray-200 rounded-full h-2 mt-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                style={{ width: `${apprentice.overallProgress}%` }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Calendar className="w-8 h-8 text-blue-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Time Remaining</p>
                <p className="font-semibold">{calculateTimeRemaining()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <BookOpen className="w-8 h-8 text-green-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Modules Completed</p>
                <p className="font-semibold">
                  {moduleProgress.filter(p => p.status === 'completed').length} / {modules.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Award className="w-8 h-8 text-purple-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Exams Passed</p>
                <p className="font-semibold">
                  {examResults.filter(r => r.passed).length} / {examResults.length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <BarChart3 className="w-8 h-8 text-orange-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Avg Review Score</p>
                <p className="font-semibold">
                  {reviews.length > 0 
                    ? `${(reviews.reduce((sum, r) => sum + r.scores.overallRating, 0) / reviews.length).toFixed(1)}/5`
                    : 'N/A'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="progress">Progress</TabsTrigger>
          <TabsTrigger value="reviews">Reviews</TabsTrigger>
          <TabsTrigger value="exams">Exams</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="w-5 h-5 mr-2" />
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="w-4 h-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium">{apprentice.email}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="w-4 h-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p className="font-medium">{apprentice.phone}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Building className="w-4 h-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Department</p>
                    <p className="font-medium">{apprentice.department}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <User className="w-4 h-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Mentor</p>
                    <p className="font-medium">{apprentice.mentor}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Program Timeline
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm text-gray-500">Start Date</p>
                  <p className="font-medium">{formatDate(apprentice.startDate)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">End Date</p>
                  <p className="font-medium">{formatDate(apprentice.endDate)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Duration</p>
                  <p className="font-medium">
                    {Math.ceil((new Date(apprentice.endDate).getTime() - new Date(apprentice.startDate).getTime()) / (1000 * 60 * 60 * 24 * 30))} months
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getStatusColor(apprentice.status)}`}>
                    {apprentice.status}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Progress Tab */}
        <TabsContent value="progress" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="w-5 h-5 mr-2" />
                Module Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {modules.map((module) => {
                  const progress = getModuleProgress(module.id)
                  const moduleProgressData = moduleProgress.find(p => p.moduleId === module.id)
                  
                  return (
                    <div key={module.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <h3 className="font-medium">{module.title}</h3>
                          <p className="text-sm text-gray-500">{module.description}</p>
                        </div>
                        <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getModuleStatusColor(progress.status)}`}>
                          {progress.status}
                        </span>
                      </div>
                      
                      <div className="mb-3">
                        <div className="flex justify-between text-sm text-gray-600 mb-1">
                          <span>Progress</span>
                          <span>{progress.percentage}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                            style={{ width: `${progress.percentage}%` }}
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Hours Completed</p>
                          <p className="font-medium">
                            {moduleProgressData?.hoursCompleted || 0} / {module.requiredHours}
                          </p>
                        </div>
                        <div>
                          <p className="text-gray-500">Category</p>
                          <p className="font-medium">{module.category}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Required</p>
                          <p className="font-medium">{module.isRequired ? 'Yes' : 'No'}</p>
                        </div>
                      </div>
                      
                      {moduleProgressData?.notes && (
                        <div className="mt-3 p-2 bg-gray-50 rounded">
                          <p className="text-sm text-gray-700">{moduleProgressData.notes}</p>
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Reviews Tab */}
        <TabsContent value="reviews" className="space-y-6">
          <div className="space-y-4">
            {reviews.map((review) => (
              <Card key={review.id}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Q{review.quarter} {review.year} Review</span>
                    <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getStatusColor(review.status)}`}>
                      {review.status}
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-3">Performance Scores</h4>
                      <div className="space-y-2">
                        {Object.entries(review.scores).map(([key, value]) => {
                          if (key === 'overallRating') return null
                          return (
                            <div key={key} className="flex justify-between items-center">
                              <span className="text-sm text-gray-600 capitalize">
                                {key.replace(/([A-Z])/g, ' $1').trim()}
                              </span>
                              <div className="flex items-center space-x-2">
                                <div className="flex">{renderStars(value)}</div>
                                <span className="text-sm font-medium">{value}/5</span>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-3">Overall Rating</h4>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-blue-600 mb-2">
                          {review.scores.overallRating}/5
                        </div>
                        <div className="flex justify-center mb-2">
                          {renderStars(review.scores.overallRating)}
                        </div>
                        <p className="text-sm text-gray-500">
                          Review Date: {formatDate(review.reviewDate)}
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-2 text-green-700">Strengths</h4>
                      <ul className="space-y-1">
                        {review.strengths.map((strength, index) => (
                          <li key={index} className="text-sm flex items-start">
                            <CheckCircle className="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                            {strength}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-2 text-yellow-700">Areas for Improvement</h4>
                      <ul className="space-y-1">
                        {review.areasForImprovement.map((area, index) => (
                          <li key={index} className="text-sm flex items-start">
                            <AlertCircle className="w-4 h-4 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
                            {area}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                  
                  <div className="mt-6">
                    <h4 className="font-medium mb-2 text-blue-700">Goals for Next Quarter</h4>
                    <ul className="space-y-1">
                      {review.goals.map((goal, index) => (
                        <li key={index} className="text-sm flex items-start">
                          <Target className="w-4 h-4 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                          {goal}
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-2">Mentor Comments</h4>
                    <p className="text-sm text-gray-700">{review.mentorComments}</p>
                  </div>
                  
                  {review.apprenticeComments && (
                    <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium mb-2">Apprentice Comments</h4>
                      <p className="text-sm text-gray-700">{review.apprenticeComments}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Exams Tab */}
        <TabsContent value="exams" className="space-y-6">
          <div className="space-y-4">
            {examResults.map((result) => (
              <Card key={result.id}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-medium">Exam {result.examId}</h3>
                    <div className="flex items-center space-x-2">
                      {result.passed ? (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      ) : (
                        <XCircle className="w-5 h-5 text-red-600" />
                      )}
                      <span className={`font-medium ${result.passed ? 'text-green-600' : 'text-red-600'}`}>
                        {result.passed ? 'Passed' : 'Failed'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Score</p>
                      <p className="font-medium text-lg">{result.score}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Time Spent</p>
                      <p className="font-medium">{result.timeSpent} minutes</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Completed</p>
                      <p className="font-medium">{formatDate(result.completedAt)}</p>
                    </div>
                  </div>
                  
                  {result.feedback && (
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-700">{result.feedback}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Timeline Tab */}
        <TabsContent value="timeline" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="w-5 h-5 mr-2" />
                Activity Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Timeline items would be generated from various activities */}
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Started apprenticeship program</p>
                    <p className="text-xs text-gray-500">{formatDate(apprentice.startDate)}</p>
                  </div>
                </div>
                
                {examResults.map((result) => (
                  <div key={result.id} className="flex items-center space-x-4">
                    <div className={`w-2 h-2 rounded-full ${result.passed ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">
                        {result.passed ? 'Passed' : 'Failed'} exam {result.examId}
                      </p>
                      <p className="text-xs text-gray-500">{formatDate(result.completedAt)}</p>
                    </div>
                  </div>
                ))}
                
                {reviews.map((review) => (
                  <div key={review.id} className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">
                        Q{review.quarter} {review.year} review completed
                      </p>
                      <p className="text-xs text-gray-500">{formatDate(review.reviewDate)}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}