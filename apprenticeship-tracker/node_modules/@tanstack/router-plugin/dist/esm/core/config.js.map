{"version": 3, "file": "config.js", "sources": ["../../../src/core/config.ts"], "sourcesContent": ["import { z } from 'zod'\nimport {\n  configSchema as generatorConfigSchema,\n  getConfig as getGeneratorConfig,\n} from '@tanstack/router-generator'\nimport type { RegisteredRouter, RouteIds } from '@tanstack/router-core'\nimport type { CodeSplitGroupings } from './constants'\n\nexport const splitGroupingsSchema = z\n  .array(\n    z.array(\n      z.union([\n        z.literal('loader'),\n        z.literal('component'),\n        z.literal('pendingComponent'),\n        z.literal('errorComponent'),\n        z.literal('notFoundComponent'),\n      ]),\n    ),\n    {\n      message:\n        \"  Must be an Array of Arrays containing the split groupings. i.e. [['component'], ['pendingComponent'], ['errorComponent', 'notFoundComponent']]\",\n    },\n  )\n  .superRefine((val, ctx) => {\n    const flattened = val.flat()\n    const unique = [...new Set(flattened)]\n\n    // Elements must be unique,\n    // ie. this shouldn't be allows [['component'], ['component', 'loader']]\n    if (unique.length !== flattened.length) {\n      ctx.addIssue({\n        code: 'custom',\n        message:\n          \"  Split groupings must be unique and not repeated. i.e. i.e. [['component'], ['pendingComponent'], ['errorComponent', 'notFoundComponent']].\" +\n          `\\n  You input was: ${JSON.stringify(val)}.`,\n      })\n    }\n  })\n\nexport type CodeSplittingOptions = {\n  /**\n   * Use this function to programmatically control the code splitting behavior\n   * based on the `routeId` for each route.\n   *\n   * If you just need to change the default behavior, you can use the `defaultBehavior` option.\n   * @param params\n   */\n  splitBehavior?: (params: {\n    routeId: RouteIds<RegisteredRouter['routeTree']>\n  }) => CodeSplitGroupings | undefined | void\n\n  /**\n   * The default/global configuration to control your code splitting behavior per route.\n   * @default [['component'],['pendingComponent'],['errorComponent'],['notFoundComponent']]\n   */\n  defaultBehavior?: CodeSplitGroupings\n\n  /**\n   * The nodes that shall be deleted from the route.\n   * @default undefined\n   */\n  deleteNodes?: Array<DeletableNodes>\n\n  /**\n   * @default true\n   */\n  addHmr?: boolean\n}\n\nconst DELETABLE_NODES = ['ssr'] as const\nexport const deletableNodesSchema = z.enum(DELETABLE_NODES)\nconst codeSplittingOptionsSchema = z.object({\n  splitBehavior: z.function().optional(),\n  defaultBehavior: splitGroupingsSchema.optional(),\n  deleteNodes: z.array(deletableNodesSchema).optional(),\n  addHmr: z.boolean().optional().default(true),\n})\nexport type DeletableNodes = (typeof DELETABLE_NODES)[number]\n\nexport const configSchema = generatorConfigSchema.extend({\n  enableRouteGeneration: z.boolean().optional(),\n  codeSplittingOptions: z\n    .custom<CodeSplittingOptions>((v) => {\n      return codeSplittingOptionsSchema.parse(v)\n    })\n    .optional(),\n  plugin: z\n    .object({\n      vite: z\n        .object({\n          environmentName: z.string().optional(),\n        })\n        .optional(),\n    })\n    .optional(),\n})\n\nexport const getConfig = (inlineConfig: Partial<Config>, root: string) => {\n  const config = getGeneratorConfig(inlineConfig, root)\n\n  return configSchema.parse({ ...config, ...inlineConfig })\n}\n\nexport type Config = z.infer<typeof configSchema>\nexport type ConfigInput = z.input<typeof configSchema>\nexport type ConfigOutput = z.output<typeof configSchema>\n"], "names": ["generatorConfigSchema", "getGeneratorConfig"], "mappings": ";;AAQO,MAAM,uBAAuB,EACjC;AAAA,EACC,EAAE;AAAA,IACA,EAAE,MAAM;AAAA,MACN,EAAE,QAAQ,QAAQ;AAAA,MAClB,EAAE,QAAQ,WAAW;AAAA,MACrB,EAAE,QAAQ,kBAAkB;AAAA,MAC5B,EAAE,QAAQ,gBAAgB;AAAA,MAC1B,EAAE,QAAQ,mBAAmB;AAAA,IAC9B,CAAA;AAAA,EACH;AAAA,EACA;AAAA,IACE,SACE;AAAA,EAAA;AAEN,EACC,YAAY,CAAC,KAAK,QAAQ;AACnB,QAAA,YAAY,IAAI,KAAK;AAC3B,QAAM,SAAS,CAAC,GAAG,IAAI,IAAI,SAAS,CAAC;AAIjC,MAAA,OAAO,WAAW,UAAU,QAAQ;AACtC,QAAI,SAAS;AAAA,MACX,MAAM;AAAA,MACN,SACE;AAAA,mBACsB,KAAK,UAAU,GAAG,CAAC;AAAA,IAAA,CAC5C;AAAA,EAAA;AAEL,CAAC;AAgCH,MAAM,kBAAkB,CAAC,KAAK;AACjB,MAAA,uBAAuB,EAAE,KAAK,eAAe;AAC1D,MAAM,6BAA6B,EAAE,OAAO;AAAA,EAC1C,eAAe,EAAE,SAAS,EAAE,SAAS;AAAA,EACrC,iBAAiB,qBAAqB,SAAS;AAAA,EAC/C,aAAa,EAAE,MAAM,oBAAoB,EAAE,SAAS;AAAA,EACpD,QAAQ,EAAE,QAAA,EAAU,SAAS,EAAE,QAAQ,IAAI;AAC7C,CAAC;AAGY,MAAA,eAAeA,eAAsB,OAAO;AAAA,EACvD,uBAAuB,EAAE,QAAQ,EAAE,SAAS;AAAA,EAC5C,sBAAsB,EACnB,OAA6B,CAAC,MAAM;AAC5B,WAAA,2BAA2B,MAAM,CAAC;AAAA,EAC1C,CAAA,EACA,SAAS;AAAA,EACZ,QAAQ,EACL,OAAO;AAAA,IACN,MAAM,EACH,OAAO;AAAA,MACN,iBAAiB,EAAE,OAAO,EAAE,SAAS;AAAA,IACtC,CAAA,EACA,SAAS;AAAA,EACb,CAAA,EACA,SAAS;AACd,CAAC;AAEY,MAAA,YAAY,CAAC,cAA+B,SAAiB;AAClE,QAAA,SAASC,YAAmB,cAAc,IAAI;AAEpD,SAAO,aAAa,MAAM,EAAE,GAAG,QAAQ,GAAG,cAAc;AAC1D;"}