{"version": 3, "file": "router-generator-plugin.js", "sources": ["../../../src/core/router-generator-plugin.ts"], "sourcesContent": ["import { isAbsolute, join, normalize } from 'node:path'\nimport { Generator, resolveConfigPath } from '@tanstack/router-generator'\nimport { getConfig } from './config'\n\nimport type { GeneratorEvent } from '@tanstack/router-generator'\nimport type { FSWatcher } from 'chokidar'\nimport type { UnpluginFactory } from 'unplugin'\nimport type { Config } from './config'\n\nconst PLUGIN_NAME = 'unplugin:router-generator'\n\nexport const unpluginRouterGeneratorFactory: UnpluginFactory<\n  Partial<Config> | undefined\n> = (options = {}) => {\n  const ROOT: string = process.cwd()\n  let userConfig = options as Config\n  let generator: Generator\n\n  const routeGenerationDisabled = () =>\n    userConfig.enableRouteGeneration === false\n  const getRoutesDirectoryPath = () => {\n    return isAbsolute(userConfig.routesDirectory)\n      ? userConfig.routesDirectory\n      : join(ROOT, userConfig.routesDirectory)\n  }\n\n  const initConfigAndGenerator = () => {\n    userConfig = getConfig(options, ROOT)\n    generator = new Generator({\n      config: userConfig,\n      root: ROOT,\n    })\n  }\n\n  const generate = async (opts?: {\n    file: string\n    event: 'create' | 'update' | 'delete'\n  }) => {\n    if (routeGenerationDisabled()) {\n      return\n    }\n    let generatorEvent: GeneratorEvent | undefined = undefined\n    if (opts) {\n      const filePath = normalize(opts.file)\n      if (filePath === resolveConfigPath({ configDirectory: ROOT })) {\n        initConfigAndGenerator()\n        return\n      }\n      generatorEvent = { path: filePath, type: opts.event }\n    }\n\n    try {\n      await generator.run(generatorEvent)\n      globalThis.TSR_ROUTES_BY_ID_MAP = generator.getRoutesByFileMap()\n    } catch (e) {\n      console.error(e)\n    }\n  }\n\n  return {\n    name: 'tanstack:router-generator',\n    enforce: 'pre',\n    async watchChange(id, { event }) {\n      await generate({\n        file: id,\n        event,\n      })\n    },\n    async buildStart() {\n      await generate()\n    },\n    vite: {\n      configResolved() {\n        initConfigAndGenerator()\n      },\n      applyToEnvironment(environment) {\n        if (userConfig.plugin?.vite?.environmentName) {\n          return userConfig.plugin.vite.environmentName === environment.name\n        }\n        return true\n      },\n      async buildStart() {\n        await generate()\n      },\n      sharedDuringBuild: true,\n    },\n    rspack(compiler) {\n      initConfigAndGenerator()\n\n      let handle: FSWatcher | null = null\n\n      compiler.hooks.beforeRun.tapPromise(PLUGIN_NAME, () => generate())\n\n      compiler.hooks.watchRun.tapPromise(PLUGIN_NAME, async () => {\n        if (handle) {\n          return\n        }\n\n        // rspack watcher doesn't register newly created files\n        const routesDirectoryPath = getRoutesDirectoryPath()\n        const chokidar = await import('chokidar')\n        handle = chokidar\n          .watch(routesDirectoryPath, { ignoreInitial: true })\n          .on('add', (file) => generate({ file, event: 'create' }))\n\n        await generate()\n      })\n\n      compiler.hooks.watchClose.tap(PLUGIN_NAME, async () => {\n        if (handle) {\n          await handle.close()\n        }\n      })\n    },\n    webpack(compiler) {\n      initConfigAndGenerator()\n\n      let handle: FSWatcher | null = null\n\n      compiler.hooks.beforeRun.tapPromise(PLUGIN_NAME, () => generate())\n\n      compiler.hooks.watchRun.tapPromise(PLUGIN_NAME, async () => {\n        if (handle) {\n          return\n        }\n\n        // webpack watcher doesn't register newly created files\n        const routesDirectoryPath = getRoutesDirectoryPath()\n        const chokidar = await import('chokidar')\n        handle = chokidar\n          .watch(routesDirectoryPath, { ignoreInitial: true })\n          .on('add', (file) => generate({ file, event: 'create' }))\n\n        await generate()\n      })\n\n      compiler.hooks.watchClose.tap(PLUGIN_NAME, async () => {\n        if (handle) {\n          await handle.close()\n        }\n      })\n\n      compiler.hooks.done.tap(PLUGIN_NAME, () => {\n        console.info('✅ ' + PLUGIN_NAME + ': route-tree generation done')\n      })\n    },\n  }\n}\n"], "names": [], "mappings": ";;;AASA,MAAM,cAAc;AAEb,MAAM,iCAET,CAAC,UAAU,OAAO;AACd,QAAA,OAAe,QAAQ,IAAI;AACjC,MAAI,aAAa;AACb,MAAA;AAEE,QAAA,0BAA0B,MAC9B,WAAW,0BAA0B;AACvC,QAAM,yBAAyB,MAAM;AAC5B,WAAA,WAAW,WAAW,eAAe,IACxC,WAAW,kBACX,KAAK,MAAM,WAAW,eAAe;AAAA,EAC3C;AAEA,QAAM,yBAAyB,MAAM;AACtB,iBAAA,UAAU,SAAS,IAAI;AACpC,gBAAY,IAAI,UAAU;AAAA,MACxB,QAAQ;AAAA,MACR,MAAM;AAAA,IAAA,CACP;AAAA,EACH;AAEM,QAAA,WAAW,OAAO,SAGlB;AACJ,QAAI,2BAA2B;AAC7B;AAAA,IAAA;AAEF,QAAI,iBAA6C;AACjD,QAAI,MAAM;AACF,YAAA,WAAW,UAAU,KAAK,IAAI;AACpC,UAAI,aAAa,kBAAkB,EAAE,iBAAiB,KAAM,CAAA,GAAG;AACtC,+BAAA;AACvB;AAAA,MAAA;AAEF,uBAAiB,EAAE,MAAM,UAAU,MAAM,KAAK,MAAM;AAAA,IAAA;AAGlD,QAAA;AACI,YAAA,UAAU,IAAI,cAAc;AACvB,iBAAA,uBAAuB,UAAU,mBAAmB;AAAA,aACxD,GAAG;AACV,cAAQ,MAAM,CAAC;AAAA,IAAA;AAAA,EAEnB;AAEO,SAAA;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM,YAAY,IAAI,EAAE,SAAS;AAC/B,YAAM,SAAS;AAAA,QACb,MAAM;AAAA,QACN;AAAA,MAAA,CACD;AAAA,IACH;AAAA,IACA,MAAM,aAAa;AACjB,YAAM,SAAS;AAAA,IACjB;AAAA,IACA,MAAM;AAAA,MACJ,iBAAiB;AACQ,+BAAA;AAAA,MACzB;AAAA,MACA,mBAAmB,aAAa;;AAC1B,aAAA,sBAAW,WAAX,mBAAmB,SAAnB,mBAAyB,iBAAiB;AAC5C,iBAAO,WAAW,OAAO,KAAK,oBAAoB,YAAY;AAAA,QAAA;AAEzD,eAAA;AAAA,MACT;AAAA,MACA,MAAM,aAAa;AACjB,cAAM,SAAS;AAAA,MACjB;AAAA,MACA,mBAAmB;AAAA,IACrB;AAAA,IACA,OAAO,UAAU;AACQ,6BAAA;AAEvB,UAAI,SAA2B;AAE/B,eAAS,MAAM,UAAU,WAAW,aAAa,MAAM,UAAU;AAEjE,eAAS,MAAM,SAAS,WAAW,aAAa,YAAY;AAC1D,YAAI,QAAQ;AACV;AAAA,QAAA;AAIF,cAAM,sBAAsB,uBAAuB;AAC7C,cAAA,WAAW,MAAM,OAAO,UAAU;AACxC,iBAAS,SACN,MAAM,qBAAqB,EAAE,eAAe,KAAA,CAAM,EAClD,GAAG,OAAO,CAAC,SAAS,SAAS,EAAE,MAAM,OAAO,SAAA,CAAU,CAAC;AAE1D,cAAM,SAAS;AAAA,MAAA,CAChB;AAED,eAAS,MAAM,WAAW,IAAI,aAAa,YAAY;AACrD,YAAI,QAAQ;AACV,gBAAM,OAAO,MAAM;AAAA,QAAA;AAAA,MACrB,CACD;AAAA,IACH;AAAA,IACA,QAAQ,UAAU;AACO,6BAAA;AAEvB,UAAI,SAA2B;AAE/B,eAAS,MAAM,UAAU,WAAW,aAAa,MAAM,UAAU;AAEjE,eAAS,MAAM,SAAS,WAAW,aAAa,YAAY;AAC1D,YAAI,QAAQ;AACV;AAAA,QAAA;AAIF,cAAM,sBAAsB,uBAAuB;AAC7C,cAAA,WAAW,MAAM,OAAO,UAAU;AACxC,iBAAS,SACN,MAAM,qBAAqB,EAAE,eAAe,KAAA,CAAM,EAClD,GAAG,OAAO,CAAC,SAAS,SAAS,EAAE,MAAM,OAAO,SAAA,CAAU,CAAC;AAE1D,cAAM,SAAS;AAAA,MAAA,CAChB;AAED,eAAS,MAAM,WAAW,IAAI,aAAa,YAAY;AACrD,YAAI,QAAQ;AACV,gBAAM,OAAO,MAAM;AAAA,QAAA;AAAA,MACrB,CACD;AAED,eAAS,MAAM,KAAK,IAAI,aAAa,MAAM;AACjC,gBAAA,KAAK,OAAO,cAAc,8BAA8B;AAAA,MAAA,CACjE;AAAA,IAAA;AAAA,EAEL;AACF;"}