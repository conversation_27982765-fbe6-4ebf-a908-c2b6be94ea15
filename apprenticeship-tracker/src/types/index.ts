export interface Apprentice {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  startDate: string
  endDate: string
  department: string
  departmentId: string
  mentor: string
  mentorId: string
  position: string
  status: 'active' | 'completed' | 'suspended' | 'withdrawn'
  avatar?: string
  overallProgress: number
  emergencyContact?: {
    name: string
    phone: string
    relationship: string
  }
  address?: {
    street: string
    city: string
    postcode: string
    country: string
  }
  qualifications?: string[]
  contractType: 'full-time' | 'part-time'
  salary?: number
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface QuarterlyReview {
  id: string
  apprenticeId: string
  quarter: 1 | 2 | 3 | 4
  year: number
  reviewDate: string
  mentorId: string
  mentorName: string
  scores: {
    technicalSkills: number
    communication: number
    teamwork: number
    initiative: number
    punctuality: number
    overallRating: number
  }
  strengths: string[]
  areasForImprovement: string[]
  goals: string[]
  mentorComments: string
  apprenticeComments?: string
  status: 'scheduled' | 'in-progress' | 'completed' | 'overdue'
}

export interface Exam {
  id: string
  title: string
  description: string
  type: 'written' | 'practical' | 'oral' | 'project'
  subject: string
  maxScore: number
  passingScore: number
  duration: number // in minutes
  scheduledDate: string
  status: 'upcoming' | 'active' | 'completed' | 'cancelled'
  instructions?: string
}

export interface ExamResult {
  id: string
  examId: string
  apprenticeId: string
  score: number
  completedAt: string
  timeSpent: number // in minutes
  feedback?: string
  passed: boolean
  retakeAllowed: boolean
  retakeCount: number
}

export interface ProgressModule {
  id: string
  title: string
  description: string
  category: string
  requiredHours: number
  order: number
  prerequisites: string[]
  isRequired: boolean
}

export interface ApprenticeProgress {
  id: string
  apprenticeId: string
  moduleId: string
  hoursCompleted: number
  status: 'not-started' | 'in-progress' | 'completed' | 'overdue'
  startDate?: string
  completedDate?: string
  notes?: string
  mentorApproved: boolean
}

export interface Mentor {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  department: string
  departmentId: string
  specialization: string[]
  apprentices: string[]
  position: string
  yearsOfExperience: number
  qualifications: string[]
  maxApprentices: number
  avatar?: string
  bio?: string
  linkedIn?: string
  createdAt: string
  updatedAt: string
}

export interface DashboardStats {
  totalApprentices: number
  activeApprentices: number
  completedApprentices: number
  averageProgress: number
  upcomingReviews: number
  overdueReviews: number
  upcomingExams: number
  passRate: number
}

export type SortDirection = 'asc' | 'desc'

export interface FilterOptions {
  status?: Apprentice['status'][]
  department?: string[]
  mentor?: string[]
  quarter?: number[]
  year?: number[]
}

export interface PaginationInfo {
  page: number
  pageSize: number
  totalItems: number
  totalPages: number
}

// New types for enhanced features
export interface Department {
  id: string
  name: string
  description: string
  manager: string
  managerId: string
  budget: number
  totalApprentices: number
  activeApprentices: number
  location: string
  createdAt: string
  updatedAt: string
}

export interface Notification {
  id: string
  userId: string
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  actionUrl?: string
  actionText?: string
  isRead: boolean
  createdAt: string
  expiresAt?: string
}

export interface CalendarEvent {
  id: string
  title: string
  description?: string
  type: 'review' | 'exam' | 'meeting' | 'training' | 'deadline'
  startDate: string
  endDate?: string
  location?: string
  participants: string[]
  apprenticeId?: string
  mentorId?: string
  isRecurring: boolean
  recurrenceRule?: string
  reminders: number[] // minutes before event
  status: 'scheduled' | 'confirmed' | 'cancelled' | 'completed'
  createdAt: string
  updatedAt: string
}

export interface ExamQuestion {
  id: string
  examId: string
  question: string
  type: 'multiple-choice' | 'short-answer' | 'essay' | 'code'
  options?: string[]
  correctAnswer?: string | number
  points: number
  order: number
  explanation?: string
}

export interface ExamSubmission {
  id: string
  examId: string
  apprenticeId: string
  questionId: string
  answer: string
  isCorrect?: boolean
  pointsEarned: number
  submittedAt: string
}

export interface AuditLog {
  id: string
  userId: string
  userType: 'apprentice' | 'mentor' | 'admin'
  action: string
  resourceType: string
  resourceId: string
  changes?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  timestamp: string
}

export interface AnalyticsData {
  apprenticeProgressTrends: {
    date: string
    averageProgress: number
    activeApprentices: number
  }[]
  departmentPerformance: {
    department: string
    averageProgress: number
    completionRate: number
    retentionRate: number
  }[]
  examPerformance: {
    examId: string
    examTitle: string
    averageScore: number
    passRate: number
    totalAttempts: number
  }[]
  mentorEffectiveness: {
    mentorId: string
    mentorName: string
    averageApprenticeProgress: number
    apprenticeCount: number
    reviewRating: number
  }[]
  monthlyStats: {
    month: string
    newApprentices: number
    completedApprentices: number
    examsCompleted: number
    reviewsCompleted: number
  }[]
}

export interface FileUpload {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  uploadedBy: string
  uploadedAt: string
  category: 'document' | 'image' | 'certificate' | 'report'
  relatedTo?: {
    type: 'apprentice' | 'review' | 'exam' | 'progress'
    id: string
  }
  url: string
  isPublic: boolean
}

export interface BulkOperation {
  id: string
  type: 'import' | 'export' | 'update' | 'delete'
  resourceType: string
  status: 'pending' | 'in-progress' | 'completed' | 'failed'
  totalRecords: number
  processedRecords: number
  failedRecords: number
  errors?: string[]
  fileUrl?: string
  createdBy: string
  createdAt: string
  completedAt?: string
}

export interface SearchFilters {
  status?: string[]
  department?: string[]
  mentor?: string[]
  quarter?: number[]
  year?: number[]
  progress?: {
    min?: number
    max?: number
  }
  dateRange?: {
    start?: string
    end?: string
  }
  searchTerm?: string
}

export interface SortOptions {
  field: string
  direction: 'asc' | 'desc'
}

export interface APIResponse<T> {
  data: T
  message?: string
  pagination?: PaginationInfo
  filters?: SearchFilters
  sort?: SortOptions
  timestamp: string
}