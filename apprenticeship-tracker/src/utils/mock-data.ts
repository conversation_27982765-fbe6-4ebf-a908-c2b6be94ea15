import { 
  Apprentice, 
  QuarterlyReview, 
  Exam, 
  ExamResult, 
  ProgressModule, 
  ApprenticeProgress, 
  Mentor,
  Department,
  Notification,
  CalendarEvent,
  ExamQuestion,
  ExamSubmission,
  AuditLog,
  AnalyticsData,
  FileUpload,
  BulkOperation
} from '../types'

// Helper function to generate realistic dates
const generateDate = (daysAgo: number): string => {
  const date = new Date()
  date.setDate(date.getDate() - daysAgo)
  return date.toISOString().split('T')[0]
}

const generateDateTime = (daysAgo: number, hour: number = 9): string => {
  const date = new Date()
  date.setDate(date.getDate() - daysAgo)
  date.setHours(hour, 0, 0, 0)
  return date.toISOString()
}

// Mock Departments
export const mockDepartments: Department[] = [
  {
    id: '1',
    name: 'Engineering',
    description: 'Software development, DevOps, and technical innovation',
    manager: '<PERSON>',
    managerId: 'mgr-1',
    budget: 250000,
    totalApprentices: 8,
    activeApprentices: 6,
    location: 'London Office - Floor 3',
    createdAt: generateDateTime(365),
    updatedAt: generateDateTime(30)
  },
  {
    id: '2',
    name: 'Marketing',
    description: 'Digital marketing, content creation, and brand management',
    manager: '<PERSON>',
    managerId: 'mgr-2',
    budget: 150000,
    totalApprentices: 5,
    activeApprentices: 4,
    location: 'Manchester Office - Floor 2',
    createdAt: generateDateTime(365),
    updatedAt: generateDateTime(15)
  },
  {
    id: '3',
    name: 'HR',
    description: 'Human resources, recruitment, and employee development',
    manager: 'David Brown',
    managerId: 'mgr-3',
    budget: 100000,
    totalApprentices: 3,
    activeApprentices: 3,
    location: 'Birmingham Office - Floor 1',
    createdAt: generateDateTime(365),
    updatedAt: generateDateTime(7)
  },
  {
    id: '4',
    name: 'Finance',
    description: 'Financial analysis, accounting, and business intelligence',
    manager: 'Lisa Johnson',
    managerId: 'mgr-4',
    budget: 120000,
    totalApprentices: 4,
    activeApprentices: 3,
    location: 'London Office - Floor 2',
    createdAt: generateDateTime(365),
    updatedAt: generateDateTime(20)
  }
]

export const mockApprentices: Apprentice[] = [
  {
    id: '1',
    firstName: 'John',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '+44 7123 456789',
    startDate: '2024-01-15',
    endDate: '2025-01-15',
    department: 'Engineering',
    departmentId: '1',
    mentor: 'Sarah Johnson',
    mentorId: '1',
    position: 'Software Development Apprentice',
    status: 'active',
    overallProgress: 65,
    emergencyContact: {
      name: 'Mary Smith',
      phone: '+44 7111 222333',
      relationship: 'Mother'
    },
    address: {
      street: '123 High Street',
      city: 'London',
      postcode: 'SW1A 1AA',
      country: 'UK'
    },
    qualifications: ['A-Level Mathematics', 'A-Level Computer Science', 'BTEC IT'],
    contractType: 'full-time',
    salary: 18000,
    notes: 'Shows strong aptitude for programming. Needs improvement in communication skills.',
    avatar: '/avatars/john-smith.jpg',
    createdAt: generateDateTime(180),
    updatedAt: generateDateTime(5)
  },
  {
    id: '2',
    firstName: 'Emma',
    lastName: 'Wilson',
    email: '<EMAIL>',
    phone: '+44 7234 567890',
    startDate: '2024-02-01',
    endDate: '2025-02-01',
    department: 'Marketing',
    departmentId: '2',
    mentor: 'Mike Brown',
    mentorId: '2',
    position: 'Digital Marketing Apprentice',
    status: 'active',
    overallProgress: 78,
    emergencyContact: {
      name: 'Robert Wilson',
      phone: '+44 7222 333444',
      relationship: 'Father'
    },
    address: {
      street: '456 Market Road',
      city: 'Manchester',
      postcode: 'M1 2AB',
      country: 'UK'
    },
    qualifications: ['A-Level English Literature', 'A-Level Media Studies', 'Marketing Foundation'],
    contractType: 'full-time',
    salary: 17500,
    notes: 'Excellent creative skills and social media knowledge. Very proactive.',
    avatar: '/avatars/emma-wilson.jpg',
    createdAt: generateDateTime(165),
    updatedAt: generateDateTime(2)
  },
  {
    id: '3',
    firstName: 'James',
    lastName: 'Davis',
    email: '<EMAIL>',
    phone: '+44 7345 678901',
    startDate: '2023-09-01',
    endDate: '2024-09-01',
    department: 'Engineering',
    departmentId: '1',
    mentor: 'Sarah Johnson',
    mentorId: '1',
    position: 'Data Analytics Apprentice',
    status: 'completed',
    overallProgress: 100,
    emergencyContact: {
      name: 'Linda Davis',
      phone: '+44 7333 444555',
      relationship: 'Mother'
    },
    address: {
      street: '789 Oak Avenue',
      city: 'Birmingham',
      postcode: 'B1 3CD',
      country: 'UK'
    },
    qualifications: ['A-Level Mathematics', 'A-Level Physics', 'Statistics Certificate'],
    contractType: 'full-time',
    salary: 19000,
    notes: 'Outstanding analytical skills. Completed apprenticeship with distinction.',
    avatar: '/avatars/james-davis.jpg',
    createdAt: generateDateTime(400),
    updatedAt: generateDateTime(30)
  },
  {
    id: '4',
    firstName: 'Sophie',
    lastName: 'Taylor',
    email: '<EMAIL>',
    phone: '+44 7456 789012',
    startDate: '2024-03-01',
    endDate: '2025-03-01',
    department: 'HR',
    departmentId: '3',
    mentor: 'Lisa Green',
    mentorId: '3',
    position: 'HR Administration Apprentice',
    status: 'active',
    overallProgress: 45,
    emergencyContact: {
      name: 'Michael Taylor',
      phone: '+44 7444 555666',
      relationship: 'Father'
    },
    address: {
      street: '321 Rose Street',
      city: 'Leeds',
      postcode: 'LS1 4EF',
      country: 'UK'
    },
    qualifications: ['A-Level Business Studies', 'A-Level Psychology', 'HR Foundation'],
    contractType: 'full-time',
    salary: 16500,
    notes: 'Strong interpersonal skills. Good attention to detail.',
    avatar: '/avatars/sophie-taylor.jpg',
    createdAt: generateDateTime(135),
    updatedAt: generateDateTime(1)
  },
  {
    id: '5',
    firstName: 'Oliver',
    lastName: 'Thompson',
    email: '<EMAIL>',
    phone: '+44 7567 890123',
    startDate: '2024-04-01',
    endDate: '2025-04-01',
    department: 'Marketing',
    departmentId: '2',
    mentor: 'Mike Brown',
    mentorId: '2',
    position: 'Content Marketing Apprentice',
    status: 'active',
    overallProgress: 35,
    emergencyContact: {
      name: 'Sarah Thompson',
      phone: '+44 7555 666777',
      relationship: 'Mother'
    },
    address: {
      street: '654 Park Lane',
      city: 'Bristol',
      postcode: 'BS1 5GH',
      country: 'UK'
    },
    qualifications: ['A-Level English Language', 'A-Level Art', 'Creative Writing Certificate'],
    contractType: 'full-time',
    salary: 17000,
    notes: 'Creative writer with good social media instincts. Still developing technical skills.',
    avatar: '/avatars/oliver-thompson.jpg',
    createdAt: generateDateTime(105),
    updatedAt: generateDateTime(3)
  },
  {
    id: '6',
    firstName: 'Charlotte',
    lastName: 'Anderson',
    email: '<EMAIL>',
    phone: '+44 7678 901234',
    startDate: '2024-01-08',
    endDate: '2025-01-08',
    department: 'HR',
    departmentId: '3',
    mentor: 'Lisa Green',
    mentorId: '3',
    position: 'Recruitment Apprentice',
    status: 'active',
    overallProgress: 68,
    emergencyContact: {
      name: 'David Anderson',
      phone: '+44 7666 777888',
      relationship: 'Father'
    },
    address: {
      street: '987 Victoria Road',
      city: 'Newcastle',
      postcode: 'NE1 6IJ',
      country: 'UK'
    },
    qualifications: ['A-Level Psychology', 'A-Level Sociology', 'Recruitment Foundation'],
    contractType: 'full-time',
    salary: 17200,
    notes: 'Excellent people skills. Shows promise in talent acquisition.',
    avatar: '/avatars/charlotte-anderson.jpg',
    createdAt: generateDateTime(187),
    updatedAt: generateDateTime(4)
  },
  {
    id: '7',
    firstName: 'William',
    lastName: 'Roberts',
    email: '<EMAIL>',
    phone: '+44 7789 012345',
    startDate: '2024-02-15',
    endDate: '2025-02-15',
    department: 'Finance',
    departmentId: '4',
    mentor: 'James Wilson',
    mentorId: '4',
    position: 'Financial Analysis Apprentice',
    status: 'active',
    overallProgress: 58,
    emergencyContact: {
      name: 'Emma Roberts',
      phone: '+44 7777 888999',
      relationship: 'Sister'
    },
    address: {
      street: '147 Church Street',
      city: 'Liverpool',
      postcode: 'L1 7KL',
      country: 'UK'
    },
    qualifications: ['A-Level Mathematics', 'A-Level Economics', 'AAT Level 2'],
    contractType: 'full-time',
    salary: 18500,
    notes: 'Strong numerical skills. Good understanding of financial principles.',
    avatar: '/avatars/william-roberts.jpg',
    createdAt: generateDateTime(152),
    updatedAt: generateDateTime(6)
  },
  {
    id: '8',
    firstName: 'Amelia',
    lastName: 'Evans',
    email: '<EMAIL>',
    phone: '+44 7890 123456',
    startDate: '2024-03-15',
    endDate: '2025-03-15',
    department: 'Finance',
    departmentId: '4',
    mentor: 'James Wilson',
    mentorId: '4',
    position: 'Business Intelligence Apprentice',
    status: 'active',
    overallProgress: 42,
    emergencyContact: {
      name: 'Thomas Evans',
      phone: '+44 7888 999000',
      relationship: 'Father'
    },
    address: {
      street: '258 Queen Street',
      city: 'Edinburgh',
      postcode: 'EH1 8MN',
      country: 'UK'
    },
    qualifications: ['A-Level Mathematics', 'A-Level Computer Science', 'Excel Advanced'],
    contractType: 'full-time',
    salary: 18200,
    notes: 'Strong analytical mindset. Interested in data visualization.',
    avatar: '/avatars/amelia-evans.jpg',
    createdAt: generateDateTime(122),
    updatedAt: generateDateTime(7)
  },
  {
    id: '9',
    firstName: 'Harry',
    lastName: 'Clark',
    email: '<EMAIL>',
    phone: '+44 7901 234567',
    startDate: '2024-04-15',
    endDate: '2025-04-15',
    department: 'Engineering',
    departmentId: '1',
    mentor: 'Emma Davis',
    mentorId: '5',
    position: 'DevOps Apprentice',
    status: 'active',
    overallProgress: 28,
    emergencyContact: {
      name: 'Jane Clark',
      phone: '+44 7999 000111',
      relationship: 'Mother'
    },
    address: {
      street: '369 Mill Lane',
      city: 'Cardiff',
      postcode: 'CF1 9OP',
      country: 'UK'
    },
    qualifications: ['A-Level Computer Science', 'A-Level Physics', 'Linux Essentials'],
    contractType: 'full-time',
    salary: 18800,
    notes: 'Keen interest in automation and cloud technologies.',
    avatar: '/avatars/harry-clark.jpg',
    createdAt: generateDateTime(92),
    updatedAt: generateDateTime(8)
  },
  {
    id: '10',
    firstName: 'Isla',
    lastName: 'Walker',
    email: '<EMAIL>',
    phone: '+44 7012 345678',
    startDate: '2024-05-01',
    endDate: '2025-05-01',
    department: 'Engineering',
    departmentId: '1',
    mentor: 'Emma Davis',
    mentorId: '5',
    position: 'Cloud Infrastructure Apprentice',
    status: 'active',
    overallProgress: 15,
    emergencyContact: {
      name: 'Mark Walker',
      phone: '+44 7000 111222',
      relationship: 'Father'
    },
    address: {
      street: '741 Forest Road',
      city: 'Glasgow',
      postcode: 'G1 0QR',
      country: 'UK'
    },
    qualifications: ['A-Level Computer Science', 'A-Level Mathematics', 'AWS Cloud Practitioner'],
    contractType: 'full-time',
    salary: 18000,
    notes: 'Just started but shows enthusiasm for cloud technologies.',
    avatar: '/avatars/isla-walker.jpg',
    createdAt: generateDateTime(77),
    updatedAt: generateDateTime(9)
  },
  {
    id: '11',
    firstName: 'George',
    lastName: 'Mitchell',
    email: '<EMAIL>',
    phone: '+44 7123 456000',
    startDate: '2023-06-01',
    endDate: '2024-06-01',
    department: 'Marketing',
    departmentId: '2',
    mentor: 'Mike Brown',
    mentorId: '2',
    position: 'SEO Apprentice',
    status: 'completed',
    overallProgress: 100,
    emergencyContact: {
      name: 'Helen Mitchell',
      phone: '+44 7111 222000',
      relationship: 'Mother'
    },
    address: {
      street: '852 High Road',
      city: 'Portsmouth',
      postcode: 'PO1 1ST',
      country: 'UK'
    },
    qualifications: ['A-Level English Language', 'A-Level Business Studies', 'Google Analytics Certified'],
    contractType: 'full-time',
    salary: 19500,
    notes: 'Completed apprenticeship successfully. Now working as junior SEO specialist.',
    avatar: '/avatars/george-mitchell.jpg',
    createdAt: generateDateTime(455),
    updatedAt: generateDateTime(60)
  },
  {
    id: '12',
    firstName: 'Grace',
    lastName: 'Turner',
    email: '<EMAIL>',
    phone: '+44 7234 567111',
    startDate: '2024-06-01',
    endDate: '2025-06-01',
    department: 'HR',
    departmentId: '3',
    mentor: 'Lisa Green',
    mentorId: '3',
    position: 'Learning & Development Apprentice',
    status: 'active',
    overallProgress: 8,
    emergencyContact: {
      name: 'Paul Turner',
      phone: '+44 7222 333111',
      relationship: 'Father'
    },
    address: {
      street: '963 Cedar Avenue',
      city: 'Nottingham',
      postcode: 'NG1 2UV',
      country: 'UK'
    },
    qualifications: ['A-Level Psychology', 'A-Level English Literature', 'First Aid Certificate'],
    contractType: 'full-time',
    salary: 17800,
    notes: 'Recently started. Interested in employee development and training.',
    avatar: '/avatars/grace-turner.jpg',
    createdAt: generateDateTime(47),
    updatedAt: generateDateTime(10)
  }
]

export const mockMentors: Mentor[] = [
  {
    id: '1',
    firstName: 'Sarah',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phone: '+44 7123 456789',
    department: 'Engineering',
    departmentId: '1',
    specialization: ['Software Development', 'Data Analytics', 'React', 'Node.js'],
    apprentices: ['1', '3'],
    position: 'Senior Software Engineer',
    yearsOfExperience: 8,
    qualifications: ['BSc Computer Science', 'AWS Certified Solutions Architect', 'Scrum Master'],
    maxApprentices: 3,
    avatar: '/avatars/sarah-johnson.jpg',
    bio: 'Passionate about mentoring junior developers and building scalable web applications.',
    linkedIn: 'https://linkedin.com/in/sarah-johnson-dev',
    createdAt: generateDateTime(200),
    updatedAt: generateDateTime(5)
  },
  {
    id: '2',
    firstName: 'Mike',
    lastName: 'Brown',
    email: '<EMAIL>',
    phone: '+44 7234 567890',
    department: 'Marketing',
    departmentId: '2',
    specialization: ['Digital Marketing', 'Content Strategy', 'SEO', 'Social Media'],
    apprentices: ['2', '5'],
    position: 'Marketing Manager',
    yearsOfExperience: 6,
    qualifications: ['BA Marketing', 'Google Analytics Certified', 'HubSpot Certified'],
    maxApprentices: 2,
    avatar: '/avatars/mike-brown.jpg',
    bio: 'Expert in digital marketing strategies and content creation with a focus on data-driven results.',
    linkedIn: 'https://linkedin.com/in/mike-brown-marketing',
    createdAt: generateDateTime(180),
    updatedAt: generateDateTime(10)
  },
  {
    id: '3',
    firstName: 'Lisa',
    lastName: 'Green',
    email: '<EMAIL>',
    phone: '+44 7345 678901',
    department: 'HR',
    departmentId: '3',
    specialization: ['HR Administration', 'Recruitment', 'Employee Relations', 'Training'],
    apprentices: ['4', '6'],
    position: 'HR Business Partner',
    yearsOfExperience: 10,
    qualifications: ['MSc Human Resources', 'CIPD Level 7', 'Certified Recruiter'],
    maxApprentices: 3,
    avatar: '/avatars/lisa-green.jpg',
    bio: 'Dedicated HR professional with expertise in talent acquisition and employee development.',
    linkedIn: 'https://linkedin.com/in/lisa-green-hr',
    createdAt: generateDateTime(220),
    updatedAt: generateDateTime(3)
  },
  {
    id: '4',
    firstName: 'James',
    lastName: 'Wilson',
    email: '<EMAIL>',
    phone: '+44 7456 789012',
    department: 'Finance',
    departmentId: '4',
    specialization: ['Financial Analysis', 'Business Intelligence', 'Excel', 'SQL'],
    apprentices: ['7', '8'],
    position: 'Senior Financial Analyst',
    yearsOfExperience: 7,
    qualifications: ['BSc Finance', 'CFA Level 2', 'Advanced Excel Certified'],
    maxApprentices: 2,
    avatar: '/avatars/james-wilson.jpg',
    bio: 'Financial analyst with strong analytical skills and expertise in business intelligence.',
    linkedIn: 'https://linkedin.com/in/james-wilson-finance',
    createdAt: generateDateTime(190),
    updatedAt: generateDateTime(8)
  },
  {
    id: '5',
    firstName: 'Emma',
    lastName: 'Davis',
    email: '<EMAIL>',
    phone: '+44 7567 890123',
    department: 'Engineering',
    departmentId: '1',
    specialization: ['DevOps', 'Cloud Architecture', 'Docker', 'Kubernetes'],
    apprentices: ['9', '10'],
    position: 'DevOps Engineer',
    yearsOfExperience: 5,
    qualifications: ['BSc Software Engineering', 'AWS Certified DevOps', 'Docker Certified'],
    maxApprentices: 2,
    avatar: '/avatars/emma-davis.jpg',
    bio: 'DevOps specialist focused on automation and cloud infrastructure.',
    linkedIn: 'https://linkedin.com/in/emma-davis-devops',
    createdAt: generateDateTime(150),
    updatedAt: generateDateTime(12)
  }
]

export const mockQuarterlyReviews: QuarterlyReview[] = [
  {
    id: '1',
    apprenticeId: '1',
    quarter: 2,
    year: 2024,
    reviewDate: '2024-06-15',
    mentorId: '1',
    mentorName: 'Sarah Johnson',
    scores: {
      technicalSkills: 7,
      communication: 8,
      teamwork: 9,
      initiative: 6,
      punctuality: 9,
      overallRating: 7.8
    },
    strengths: ['Great teamwork', 'Excellent punctuality', 'Good communication skills'],
    areasForImprovement: ['Technical skills development', 'Taking more initiative'],
    goals: ['Complete React certification', 'Lead a small project'],
    mentorComments: 'John is progressing well and shows great potential.',
    apprenticeComments: 'I feel more confident with React now and want to take on more challenging projects.',
    status: 'completed'
  },
  {
    id: '2',
    apprenticeId: '2',
    quarter: 3,
    year: 2024,
    reviewDate: '2024-09-15',
    mentorId: '2',
    mentorName: 'Mike Brown',
    scores: {
      technicalSkills: 8,
      communication: 9,
      teamwork: 8,
      initiative: 8,
      punctuality: 7,
      overallRating: 8.0
    },
    strengths: ['Excellent communication', 'Creative thinking', 'Strong initiative'],
    areasForImprovement: ['Time management', 'Technical proficiency'],
    goals: ['Improve punctuality', 'Complete Google Analytics certification'],
    mentorComments: 'Emma shows excellent potential in marketing.',
    apprenticeComments: 'I enjoy the creative aspects of marketing and want to learn more about analytics.',
    status: 'completed'
  },
  {
    id: '3',
    apprenticeId: '3',
    quarter: 4,
    year: 2024,
    reviewDate: '2024-12-15',
    mentorId: '1',
    mentorName: 'Sarah Johnson',
    scores: {
      technicalSkills: 9,
      communication: 9,
      teamwork: 9,
      initiative: 9,
      punctuality: 10,
      overallRating: 9.2
    },
    strengths: ['Exceptional technical skills', 'Leadership qualities', 'Excellent problem-solving'],
    areasForImprovement: ['Continue professional development'],
    goals: ['Pursue advanced data science certification', 'Mentor junior apprentices'],
    mentorComments: 'Outstanding performance throughout the apprenticeship. Ready for full-time role.',
    apprenticeComments: 'I am grateful for the learning opportunities and feel ready for the next challenge.',
    status: 'completed'
  },
  {
    id: '4',
    apprenticeId: '4',
    quarter: 1,
    year: 2024,
    reviewDate: '2024-03-15',
    mentorId: '3',
    mentorName: 'Lisa Green',
    scores: {
      technicalSkills: 6,
      communication: 8,
      teamwork: 7,
      initiative: 5,
      punctuality: 8,
      overallRating: 6.8
    },
    strengths: ['Good communication skills', 'Reliable and punctual', 'Eager to learn'],
    areasForImprovement: ['Technical skills in HRIS', 'Taking initiative on projects'],
    goals: ['Complete CIPD Level 3', 'Shadow senior HR team members'],
    mentorComments: 'Sophie is developing well but needs to be more proactive.',
    apprenticeComments: 'I want to learn more about HR systems and processes.',
    status: 'completed'
  },
  {
    id: '5',
    apprenticeId: '5',
    quarter: 1,
    year: 2024,
    reviewDate: '2024-07-15',
    mentorId: '2',
    mentorName: 'Mike Brown',
    scores: {
      technicalSkills: 5,
      communication: 7,
      teamwork: 6,
      initiative: 7,
      punctuality: 6,
      overallRating: 6.2
    },
    strengths: ['Creative writing', 'Social media understanding', 'Good ideas'],
    areasForImprovement: ['Technical marketing tools', 'Time management', 'Consistency'],
    goals: ['Complete HubSpot certification', 'Improve punctuality'],
    mentorComments: 'Oliver has potential but needs to improve consistency and technical skills.',
    apprenticeComments: 'I enjoy the creative side and want to get better at the technical aspects.',
    status: 'completed'
  },
  {
    id: '6',
    apprenticeId: '6',
    quarter: 2,
    year: 2024,
    reviewDate: '2024-06-15',
    mentorId: '3',
    mentorName: 'Lisa Green',
    scores: {
      technicalSkills: 7,
      communication: 9,
      teamwork: 8,
      initiative: 8,
      punctuality: 9,
      overallRating: 8.2
    },
    strengths: ['Excellent interpersonal skills', 'Strong initiative', 'Professional demeanor'],
    areasForImprovement: ['Technical recruitment tools', 'Industry knowledge'],
    goals: ['Complete LinkedIn Recruiter certification', 'Attend industry networking events'],
    mentorComments: 'Charlotte shows great promise in recruitment with natural people skills.',
    apprenticeComments: 'I love working with people and helping them find the right opportunities.',
    status: 'completed'
  },
  {
    id: '7',
    apprenticeId: '7',
    quarter: 1,
    year: 2024,
    reviewDate: '2024-05-15',
    mentorId: '4',
    mentorName: 'James Wilson',
    scores: {
      technicalSkills: 7,
      communication: 6,
      teamwork: 7,
      initiative: 6,
      punctuality: 8,
      overallRating: 6.8
    },
    strengths: ['Strong analytical skills', 'Good with numbers', 'Attention to detail'],
    areasForImprovement: ['Presentation skills', 'Business acumen', 'Initiative'],
    goals: ['Complete Excel advanced course', 'Present findings to management'],
    mentorComments: 'William has solid technical skills but needs to develop business communication.',
    apprenticeComments: 'I enjoy the analytical work and want to improve my presentation skills.',
    status: 'completed'
  },
  {
    id: '8',
    apprenticeId: '8',
    quarter: 1,
    year: 2024,
    reviewDate: '2024-06-15',
    mentorId: '4',
    mentorName: 'James Wilson',
    scores: {
      technicalSkills: 6,
      communication: 7,
      teamwork: 8,
      initiative: 7,
      punctuality: 7,
      overallRating: 7.0
    },
    strengths: ['Good teamwork', 'Interested in data visualization', 'Quick learner'],
    areasForImprovement: ['Advanced analytical techniques', 'SQL skills'],
    goals: ['Complete Power BI certification', 'Learn advanced SQL'],
    mentorComments: 'Amelia shows good potential in business intelligence and data visualization.',
    apprenticeComments: 'I find data visualization fascinating and want to become an expert.',
    status: 'completed'
  },
  {
    id: '9',
    apprenticeId: '9',
    quarter: 1,
    year: 2024,
    reviewDate: '2024-07-15',
    mentorId: '5',
    mentorName: 'Emma Davis',
    scores: {
      technicalSkills: 5,
      communication: 6,
      teamwork: 7,
      initiative: 8,
      punctuality: 7,
      overallRating: 6.6
    },
    strengths: ['Enthusiasm for technology', 'Good problem-solving', 'Strong initiative'],
    areasForImprovement: ['DevOps fundamentals', 'Cloud concepts', 'Technical documentation'],
    goals: ['Complete AWS Cloud Practitioner', 'Set up CI/CD pipeline'],
    mentorComments: 'Harry shows enthusiasm but needs to build fundamental knowledge.',
    apprenticeComments: 'I am excited about DevOps and cloud technologies.',
    status: 'completed'
  },
  {
    id: '10',
    apprenticeId: '10',
    quarter: 1,
    year: 2024,
    reviewDate: '2024-08-15',
    mentorId: '5',
    mentorName: 'Emma Davis',
    scores: {
      technicalSkills: 4,
      communication: 6,
      teamwork: 7,
      initiative: 6,
      punctuality: 8,
      overallRating: 6.2
    },
    strengths: ['Punctual and reliable', 'Good team player', 'AWS knowledge'],
    areasForImprovement: ['Practical cloud skills', 'Infrastructure concepts', 'Linux administration'],
    goals: ['Set up AWS infrastructure', 'Learn Docker basics'],
    mentorComments: 'Isla is just starting but shows promise. Needs practical experience.',
    apprenticeComments: 'I want to learn more about cloud infrastructure and gain hands-on experience.',
    status: 'completed'
  },
  {
    id: '11',
    apprenticeId: '11',
    quarter: 4,
    year: 2024,
    reviewDate: '2024-12-15',
    mentorId: '2',
    mentorName: 'Mike Brown',
    scores: {
      technicalSkills: 9,
      communication: 8,
      teamwork: 9,
      initiative: 9,
      punctuality: 8,
      overallRating: 8.6
    },
    strengths: ['SEO expertise', 'Data analysis', 'Project management'],
    areasForImprovement: ['Keep up with algorithm changes'],
    goals: ['Transition to full-time role', 'Lead SEO projects'],
    mentorComments: 'George has become an SEO expert and is ready for a full-time position.',
    apprenticeComments: 'I have learned so much about SEO and digital marketing.',
    status: 'completed'
  },
  {
    id: '12',
    apprenticeId: '12',
    quarter: 1,
    year: 2024,
    reviewDate: '2024-09-15',
    mentorId: '3',
    mentorName: 'Lisa Green',
    scores: {
      technicalSkills: 3,
      communication: 7,
      teamwork: 6,
      initiative: 5,
      punctuality: 7,
      overallRating: 5.6
    },
    strengths: ['Good communication', 'Interest in learning', 'Friendly demeanor'],
    areasForImprovement: ['All technical skills', 'Industry knowledge', 'Confidence'],
    goals: ['Complete L&D foundation course', 'Shadow training sessions'],
    mentorComments: 'Grace is very new but shows willingness to learn.',
    apprenticeComments: 'I am excited to learn about learning and development.',
    status: 'completed'
  },
  {
    id: '13',
    apprenticeId: '1',
    quarter: 3,
    year: 2024,
    reviewDate: '2024-10-15',
    mentorId: '1',
    mentorName: 'Sarah Johnson',
    scores: {
      technicalSkills: 8,
      communication: 9,
      teamwork: 9,
      initiative: 7,
      punctuality: 9,
      overallRating: 8.4
    },
    strengths: ['Improved technical skills', 'Leadership potential', 'Excellent teamwork'],
    areasForImprovement: ['Advanced problem-solving', 'Architecture design'],
    goals: ['Complete advanced React course', 'Lead team project'],
    mentorComments: 'John has made significant progress and is showing leadership qualities.',
    apprenticeComments: 'I feel much more confident now and ready to take on leadership roles.',
    status: 'completed'
  },
  {
    id: '14',
    apprenticeId: '2',
    quarter: 4,
    year: 2024,
    reviewDate: '2024-12-15',
    mentorId: '2',
    mentorName: 'Mike Brown',
    scores: {
      technicalSkills: 9,
      communication: 9,
      teamwork: 9,
      initiative: 9,
      punctuality: 8,
      overallRating: 8.8
    },
    strengths: ['Excellent all-around performance', 'Creative campaigns', 'Data-driven decisions'],
    areasForImprovement: ['Time management consistency'],
    goals: ['Complete advanced analytics course', 'Mentor new apprentices'],
    mentorComments: 'Emma has exceeded expectations and is ready for greater responsibilities.',
    apprenticeComments: 'I have grown so much and am excited about the future.',
    status: 'completed'
  },
  {
    id: '15',
    apprenticeId: '4',
    quarter: 2,
    year: 2024,
    reviewDate: '2024-09-15',
    mentorId: '3',
    mentorName: 'Lisa Green',
    scores: {
      technicalSkills: 7,
      communication: 8,
      teamwork: 8,
      initiative: 6,
      punctuality: 9,
      overallRating: 7.6
    },
    strengths: ['Improved technical skills', 'Better communication', 'Reliable performance'],
    areasForImprovement: ['Leadership skills', 'Strategic thinking'],
    goals: ['Complete CIPD Level 5', 'Take on project management role'],
    mentorComments: 'Sophie has improved significantly and is developing into a well-rounded HR professional.',
    apprenticeComments: 'I feel more confident in my HR skills and ready for new challenges.',
    status: 'completed'
  }
]

export const mockExams: Exam[] = [
  {
    id: '1',
    title: 'JavaScript Fundamentals',
    description: 'Comprehensive test covering ES6+, DOM manipulation, and async programming',
    type: 'written',
    subject: 'Programming',
    maxScore: 100,
    passingScore: 70,
    duration: 120,
    scheduledDate: '2024-08-15T09:00:00Z',
    status: 'completed',
    instructions: 'Answer all questions within the time limit. Code questions require working solutions.'
  },
  {
    id: '2',
    title: 'React Development Project',
    description: 'Build a complete React application with state management',
    type: 'project',
    subject: 'Web Development',
    maxScore: 100,
    passingScore: 75,
    duration: 480,
    scheduledDate: '2024-09-01T09:00:00Z',
    status: 'upcoming',
    instructions: 'Create a fully functional React application with routing, state management, and API integration.'
  },
  {
    id: '3',
    title: 'Digital Marketing Strategy',
    description: 'Oral presentation on campaign development and analytics',
    type: 'oral',
    subject: 'Marketing',
    maxScore: 100,
    passingScore: 70,
    duration: 45,
    scheduledDate: '2024-08-20T14:00:00Z',
    status: 'upcoming',
    instructions: 'Present a comprehensive marketing strategy including target audience, channels, and success metrics.'
  },
  {
    id: '4',
    title: 'Node.js Backend Development',
    description: 'Build RESTful APIs and database integration',
    type: 'practical',
    subject: 'Backend Development',
    maxScore: 100,
    passingScore: 75,
    duration: 240,
    scheduledDate: '2024-10-15T10:00:00Z',
    status: 'upcoming',
    instructions: 'Create a complete backend API with authentication, CRUD operations, and database integration.'
  },
  {
    id: '5',
    title: 'HR Policies and Procedures',
    description: 'Written assessment on HR practices and employment law',
    type: 'written',
    subject: 'Human Resources',
    maxScore: 100,
    passingScore: 70,
    duration: 90,
    scheduledDate: '2024-11-01T14:00:00Z',
    status: 'upcoming',
    instructions: 'Answer questions on employment law, HR policies, and best practices.'
  },
  {
    id: '6',
    title: 'Financial Analysis Case Study',
    description: 'Analyze financial data and present recommendations',
    type: 'project',
    subject: 'Finance',
    maxScore: 100,
    passingScore: 75,
    duration: 180,
    scheduledDate: '2024-11-15T09:00:00Z',
    status: 'upcoming',
    instructions: 'Analyze the provided financial data and prepare a comprehensive report with recommendations.'
  },
  {
    id: '7',
    title: 'Content Marketing Campaign',
    description: 'Create and present a content marketing strategy',
    type: 'project',
    subject: 'Content Marketing',
    maxScore: 100,
    passingScore: 70,
    duration: 300,
    scheduledDate: '2024-12-01T10:00:00Z',
    status: 'upcoming',
    instructions: 'Develop a 3-month content marketing campaign with clear objectives and measurable outcomes.'
  },
  {
    id: '8',
    title: 'DevOps Fundamentals',
    description: 'Practical assessment of CI/CD pipeline setup',
    type: 'practical',
    subject: 'DevOps',
    maxScore: 100,
    passingScore: 75,
    duration: 200,
    scheduledDate: '2024-12-15T13:00:00Z',
    status: 'upcoming',
    instructions: 'Set up a complete CI/CD pipeline with automated testing and deployment.'
  },
  {
    id: '9',
    title: 'Cloud Infrastructure Design',
    description: 'Design and implement cloud infrastructure solution',
    type: 'practical',
    subject: 'Cloud Computing',
    maxScore: 100,
    passingScore: 75,
    duration: 240,
    scheduledDate: '2025-01-10T10:00:00Z',
    status: 'upcoming',
    instructions: 'Design and implement a scalable cloud infrastructure using AWS services.'
  },
  {
    id: '10',
    title: 'Business Intelligence Dashboard',
    description: 'Create interactive dashboard from business data',
    type: 'project',
    subject: 'Business Intelligence',
    maxScore: 100,
    passingScore: 75,
    duration: 360,
    scheduledDate: '2025-01-20T09:00:00Z',
    status: 'upcoming',
    instructions: 'Build an interactive dashboard that provides insights into business performance metrics.'
  }
]

export const mockExamResults: ExamResult[] = [
  {
    id: '1',
    examId: '1',
    apprenticeId: '1',
    score: 85,
    completedAt: '2024-08-15T11:00:00Z',
    timeSpent: 110,
    feedback: 'Excellent understanding of core concepts. Strong performance in async programming and ES6 features. Keep up the good work!',
    passed: true,
    retakeAllowed: false,
    retakeCount: 0
  },
  {
    id: '2',
    examId: '1',
    apprenticeId: '3',
    score: 92,
    completedAt: '2024-08-15T10:45:00Z',
    timeSpent: 105,
    feedback: 'Outstanding performance! Demonstrated deep understanding of all concepts with excellent code quality.',
    passed: true,
    retakeAllowed: false,
    retakeCount: 0
  },
  {
    id: '3',
    examId: '3',
    apprenticeId: '2',
    score: 78,
    completedAt: '2024-08-20T14:30:00Z',
    timeSpent: 42,
    feedback: 'Good presentation skills and strategic thinking. Consider strengthening data analysis section.',
    passed: true,
    retakeAllowed: false,
    retakeCount: 0
  },
  {
    id: '4',
    examId: '3',
    apprenticeId: '5',
    score: 65,
    completedAt: '2024-08-20T14:45:00Z',
    timeSpent: 45,
    feedback: 'Creative ideas but needs improvement in strategic planning and metrics definition. Retake available.',
    passed: false,
    retakeAllowed: true,
    retakeCount: 0
  },
  {
    id: '5',
    examId: '5',
    apprenticeId: '4',
    score: 88,
    completedAt: '2024-11-01T15:30:00Z',
    timeSpent: 85,
    feedback: 'Excellent knowledge of HR policies and employment law. Strong analytical skills demonstrated.',
    passed: true,
    retakeAllowed: false,
    retakeCount: 0
  },
  {
    id: '6',
    examId: '5',
    apprenticeId: '6',
    score: 82,
    completedAt: '2024-11-01T15:15:00Z',
    timeSpent: 88,
    feedback: 'Good understanding of HR practices. Well-structured answers with practical examples.',
    passed: true,
    retakeAllowed: false,
    retakeCount: 0
  },
  {
    id: '7',
    examId: '6',
    apprenticeId: '7',
    score: 79,
    completedAt: '2024-11-15T11:45:00Z',
    timeSpent: 175,
    feedback: 'Solid financial analysis skills. Good use of analytical tools and clear presentation of findings.',
    passed: true,
    retakeAllowed: false,
    retakeCount: 0
  },
  {
    id: '8',
    examId: '6',
    apprenticeId: '8',
    score: 86,
    completedAt: '2024-11-15T12:00:00Z',
    timeSpent: 168,
    feedback: 'Excellent data visualization and insightful recommendations. Strong business acumen displayed.',
    passed: true,
    retakeAllowed: false,
    retakeCount: 0
  },
  {
    id: '9',
    examId: '1',
    apprenticeId: '9',
    score: 68,
    completedAt: '2024-08-15T11:30:00Z',
    timeSpent: 120,
    feedback: 'Good effort but needs improvement in advanced concepts. Focus on closures and async programming.',
    passed: false,
    retakeAllowed: true,
    retakeCount: 0
  },
  {
    id: '10',
    examId: '1',
    apprenticeId: '9',
    score: 75,
    completedAt: '2024-09-01T10:15:00Z',
    timeSpent: 115,
    feedback: 'Much improved! Better understanding of advanced concepts. Well done on the retake.',
    passed: true,
    retakeAllowed: false,
    retakeCount: 1
  }
]

export const mockProgressModules: ProgressModule[] = [
  {
    id: '1',
    title: 'Web Development Fundamentals',
    description: 'HTML, CSS, and JavaScript basics',
    category: 'Technical',
    requiredHours: 40,
    order: 1,
    prerequisites: [],
    isRequired: true
  },
  {
    id: '2',
    title: 'React Framework',
    description: 'Building modern web applications with React',
    category: 'Technical',
    requiredHours: 60,
    order: 2,
    prerequisites: ['1'],
    isRequired: true
  },
  {
    id: '3',
    title: 'Professional Communication',
    description: 'Business communication and presentation skills',
    category: 'Soft Skills',
    requiredHours: 20,
    order: 3,
    prerequisites: [],
    isRequired: true
  },
  {
    id: '4',
    title: 'Node.js Backend Development',
    description: 'Server-side JavaScript and API development',
    category: 'Technical',
    requiredHours: 50,
    order: 4,
    prerequisites: ['1'],
    isRequired: true
  },
  {
    id: '5',
    title: 'Database Design and Management',
    description: 'SQL and NoSQL database concepts and implementation',
    category: 'Technical',
    requiredHours: 35,
    order: 5,
    prerequisites: ['4'],
    isRequired: true
  },
  {
    id: '6',
    title: 'Digital Marketing Fundamentals',
    description: 'Core principles of digital marketing and online presence',
    category: 'Technical',
    requiredHours: 30,
    order: 6,
    prerequisites: [],
    isRequired: true
  },
  {
    id: '7',
    title: 'Content Strategy and Creation',
    description: 'Planning and creating engaging digital content',
    category: 'Technical',
    requiredHours: 40,
    order: 7,
    prerequisites: ['6'],
    isRequired: true
  },
  {
    id: '8',
    title: 'Social Media Marketing',
    description: 'Leveraging social platforms for marketing success',
    category: 'Technical',
    requiredHours: 25,
    order: 8,
    prerequisites: ['6'],
    isRequired: true
  },
  {
    id: '9',
    title: 'HR Administration Basics',
    description: 'Core HR processes and administrative tasks',
    category: 'Technical',
    requiredHours: 30,
    order: 9,
    prerequisites: [],
    isRequired: true
  },
  {
    id: '10',
    title: 'Recruitment and Selection',
    description: 'Talent acquisition strategies and interview processes',
    category: 'Technical',
    requiredHours: 35,
    order: 10,
    prerequisites: ['9'],
    isRequired: true
  },
  {
    id: '11',
    title: 'Employee Relations',
    description: 'Managing workplace relationships and conflict resolution',
    category: 'Soft Skills',
    requiredHours: 25,
    order: 11,
    prerequisites: ['9'],
    isRequired: true
  },
  {
    id: '12',
    title: 'Financial Analysis Fundamentals',
    description: 'Basic financial analysis and reporting principles',
    category: 'Technical',
    requiredHours: 40,
    order: 12,
    prerequisites: [],
    isRequired: true
  },
  {
    id: '13',
    title: 'Business Intelligence Tools',
    description: 'Excel, Power BI, and data visualization techniques',
    category: 'Technical',
    requiredHours: 45,
    order: 13,
    prerequisites: ['12'],
    isRequired: true
  },
  {
    id: '14',
    title: 'DevOps Fundamentals',
    description: 'CI/CD pipelines and deployment automation',
    category: 'Technical',
    requiredHours: 50,
    order: 14,
    prerequisites: ['4'],
    isRequired: true
  },
  {
    id: '15',
    title: 'Cloud Infrastructure',
    description: 'AWS, Azure, and cloud deployment strategies',
    category: 'Technical',
    requiredHours: 55,
    order: 15,
    prerequisites: ['14'],
    isRequired: true
  },
  {
    id: '16',
    title: 'Project Management',
    description: 'Agile methodologies and project planning',
    category: 'Soft Skills',
    requiredHours: 30,
    order: 16,
    prerequisites: [],
    isRequired: false
  },
  {
    id: '17',
    title: 'Leadership and Teamwork',
    description: 'Developing leadership skills and team collaboration',
    category: 'Soft Skills',
    requiredHours: 25,
    order: 17,
    prerequisites: ['3'],
    isRequired: false
  },
  {
    id: '18',
    title: 'Data Analysis and Statistics',
    description: 'Statistical analysis and data interpretation',
    category: 'Technical',
    requiredHours: 40,
    order: 18,
    prerequisites: ['12'],
    isRequired: false
  },
  {
    id: '19',
    title: 'Cybersecurity Awareness',
    description: 'Information security best practices and threat awareness',
    category: 'Technical',
    requiredHours: 20,
    order: 19,
    prerequisites: [],
    isRequired: false
  },
  {
    id: '20',
    title: 'Industry Compliance',
    description: 'Understanding regulatory requirements and compliance',
    category: 'Technical',
    requiredHours: 15,
    order: 20,
    prerequisites: [],
    isRequired: false
  }
]

export const mockApprenticeProgress: ApprenticeProgress[] = [
  // John Smith (Apprentice 1) - Software Development
  {
    id: '1',
    apprenticeId: '1',
    moduleId: '1',
    hoursCompleted: 40,
    status: 'completed',
    startDate: '2024-01-15',
    completedDate: '2024-03-01',
    notes: 'Strong foundation in HTML, CSS, and JavaScript. Ready for advanced topics.',
    mentorApproved: true
  },
  {
    id: '2',
    apprenticeId: '1',
    moduleId: '2',
    hoursCompleted: 45,
    status: 'in-progress',
    startDate: '2024-03-01',
    notes: 'Making good progress with React components and state management.',
    mentorApproved: false
  },
  {
    id: '3',
    apprenticeId: '1',
    moduleId: '3',
    hoursCompleted: 20,
    status: 'completed',
    startDate: '2024-02-15',
    completedDate: '2024-04-01',
    notes: 'Improved communication skills significantly. Ready for presentations.',
    mentorApproved: true
  },
  {
    id: '4',
    apprenticeId: '1',
    moduleId: '4',
    hoursCompleted: 25,
    status: 'in-progress',
    startDate: '2024-04-15',
    notes: 'Learning Node.js and Express framework. Building first API.',
    mentorApproved: false
  },
  
  // Emma Wilson (Apprentice 2) - Digital Marketing
  {
    id: '5',
    apprenticeId: '2',
    moduleId: '6',
    hoursCompleted: 30,
    status: 'completed',
    startDate: '2024-02-01',
    completedDate: '2024-03-15',
    notes: 'Excellent grasp of digital marketing concepts and tools.',
    mentorApproved: true
  },
  {
    id: '6',
    apprenticeId: '2',
    moduleId: '7',
    hoursCompleted: 35,
    status: 'in-progress',
    startDate: '2024-03-15',
    notes: 'Creating engaging content for various platforms. Very creative.',
    mentorApproved: false
  },
  {
    id: '7',
    apprenticeId: '2',
    moduleId: '8',
    hoursCompleted: 25,
    status: 'completed',
    startDate: '2024-04-01',
    completedDate: '2024-05-15',
    notes: 'Social media campaigns showing great results. Natural talent.',
    mentorApproved: true
  },
  {
    id: '8',
    apprenticeId: '2',
    moduleId: '3',
    hoursCompleted: 20,
    status: 'completed',
    startDate: '2024-02-01',
    completedDate: '2024-04-01',
    notes: 'Outstanding presentation skills. Very confident speaker.',
    mentorApproved: true
  },
  
  // James Davis (Apprentice 3) - Data Analytics - COMPLETED
  {
    id: '9',
    apprenticeId: '3',
    moduleId: '1',
    hoursCompleted: 40,
    status: 'completed',
    startDate: '2023-09-01',
    completedDate: '2023-11-15',
    notes: 'Excellent technical foundation. Quick learner.',
    mentorApproved: true
  },
  {
    id: '10',
    apprenticeId: '3',
    moduleId: '2',
    hoursCompleted: 60,
    status: 'completed',
    startDate: '2023-11-15',
    completedDate: '2024-02-01',
    notes: 'Advanced React skills. Built impressive portfolio projects.',
    mentorApproved: true
  },
  {
    id: '11',
    apprenticeId: '3',
    moduleId: '3',
    hoursCompleted: 20,
    status: 'completed',
    startDate: '2023-10-01',
    completedDate: '2023-12-15',
    notes: 'Professional communication skills. Ready for client interaction.',
    mentorApproved: true
  },
  {
    id: '12',
    apprenticeId: '3',
    moduleId: '18',
    hoursCompleted: 40,
    status: 'completed',
    startDate: '2024-01-15',
    completedDate: '2024-04-01',
    notes: 'Outstanding analytical skills. Data visualization expertise.',
    mentorApproved: true
  },
  
  // Sophie Taylor (Apprentice 4) - HR Administration
  {
    id: '13',
    apprenticeId: '4',
    moduleId: '9',
    hoursCompleted: 25,
    status: 'in-progress',
    startDate: '2024-03-01',
    notes: 'Learning HR fundamentals. Good attention to detail.',
    mentorApproved: false
  },
  {
    id: '14',
    apprenticeId: '4',
    moduleId: '3',
    hoursCompleted: 20,
    status: 'completed',
    startDate: '2024-03-15',
    completedDate: '2024-05-01',
    notes: 'Strong interpersonal skills. Natural communicator.',
    mentorApproved: true
  },
  {
    id: '15',
    apprenticeId: '4',
    moduleId: '10',
    hoursCompleted: 15,
    status: 'in-progress',
    startDate: '2024-05-15',
    notes: 'Starting recruitment training. Shows promise.',
    mentorApproved: false
  },
  
  // Oliver Thompson (Apprentice 5) - Content Marketing
  {
    id: '16',
    apprenticeId: '5',
    moduleId: '6',
    hoursCompleted: 20,
    status: 'in-progress',
    startDate: '2024-04-01',
    notes: 'Creative approach to marketing. Needs more technical skills.',
    mentorApproved: false
  },
  {
    id: '17',
    apprenticeId: '5',
    moduleId: '7',
    hoursCompleted: 25,
    status: 'in-progress',
    startDate: '2024-05-01',
    notes: 'Excellent writing skills. Content quality is high.',
    mentorApproved: false
  },
  {
    id: '18',
    apprenticeId: '5',
    moduleId: '3',
    hoursCompleted: 15,
    status: 'in-progress',
    startDate: '2024-04-15',
    notes: 'Working on presentation skills. Good progress.',
    mentorApproved: false
  },
  
  // Charlotte Anderson (Apprentice 6) - Recruitment
  {
    id: '19',
    apprenticeId: '6',
    moduleId: '9',
    hoursCompleted: 30,
    status: 'completed',
    startDate: '2024-01-08',
    completedDate: '2024-03-01',
    notes: 'Solid understanding of HR basics. Ready for specialization.',
    mentorApproved: true
  },
  {
    id: '20',
    apprenticeId: '6',
    moduleId: '10',
    hoursCompleted: 30,
    status: 'in-progress',
    startDate: '2024-03-01',
    notes: 'Excellent people skills. Natural talent for recruitment.',
    mentorApproved: false
  },
  {
    id: '21',
    apprenticeId: '6',
    moduleId: '3',
    hoursCompleted: 20,
    status: 'completed',
    startDate: '2024-02-01',
    completedDate: '2024-04-15',
    notes: 'Outstanding communication skills. Very professional.',
    mentorApproved: true
  },
  
  // William Roberts (Apprentice 7) - Financial Analysis
  {
    id: '22',
    apprenticeId: '7',
    moduleId: '12',
    hoursCompleted: 35,
    status: 'in-progress',
    startDate: '2024-02-15',
    notes: 'Strong analytical mindset. Good with numbers.',
    mentorApproved: false
  },
  {
    id: '23',
    apprenticeId: '7',
    moduleId: '13',
    hoursCompleted: 20,
    status: 'in-progress',
    startDate: '2024-04-01',
    notes: 'Learning Power BI and Excel advanced features.',
    mentorApproved: false
  },
  {
    id: '24',
    apprenticeId: '7',
    moduleId: '3',
    hoursCompleted: 15,
    status: 'in-progress',
    startDate: '2024-03-01',
    notes: 'Improving presentation skills. More confidence needed.',
    mentorApproved: false
  },
  
  // Amelia Evans (Apprentice 8) - Business Intelligence
  {
    id: '25',
    apprenticeId: '8',
    moduleId: '12',
    hoursCompleted: 25,
    status: 'in-progress',
    startDate: '2024-03-15',
    notes: 'Good understanding of financial concepts. Eager to learn.',
    mentorApproved: false
  },
  {
    id: '26',
    apprenticeId: '8',
    moduleId: '13',
    hoursCompleted: 30,
    status: 'in-progress',
    startDate: '2024-04-15',
    notes: 'Excellent with data visualization. Creative dashboards.',
    mentorApproved: false
  },
  {
    id: '27',
    apprenticeId: '8',
    moduleId: '18',
    hoursCompleted: 15,
    status: 'in-progress',
    startDate: '2024-05-01',
    notes: 'Learning statistical analysis. Good progress.',
    mentorApproved: false
  },
  
  // Harry Clark (Apprentice 9) - DevOps
  {
    id: '28',
    apprenticeId: '9',
    moduleId: '1',
    hoursCompleted: 30,
    status: 'in-progress',
    startDate: '2024-04-15',
    notes: 'Building web development foundation for DevOps.',
    mentorApproved: false
  },
  {
    id: '29',
    apprenticeId: '9',
    moduleId: '4',
    hoursCompleted: 15,
    status: 'in-progress',
    startDate: '2024-05-15',
    notes: 'Learning Node.js for backend understanding.',
    mentorApproved: false
  },
  {
    id: '30',
    apprenticeId: '9',
    moduleId: '14',
    hoursCompleted: 10,
    status: 'in-progress',
    startDate: '2024-06-01',
    notes: 'Just started DevOps fundamentals. Enthusiastic.',
    mentorApproved: false
  },
  
  // Isla Walker (Apprentice 10) - Cloud Infrastructure
  {
    id: '31',
    apprenticeId: '10',
    moduleId: '1',
    hoursCompleted: 20,
    status: 'in-progress',
    startDate: '2024-05-01',
    notes: 'Learning web fundamentals to understand cloud applications.',
    mentorApproved: false
  },
  {
    id: '32',
    apprenticeId: '10',
    moduleId: '15',
    hoursCompleted: 8,
    status: 'in-progress',
    startDate: '2024-06-01',
    notes: 'Beginning cloud infrastructure training. AWS focus.',
    mentorApproved: false
  },
  
  // George Mitchell (Apprentice 11) - SEO - COMPLETED
  {
    id: '33',
    apprenticeId: '11',
    moduleId: '6',
    hoursCompleted: 30,
    status: 'completed',
    startDate: '2023-06-01',
    completedDate: '2023-08-15',
    notes: 'Excellent understanding of digital marketing principles.',
    mentorApproved: true
  },
  {
    id: '34',
    apprenticeId: '11',
    moduleId: '7',
    hoursCompleted: 40,
    status: 'completed',
    startDate: '2023-08-15',
    completedDate: '2023-11-01',
    notes: 'Outstanding content creation skills. SEO-optimized content.',
    mentorApproved: true
  },
  {
    id: '35',
    apprenticeId: '11',
    moduleId: '8',
    hoursCompleted: 25,
    status: 'completed',
    startDate: '2023-09-01',
    completedDate: '2023-11-15',
    notes: 'Social media expertise. Great engagement rates.',
    mentorApproved: true
  },
  {
    id: '36',
    apprenticeId: '11',
    moduleId: '18',
    hoursCompleted: 40,
    status: 'completed',
    startDate: '2023-12-01',
    completedDate: '2024-03-15',
    notes: 'Advanced analytics skills. Data-driven SEO strategies.',
    mentorApproved: true
  },
  
  // Grace Turner (Apprentice 12) - Learning & Development
  {
    id: '37',
    apprenticeId: '12',
    moduleId: '9',
    hoursCompleted: 5,
    status: 'in-progress',
    startDate: '2024-06-01',
    notes: 'Just started. Learning HR fundamentals.',
    mentorApproved: false
  },
  {
    id: '38',
    apprenticeId: '12',
    moduleId: '3',
    hoursCompleted: 8,
    status: 'in-progress',
    startDate: '2024-06-15',
    notes: 'Working on communication skills. Good potential.',
    mentorApproved: false
  }
]

// Departments are already exported above