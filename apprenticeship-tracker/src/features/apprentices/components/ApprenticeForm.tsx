import React from 'react'
import { <PERSON><PERSON> } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Select } from '../../../components/ui/select'
import { DatePicker } from '../../../components/ui/date-picker'
import { useFormValidation, validationRules } from '../../../lib/validation'
import { Apprentice } from '../../../types'
import { useApprenticeStore } from '../apprenticeStore'

interface ApprenticeFormProps {
  apprentice?: Apprentice
  onSubmit: (data: Partial<Apprentice>) => void
  onCancel: () => void
  isLoading?: boolean
}

interface ApprenticeFormData {
  firstName: string
  lastName: string
  email: string
  phone: string
  position: string
  department: string
  startDate: string
  endDate: string
  mentor: string
  status: 'active' | 'completed' | 'suspended' | 'withdrawn'
}

const departmentOptions = [
  { value: 'Technology', label: 'Technology' },
  { value: 'Marketing', label: 'Marketing' },
  { value: 'Sales', label: 'Sales' },
  { value: 'Human Resources', label: 'Human Resources' },
  { value: 'Finance', label: 'Finance' },
  { value: 'Operations', label: 'Operations' },
]

const statusOptions = [
  { value: 'active', label: 'Active' },
  { value: 'completed', label: 'Completed' },
  { value: 'suspended', label: 'Suspended' },
  { value: 'withdrawn', label: 'Withdrawn' },
]

const mentorOptions = [
  { value: '<PERSON>', label: 'John Smith' },
  { value: 'Sarah Johnson', label: 'Sarah Johnson' },
  { value: 'Mike Brown', label: 'Mike Brown' },
  { value: 'Emily Davis', label: 'Emily Davis' },
  { value: 'David Wilson', label: 'David Wilson' },
]

export const ApprenticeForm: React.FC<ApprenticeFormProps> = ({
  apprentice,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const initialData: ApprenticeFormData = {
    firstName: apprentice?.firstName || '',
    lastName: apprentice?.lastName || '',
    email: apprentice?.email || '',
    phone: apprentice?.phone || '',
    position: apprentice?.position || '',
    department: apprentice?.department || '',
    startDate: apprentice?.startDate ? apprentice.startDate.split('T')[0] : '',
    endDate: apprentice?.endDate ? apprentice.endDate.split('T')[0] : '',
    mentor: apprentice?.mentor || '',
    status: apprentice?.status || 'active',
  }

  const validationConfig = {
    firstName: {
      required: true,
      rules: [validationRules.minLength(2), validationRules.maxLength(50)]
    },
    lastName: {
      required: true,
      rules: [validationRules.minLength(2), validationRules.maxLength(50)]
    },
    email: {
      required: true,
      rules: [validationRules.email]
    },
    phone: {
      required: true,
      rules: [validationRules.phone]
    },
    position: {
      required: true,
      rules: [validationRules.minLength(2), validationRules.maxLength(100)]
    },
    department: {
      required: true
    },
    startDate: {
      required: true,
      rules: [
        validationRules.custom(
          (value: string) => new Date(value) <= new Date(),
          'Start date cannot be in the future'
        )
      ]
    },
    endDate: {
      required: true,
      rules: [
        validationRules.custom(
          (value: string) => new Date(value) > new Date(),
          'End date must be in the future'
        )
      ]
    },
    mentor: {
      required: true
    },
    status: {
      required: true
    }
  }

  const {
    data,
    errors,
    touched,
    updateField,
    touchField,
    validateForm,
    isValid
  } = useFormValidation(initialData, validationConfig)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (validateForm()) {
      const formattedData = {
        ...data,
        startDate: new Date(data.startDate).toISOString(),
        endDate: new Date(data.endDate).toISOString(),
        ...(apprentice?.id && { id: apprentice.id })
      }
      onSubmit(formattedData)
    }
  }

  // Validate end date is after start date
  const validateDates = () => {
    if (data.startDate && data.endDate) {
      const start = new Date(data.startDate)
      const end = new Date(data.endDate)
      return end > start
    }
    return true
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6 p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label="First Name"
          value={data.firstName}
          onChange={(e) => updateField('firstName', e.target.value)}
          onBlur={() => touchField('firstName')}
          error={touched.firstName ? errors.firstName : undefined}
          disabled={isLoading}
          required
        />
        
        <Input
          label="Last Name"
          value={data.lastName}
          onChange={(e) => updateField('lastName', e.target.value)}
          onBlur={() => touchField('lastName')}
          error={touched.lastName ? errors.lastName : undefined}
          disabled={isLoading}
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label="Email"
          type="email"
          value={data.email}
          onChange={(e) => updateField('email', e.target.value)}
          onBlur={() => touchField('email')}
          error={touched.email ? errors.email : undefined}
          disabled={isLoading}
          required
        />
        
        <Input
          label="Phone"
          type="tel"
          value={data.phone}
          onChange={(e) => updateField('phone', e.target.value)}
          onBlur={() => touchField('phone')}
          error={touched.phone ? errors.phone : undefined}
          disabled={isLoading}
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label="Position"
          value={data.position}
          onChange={(e) => updateField('position', e.target.value)}
          onBlur={() => touchField('position')}
          error={touched.position ? errors.position : undefined}
          disabled={isLoading}
          required
        />
        
        <Select
          label="Department"
          options={departmentOptions}
          value={data.department}
          onChange={(value) => updateField('department', value)}
          onBlur={() => touchField('department')}
          error={touched.department ? errors.department : undefined}
          disabled={isLoading}
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <DatePicker
          label="Start Date"
          value={data.startDate}
          onChange={(value) => updateField('startDate', value)}
          onBlur={() => touchField('startDate')}
          error={touched.startDate ? errors.startDate : undefined}
          disabled={isLoading}
          required
        />
        
        <DatePicker
          label="End Date"
          value={data.endDate}
          onChange={(value) => updateField('endDate', value)}
          onBlur={() => touchField('endDate')}
          error={touched.endDate ? errors.endDate : (!validateDates() ? 'End date must be after start date' : undefined)}
          disabled={isLoading}
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Select
          label="Mentor"
          options={mentorOptions}
          value={data.mentor}
          onChange={(value) => updateField('mentor', value)}
          onBlur={() => touchField('mentor')}
          error={touched.mentor ? errors.mentor : undefined}
          disabled={isLoading}
          required
        />
        
        <Select
          label="Status"
          options={statusOptions}
          value={data.status}
          onChange={(value) => updateField('status', value as any)}
          onBlur={() => touchField('status')}
          error={touched.status ? errors.status : undefined}
          disabled={isLoading}
          required
        />
      </div>

      <div className="flex justify-end space-x-3 pt-6 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={!isValid || !validateDates() || isLoading}
        >
          {isLoading ? 'Saving...' : (apprentice ? 'Update Apprentice' : 'Create Apprentice')}
        </Button>
      </div>
    </form>
  )
}