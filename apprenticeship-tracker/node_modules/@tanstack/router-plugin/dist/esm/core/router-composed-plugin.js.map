{"version": 3, "file": "router-composed-plugin.js", "sources": ["../../../src/core/router-composed-plugin.ts"], "sourcesContent": ["import { unpluginRouterGeneratorFactory } from './router-generator-plugin'\nimport { unpluginRouterCodeSplitterFactory } from './router-code-splitter-plugin'\nimport { unpluginRouterHmrFactory } from './router-hmr-plugin'\nimport { unpluginRouteAutoImportFactory } from './route-autoimport-plugin'\nimport type { Config } from './config'\nimport type { UnpluginFactory } from 'unplugin'\n\nexport const unpluginRouterComposedFactory: UnpluginFactory<\n  Partial<Config> | undefined\n> = (options = {}, meta) => {\n  const getPlugin = (pluginFactory: UnpluginFactory<Partial<Config>>) => {\n    const plugin = pluginFactory(options, meta)\n    if (!Array.isArray(plugin)) {\n      return [plugin]\n    }\n    return plugin\n  }\n\n  const routerGenerator = getPlugin(unpluginRouterGeneratorFactory)\n  const routerCodeSplitter = getPlugin(unpluginRouterCodeSplitterFactory)\n  const routeAutoImport = getPlugin(unpluginRouteAutoImportFactory)\n\n  const result = [...routerGenerator]\n  if (options.autoCodeSplitting) {\n    result.push(...routerCodeSplitter)\n  }\n  if (options.verboseFileRoutes === false) {\n    result.push(...routeAutoImport)\n  }\n\n  const isProduction = process.env.NODE_ENV === 'production'\n\n  if (!isProduction && !options.autoCodeSplitting) {\n    const routerHmr = getPlugin(unpluginRouterHmrFactory)\n    result.push(...routerHmr)\n  }\n  return result\n}\n"], "names": [], "mappings": ";;;;AAOO,MAAM,gCAET,CAAC,UAAU,IAAI,SAAS;AACpB,QAAA,YAAY,CAAC,kBAAoD;AAC/D,UAAA,SAAS,cAAc,SAAS,IAAI;AAC1C,QAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,aAAO,CAAC,MAAM;AAAA,IAAA;AAET,WAAA;AAAA,EACT;AAEM,QAAA,kBAAkB,UAAU,8BAA8B;AAC1D,QAAA,qBAAqB,UAAU,iCAAiC;AAChE,QAAA,kBAAkB,UAAU,8BAA8B;AAE1D,QAAA,SAAS,CAAC,GAAG,eAAe;AAClC,MAAI,QAAQ,mBAAmB;AACtB,WAAA,KAAK,GAAG,kBAAkB;AAAA,EAAA;AAE/B,MAAA,QAAQ,sBAAsB,OAAO;AAChC,WAAA,KAAK,GAAG,eAAe;AAAA,EAAA;AAG1B,QAAA,eAAe,QAAQ,IAAI,aAAa;AAE9C,MAAI,CAAC,gBAAgB,CAAC,QAAQ,mBAAmB;AACzC,UAAA,YAAY,UAAU,wBAAwB;AAC7C,WAAA,KAAK,GAAG,SAAS;AAAA,EAAA;AAEnB,SAAA;AACT;"}