export type ValidationRule<T = any> = {
  test: (value: T) => boolean
  message: string
}

export type FieldValidation<T = any> = {
  required?: boolean
  rules?: ValidationRule<T>[]
}

export type FormValidation<T extends Record<string, any>> = {
  [K in keyof T]?: FieldValidation<T[K]>
}

export type ValidationErrors<T extends Record<string, any>> = {
  [K in keyof T]?: string
}

export class FormValidator<T extends Record<string, any>> {
  private validationRules: FormValidation<T>

  constructor(validationRules: FormValidation<T>) {
    this.validationRules = validationRules
  }

  validate(data: T): ValidationErrors<T> {
    const errors: ValidationErrors<T> = {}

    Object.keys(this.validationRules).forEach((key) => {
      const fieldKey = key as keyof T
      const value = data[fieldKey]
      const fieldValidation = this.validationRules[fieldKey]

      if (!fieldValidation) return

      // Required validation
      if (fieldValidation.required && (!value || (typeof value === 'string' && !value.trim()))) {
        errors[fieldKey] = `${String(fieldKey)} is required`
        return
      }

      // Skip other validations if field is empty and not required
      if (!value && !fieldValidation.required) return

      // Custom rules validation
      if (fieldValidation.rules) {
        for (const rule of fieldValidation.rules) {
          if (!rule.test(value)) {
            errors[fieldKey] = rule.message
            break
          }
        }
      }
    })

    return errors
  }

  validateField(fieldName: keyof T, value: T[keyof T]): string | undefined {
    const fieldValidation = this.validationRules[fieldName]
    
    if (!fieldValidation) return undefined

    // Required validation
    if (fieldValidation.required && (!value || (typeof value === 'string' && !value.trim()))) {
      return `${String(fieldName)} is required`
    }

    // Skip other validations if field is empty and not required
    if (!value && !fieldValidation.required) return undefined

    // Custom rules validation
    if (fieldValidation.rules) {
      for (const rule of fieldValidation.rules) {
        if (!rule.test(value)) {
          return rule.message
        }
      }
    }

    return undefined
  }
}

// Common validation rules
export const validationRules = {
  email: {
    test: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    message: 'Please enter a valid email address'
  },
  
  phone: {
    test: (value: string) => /^\+?[\d\s\-\(\)]+$/.test(value),
    message: 'Please enter a valid phone number'
  },
  
  minLength: (min: number) => ({
    test: (value: string) => value.length >= min,
    message: `Must be at least ${min} characters long`
  }),
  
  maxLength: (max: number) => ({
    test: (value: string) => value.length <= max,
    message: `Must be no more than ${max} characters long`
  }),
  
  minValue: (min: number) => ({
    test: (value: number) => value >= min,
    message: `Must be at least ${min}`
  }),
  
  maxValue: (max: number) => ({
    test: (value: number) => value <= max,
    message: `Must be no more than ${max}`
  }),
  
  pattern: (pattern: RegExp, message: string) => ({
    test: (value: string) => pattern.test(value),
    message
  }),
  
  custom: <T>(testFn: (value: T) => boolean, message: string) => ({
    test: testFn,
    message
  })
}

// Hook for form validation
import { useState, useCallback } from 'react'

export function useFormValidation<T extends Record<string, any>>(
  initialData: T,
  validationRules: FormValidation<T>
) {
  const [data, setData] = useState<T>(initialData)
  const [errors, setErrors] = useState<ValidationErrors<T>>({})
  const [touched, setTouched] = useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>)

  const validator = new FormValidator(validationRules)

  const validateField = useCallback((fieldName: keyof T, value: T[keyof T]) => {
    const error = validator.validateField(fieldName, value)
    setErrors(prev => ({
      ...prev,
      [fieldName]: error
    }))
    return !error
  }, [validator])

  const validateForm = useCallback(() => {
    const newErrors = validator.validate(data)
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [data, validator])

  const updateField = useCallback((fieldName: keyof T, value: T[keyof T]) => {
    setData(prev => ({
      ...prev,
      [fieldName]: value
    }))
    
    // Validate field if it has been touched
    if (touched[fieldName]) {
      validateField(fieldName, value)
    }
  }, [touched, validateField])

  const touchField = useCallback((fieldName: keyof T) => {
    setTouched(prev => ({
      ...prev,
      [fieldName]: true
    }))
    
    // Validate field when touched
    validateField(fieldName, data[fieldName])
  }, [data, validateField])

  const resetForm = useCallback(() => {
    setData(initialData)
    setErrors({})
    setTouched({} as Record<keyof T, boolean>)
  }, [initialData])

  return {
    data,
    errors,
    touched,
    updateField,
    touchField,
    validateForm,
    validateField,
    resetForm,
    isValid: Object.keys(errors).length === 0
  }
}