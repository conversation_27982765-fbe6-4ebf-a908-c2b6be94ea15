import { useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '../../../components/ui/card'
import { Progress, CircularProgress } from '../../../components/ui/progress'
import { SkeletonCard } from '../../../components/ui/skeleton'
import { LoadingScreen } from '../../../components/ui/loading'
import { <PERSON><PERSON>, <PERSON>, Line<PERSON>hart, StatCard } from '../../../components/ui/chart'
import { useDashboardStore } from '../dashboardStore'

export function Dashboard() {
  const { stats, loading, error, fetchStats } = useDashboardStore()

  useEffect(() => {
    fetchStats()
  }, [fetchStats])

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <SkeletonCard key={i} />
          ))}
        </div>
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <SkeletonCard />
          <SkeletonCard />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <Card variant="glass" className="p-8 text-center">
          <div className="text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-destructive mb-2">Error</h2>
          <p className="text-muted-foreground">{error}</p>
        </Card>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <Card variant="glass" className="p-8 text-center">
          <div className="text-6xl mb-4">📊</div>
          <h2 className="text-2xl font-bold mb-2">No Data Available</h2>
          <p className="text-muted-foreground">Please check back later</p>
        </Card>
      </div>
    )
  }

  const statCards = [
    {
      title: 'Total Apprentices',
      value: stats.totalApprentices,
      description: 'All registered apprentices',
      icon: '👥',
      variant: 'default' as const,
      trend: '+12%'
    },
    {
      title: 'Active Apprentices',
      value: stats.activeApprentices,
      description: 'Currently enrolled',
      icon: '🎓',
      variant: 'glass' as const,
      trend: '+5%'
    },
    {
      title: 'Completed Programs',
      value: stats.completedApprentices,
      description: 'Successfully finished',
      icon: '🏆',
      variant: 'modern' as const,
      trend: '+8%'
    },
    {
      title: 'Average Progress',
      value: `${stats.averageProgress}%`,
      description: 'Overall completion rate',
      icon: '📈',
      variant: 'glass' as const,
      progress: stats.averageProgress
    },
    {
      title: 'Upcoming Reviews',
      value: stats.upcomingReviews,
      description: 'Scheduled this quarter',
      icon: '📋',
      variant: 'default' as const,
      trend: '+2%'
    },
    {
      title: 'Upcoming Exams',
      value: stats.upcomingExams,
      description: 'This month',
      icon: '📝',
      variant: 'modern' as const,
      trend: '+3%'
    },
    {
      title: 'Pass Rate',
      value: `${stats.passRate}%`,
      description: 'Last quarter average',
      icon: '✅',
      variant: 'glass' as const,
      progress: stats.passRate
    },
    {
      title: 'Overdue Reviews',
      value: stats.overdueReviews,
      description: 'Need attention',
      icon: '⚠️',
      variant: 'default' as const,
      urgent: true
    }
  ]

  // Chart data
  const departmentData = [
    { label: 'Technology', value: 15, color: '#3B82F6' },
    { label: 'Marketing', value: 8, color: '#10B981' },
    { label: 'Sales', value: 12, color: '#F59E0B' },
    { label: 'HR', value: 5, color: '#EF4444' },
    { label: 'Finance', value: 7, color: '#8B5CF6' }
  ]

  const progressTrendData = [
    { label: 'Jan', value: 65 },
    { label: 'Feb', value: 72 },
    { label: 'Mar', value: 78 },
    { label: 'Apr', value: 75 },
    { label: 'May', value: 82 },
    { label: 'Jun', value: 88 }
  ]

  const skillsData = [
    { label: 'JavaScript', value: 25 },
    { label: 'React', value: 18 },
    { label: 'Python', value: 15 },
    { label: 'Node.js', value: 12 },
    { label: 'Database', value: 10 },
    { label: 'DevOps', value: 8 }
  ]

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="relative overflow-hidden rounded-3xl gradient-aurora p-8 text-white">
        <div className="relative z-10">
          <h1 className="text-4xl font-bold mb-2">Welcome back! 👋</h1>
          <p className="text-xl opacity-90 mb-6">
            Here's what's happening with your apprenticeship program today.
          </p>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-green-400 animate-pulse"></div>
              <span className="text-sm">All systems operational</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-blue-400 animate-pulse"></div>
              <span className="text-sm">5 notifications</span>
            </div>
          </div>
        </div>
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm"></div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((card, index) => (
          <Card 
            key={index} 
            variant={card.urgent ? 'default' : card.variant}
            className={`hover-lift group ${card.urgent ? 'border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800' : ''}`}
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-2xl">{card.icon}</span>
                  <CardTitle className={`text-sm font-medium ${card.urgent ? 'text-red-800 dark:text-red-300' : 'text-muted-foreground'}`}>
                    {card.title}
                  </CardTitle>
                </div>
                {card.trend && (
                  <div className="text-xs text-green-600 dark:text-green-400 font-medium">
                    {card.trend}
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className={`text-3xl font-bold mb-2 ${card.urgent ? 'text-red-900 dark:text-red-300' : 'text-foreground'}`}>
                {card.value}
              </div>
              <p className={`text-xs mb-3 ${card.urgent ? 'text-red-600 dark:text-red-400' : 'text-muted-foreground'}`}>
                {card.description}
              </p>
              {card.progress && (
                <Progress 
                  value={card.progress} 
                  variant="gradient" 
                  className="h-2"
                  animated
                />
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Recent Activity */}
        <Card variant="glass" className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <span className="text-2xl">📊</span>
                <span>Recent Activity</span>
              </CardTitle>
              <button className="btn-glass text-xs">View All</button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                {
                  name: 'Emma Wilson',
                  action: 'completed Q3 review',
                  time: '2 hours ago',
                  type: 'success',
                  icon: '✅'
                },
                {
                  name: 'John Smith',
                  action: 'passed JavaScript exam',
                  time: '1 day ago',
                  type: 'info',
                  icon: '📝'
                },
                {
                  name: 'Sophie Taylor',
                  action: 'enrolled in new program',
                  time: '3 days ago',
                  type: 'warning',
                  icon: '🎓'
                },
                {
                  name: 'Michael Chen',
                  action: 'submitted final project',
                  time: '5 days ago',
                  type: 'info',
                  icon: '📋'
                }
              ].map((activity, index) => (
                <div key={index} className="flex items-center space-x-4 p-3 rounded-xl hover:bg-muted/50 transition-colors group">
                  <div className="flex-shrink-0 text-2xl">{activity.icon}</div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-foreground group-hover:text-primary transition-colors">
                      <span className="font-semibold">{activity.name}</span> {activity.action}
                    </p>
                    <p className="text-xs text-muted-foreground">{activity.time}</p>
                  </div>
                  <div className="flex-shrink-0">
                    <div className={`w-2 h-2 rounded-full ${
                      activity.type === 'success' ? 'bg-green-500' :
                      activity.type === 'info' ? 'bg-blue-500' :
                      activity.type === 'warning' ? 'bg-yellow-500' :
                      'bg-gray-500'
                    }`}></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Progress Overview */}
        <Card variant="modern">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span className="text-2xl">🎯</span>
              <span>Progress Overview</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="text-center">
                <CircularProgress 
                  value={stats.averageProgress} 
                  size={120}
                  variant="gradient"
                />
                <p className="text-sm text-muted-foreground mt-2">Overall Progress</p>
              </div>
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Completion Rate</span>
                    <span className="font-medium">{stats.passRate}%</span>
                  </div>
                  <Progress value={stats.passRate} variant="gradient" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Engagement</span>
                    <span className="font-medium">87%</span>
                  </div>
                  <Progress value={87} variant="default" />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Satisfaction</span>
                    <span className="font-medium">92%</span>
                  </div>
                  <Progress value={92} variant="gradient" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Charts */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <PieChart 
          data={departmentData}
          title="Apprentices by Department"
          className="lg:col-span-1"
        />
        
        <LineChart 
          data={progressTrendData}
          title="Progress Trend (6 months)"
          className="lg:col-span-2"
        />
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <BarChart 
          data={skillsData}
          title="Skills Distribution"
        />
        
        <Card>
          <CardHeader>
            <CardTitle>Key Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <StatCard
                title="Completion Rate"
                value={`${stats.passRate}%`}
                change="+12% from last month"
                changeType="positive"
                icon={<svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>}
              />
              <StatCard
                title="Avg Score"
                value="84.5"
                change="+5.2 from last month"
                changeType="positive"
                icon={<svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.196-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" /></svg>}
              />
              <StatCard
                title="Engagement"
                value="92%"
                change="+8% from last month"
                changeType="positive"
                icon={<svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /></svg>}
              />
              <StatCard
                title="Retention"
                value="96%"
                change="+2% from last month"
                changeType="positive"
                icon={<svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Upcoming Deadlines */}
      <Card variant="glass">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span className="text-2xl">⏰</span>
            <span>Upcoming Deadlines</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                title: 'React Development Project',
                student: 'John Smith',
                date: 'Sep 1, 2024',
                status: 'upcoming',
                priority: 'high'
              },
              {
                title: 'Digital Marketing Strategy',
                student: 'Emma Wilson',
                date: 'Aug 20, 2024',
                status: 'upcoming',
                priority: 'medium'
              },
              {
                title: 'Q3 Performance Review',
                student: 'Sophie Taylor',
                date: 'Overdue',
                status: 'overdue',
                priority: 'urgent'
              }
            ].map((deadline, index) => (
              <div key={index} className={`p-4 rounded-xl border-2 transition-all duration-200 hover:shadow-lg ${
                deadline.status === 'overdue' ? 'border-red-200 bg-red-50 dark:bg-red-900/20' :
                deadline.priority === 'high' ? 'border-yellow-200 bg-yellow-50 dark:bg-yellow-900/20' :
                'border-border bg-card'
              }`}>
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-medium text-sm">{deadline.title}</h3>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    deadline.status === 'overdue' ? 'bg-red-100 text-red-800' :
                    deadline.priority === 'high' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {deadline.priority}
                  </span>
                </div>
                <p className="text-xs text-muted-foreground mb-2">{deadline.student}</p>
                <p className={`text-xs font-medium ${
                  deadline.status === 'overdue' ? 'text-red-600' : 'text-muted-foreground'
                }`}>
                  {deadline.date}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}