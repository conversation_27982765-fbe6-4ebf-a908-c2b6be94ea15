import * as React from "react"
import { cn } from "@/lib/utils"

interface HeaderProps {
  title?: string
  subtitle?: string
  actions?: React.ReactNode
  variant?: "default" | "glass" | "gradient"
  showBreadcrumb?: boolean
  breadcrumbItems?: Array<{ label: string; href?: string }>
}

const Header = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & HeaderProps
>(({ 
  className, 
  title = "Dashboard", 
  subtitle,
  actions,
  variant = "default",
  showBreadcrumb = false,
  breadcrumbItems = [],
  ...props 
}, ref) => {
  const variantClasses = {
    default: "bg-background border-b border-border",
    glass: "glass-subtle border-b border-white/10",
    gradient: "gradient-aurora text-white"
  }
  
  return (
    <div
      ref={ref}
      className={cn(
        "sticky top-0 z-40 px-6 py-4 backdrop-blur-sm",
        variantClasses[variant],
        className
      )}
      {...props}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1 min-w-0">
          {showBreadcrumb && breadcrumbItems.length > 0 && (
            <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-1">
              {breadcrumbItems.map((item, index) => (
                <React.Fragment key={index}>
                  {item.href ? (
                    <a
                      href={item.href}
                      className="hover:text-foreground transition-colors"
                    >
                      {item.label}
                    </a>
                  ) : (
                    <span className="text-foreground">{item.label}</span>
                  )}
                  {index < breadcrumbItems.length - 1 && (
                    <span className="text-muted-foreground/50">/</span>
                  )}
                </React.Fragment>
              ))}
            </nav>
          )}
          <div>
            <h1 className="text-2xl font-bold text-foreground text-gradient">
              {title}
            </h1>
            {subtitle && (
              <p className="text-sm text-muted-foreground mt-1">
                {subtitle}
              </p>
            )}
          </div>
        </div>
        
        {actions && (
          <div className="flex items-center space-x-2">
            {actions}
          </div>
        )}
      </div>
    </div>
  )
})
Header.displayName = "Header"

const HeaderAction = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    variant?: "default" | "glass" | "ghost"
    size?: "sm" | "md" | "lg"
  }
>(({ className, variant = "default", size = "md", ...props }, ref) => {
  const variantClasses = {
    default: "bg-primary text-primary-foreground hover:bg-primary/90",
    glass: "btn-glass",
    ghost: "hover:bg-accent hover:text-accent-foreground"
  }
  
  const sizeClasses = {
    sm: "h-8 px-3 text-xs",
    md: "h-10 px-4 text-sm",
    lg: "h-12 px-6 text-base"
  }
  
  return (
    <button
      ref={ref}
      className={cn(
        "inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      {...props}
    />
  )
})
HeaderAction.displayName = "HeaderAction"

const HeaderStats = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    stats: Array<{
      label: string
      value: string | number
      change?: number
      variant?: "default" | "success" | "warning" | "error"
    }>
  }
>(({ className, stats, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6",
        className
      )}
      {...props}
    >
      {stats.map((stat, index) => (
        <div
          key={index}
          className="glass-card p-4 rounded-xl hover:shadow-lg transition-all duration-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                {stat.label}
              </p>
              <p className="text-2xl font-bold text-foreground">
                {stat.value}
              </p>
            </div>
            {stat.change !== undefined && (
              <div className={cn(
                "flex items-center space-x-1 text-sm",
                stat.variant === "success" && "text-green-600",
                stat.variant === "warning" && "text-yellow-600",
                stat.variant === "error" && "text-red-600",
                !stat.variant && "text-muted-foreground"
              )}>
                <span>{stat.change > 0 ? "↗" : stat.change < 0 ? "↘" : "→"}</span>
                <span>{Math.abs(stat.change)}%</span>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  )
})
HeaderStats.displayName = "HeaderStats"

const NotificationBell = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    count?: number
    variant?: "default" | "glass"
  }
>(({ className, count = 0, variant = "default", ...props }, ref) => {
  const variantClasses = {
    default: "hover:bg-accent hover:text-accent-foreground",
    glass: "btn-glass"
  }
  
  return (
    <button
      ref={ref}
      className={cn(
        "relative p-2 rounded-lg transition-all duration-200",
        variantClasses[variant],
        className
      )}
      {...props}
    >
      <svg
        className="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
        />
      </svg>
      {count > 0 && (
        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
          {count > 99 ? "99+" : count}
        </span>
      )}
    </button>
  )
})
NotificationBell.displayName = "NotificationBell"

const ThemeToggle = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    variant?: "default" | "glass"
  }
>(({ className, variant = "default", ...props }, ref) => {
  const [isDark, setIsDark] = React.useState(false)
  
  React.useEffect(() => {
    const isDarkMode = document.documentElement.classList.contains('dark')
    setIsDark(isDarkMode)
  }, [])
  
  const toggleTheme = () => {
    document.documentElement.classList.toggle('dark')
    setIsDark(!isDark)
  }
  
  const variantClasses = {
    default: "hover:bg-accent hover:text-accent-foreground",
    glass: "btn-glass"
  }
  
  return (
    <button
      ref={ref}
      onClick={toggleTheme}
      className={cn(
        "p-2 rounded-lg transition-all duration-200",
        variantClasses[variant],
        className
      )}
      {...props}
    >
      {isDark ? (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
      ) : (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
        </svg>
      )}
    </button>
  )
})
ThemeToggle.displayName = "ThemeToggle"

export { 
  Header, 
  HeaderAction, 
  HeaderStats, 
  NotificationBell, 
  ThemeToggle 
}