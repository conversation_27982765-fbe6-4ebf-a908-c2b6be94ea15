import { Outlet, useRouterState } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import { useState } from 'react'
import { Sidebar } from './ui/sidebar'
import { Header, NotificationBell, ThemeToggle } from './ui/header'
import { ToastProvider } from './ui/toast'
import { ModalProvider } from './ui/modal-provider'

export default function Layout() {
  const routerState = useRouterState()
  const currentPath = routerState.location.pathname
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  const sidebarItems = [
    {
      label: 'Dashboard',
      href: '/',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v4H8V5z" />
        </svg>
      )
    },
    {
      label: 'Apprentices',
      href: '/apprentices',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
        </svg>
      ),
      badge: 24
    },
    {
      label: 'Reviews',
      href: '/reviews',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      badge: 3
    },
    {
      label: 'Exams',
      href: '/exams',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
        </svg>
      ),
      badge: 7
    }
  ]

  const getPageTitle = () => {
    switch (currentPath) {
      case '/':
        return 'Dashboard'
      case '/apprentices':
        return 'Apprentices'
      case '/reviews':
        return 'Reviews'
      case '/exams':
        return 'Exams'
      default:
        return 'Dashboard'
    }
  }

  const getPageSubtitle = () => {
    switch (currentPath) {
      case '/':
        return 'Overview of apprenticeship program metrics and key performance indicators'
      case '/apprentices':
        return 'Manage and track apprentice progress and information'
      case '/reviews':
        return 'Schedule and manage performance reviews'
      case '/exams':
        return 'Track exam schedules and results'
      default:
        return ''
    }
  }

  return (
    <ModalProvider>
      <ToastProvider>
      <div className="min-h-screen bg-background flex">
        {/* Sidebar */}
        <Sidebar
          items={sidebarItems}
          collapsed={sidebarCollapsed}
          onCollapse={setSidebarCollapsed}
          variant="modern"
        />

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <Header
            title={getPageTitle()}
            subtitle={getPageSubtitle()}
            variant="glass"
            actions={
              <div className="flex items-center space-x-2">
                <NotificationBell count={5} variant="glass" />
                <ThemeToggle variant="glass" />
              </div>
            }
          />

          {/* Main Content Area */}
          <main className="flex-1 overflow-auto">
            <div className="container-modern section-padding">
              <div className="animate-fade-in">
                <Outlet />
              </div>
            </div>
          </main>
        </div>

        {/* Development Tools */}
        {import.meta.env.DEV && (
          <TanStackRouterDevtools position="bottom-right" />
        )}
      </div>
    </ToastProvider>
  )
}