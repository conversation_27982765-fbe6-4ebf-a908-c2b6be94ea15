import { configSchema, getConfig } from "./core/config.js";
import { unpluginRouterCodeSplitterFactory } from "./core/router-code-splitter-plugin.js";
import { unpluginRouterGeneratorFactory } from "./core/router-generator-plugin.js";
import { defaultCodeSplitGroupings, splitRouteIdentNodes, tsrSplit } from "./core/constants.js";
export {
  configSchema,
  defaultCodeSplitGroupings,
  getConfig,
  splitRouteIdentNodes,
  tsrSplit,
  unpluginRouterCodeSplitterFactory,
  unpluginRouterGeneratorFactory
};
//# sourceMappingURL=index.js.map
