import * as React from "react"
import { cn } from "@/lib/utils"

interface ParticleBackgroundProps {
  className?: string
  particleCount?: number
  variant?: "default" | "stars" | "dots" | "lines"
  color?: string
  animated?: boolean
}

const ParticleBackground = React.forwardRef<
  HTMLDivElement,
  ParticleBackgroundProps
>(({ 
  className, 
  particleCount = 50, 
  variant = "default", 
  color = "currentColor", 
  animated = true,
  ...props 
}, ref) => {
  const [particles, setParticles] = React.useState<Array<{
    id: number
    x: number
    y: number
    size: number
    opacity: number
    animationDelay: number
  }>>([])

  React.useEffect(() => {
    const newParticles = Array.from({ length: particleCount }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 3 + 1,
      opacity: Math.random() * 0.5 + 0.1,
      animationDelay: Math.random() * 2
    }))
    setParticles(newParticles)
  }, [particleCount])

  const renderParticle = (particle: typeof particles[0]) => {
    const baseClasses = "absolute rounded-full pointer-events-none"
    const animationClasses = animated ? "animate-pulse" : ""
    
    switch (variant) {
      case "stars":
        return (
          <div
            key={particle.id}
            className={cn(baseClasses, animationClasses)}
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              width: `${particle.size}px`,
              height: `${particle.size}px`,
              backgroundColor: color,
              opacity: particle.opacity,
              animationDelay: `${particle.animationDelay}s`,
              clipPath: "polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)"
            }}
          />
        )
      case "dots":
        return (
          <div
            key={particle.id}
            className={cn(baseClasses, animationClasses)}
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              width: `${particle.size}px`,
              height: `${particle.size}px`,
              backgroundColor: color,
              opacity: particle.opacity,
              animationDelay: `${particle.animationDelay}s`
            }}
          />
        )
      case "lines":
        return (
          <div
            key={particle.id}
            className={cn(baseClasses, animationClasses)}
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              width: `${particle.size * 4}px`,
              height: "1px",
              backgroundColor: color,
              opacity: particle.opacity,
              animationDelay: `${particle.animationDelay}s`,
              transform: `rotate(${particle.x}deg)`
            }}
          />
        )
      default:
        return (
          <div
            key={particle.id}
            className={cn(baseClasses, animationClasses)}
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              width: `${particle.size}px`,
              height: `${particle.size}px`,
              backgroundColor: color,
              opacity: particle.opacity,
              animationDelay: `${particle.animationDelay}s`
            }}
          />
        )
    }
  }

  return (
    <div
      ref={ref}
      className={cn(
        "absolute inset-0 overflow-hidden pointer-events-none",
        className
      )}
      {...props}
    >
      {particles.map(renderParticle)}
    </div>
  )
})
ParticleBackground.displayName = "ParticleBackground"

const AnimatedBackground = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    variant?: "gradient" | "mesh" | "waves" | "geometric"
  }
>(({ className, variant = "gradient", ...props }, ref) => {
  const variantClasses = {
    gradient: "bg-gradient-to-br from-purple-400 via-pink-500 to-red-500 animate-pulse",
    mesh: "bg-gradient-to-r from-cyan-400 to-blue-500 opacity-20",
    waves: "bg-gradient-to-r from-blue-400 to-purple-600 opacity-30",
    geometric: "bg-gradient-to-tr from-yellow-400 to-orange-500 opacity-25"
  }

  return (
    <div
      ref={ref}
      className={cn(
        "absolute inset-0 -z-10 overflow-hidden pointer-events-none",
        variantClasses[variant],
        className
      )}
      {...props}
    >
      <div className="absolute inset-0 bg-gradient-to-t from-background/50 to-transparent" />
    </div>
  )
})
AnimatedBackground.displayName = "AnimatedBackground"

const GlowingOrb = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    size?: number
    color?: string
    intensity?: "low" | "medium" | "high"
    animated?: boolean
  }
>(({ 
  className, 
  size = 100, 
  color = "#8B5CF6", 
  intensity = "medium", 
  animated = true,
  ...props 
}, ref) => {
  const intensityClasses = {
    low: "blur-sm opacity-30",
    medium: "blur-md opacity-40",
    high: "blur-lg opacity-50"
  }

  return (
    <div
      ref={ref}
      className={cn(
        "absolute rounded-full pointer-events-none",
        intensityClasses[intensity],
        animated && "animate-pulse",
        className
      )}
      style={{
        width: `${size}px`,
        height: `${size}px`,
        backgroundColor: color,
        boxShadow: `0 0 ${size}px ${color}`
      }}
      {...props}
    />
  )
})
GlowingOrb.displayName = "GlowingOrb"

const FloatingElements = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    elements?: Array<{
      id: number
      icon: React.ReactNode
      x: number
      y: number
      size: number
      animationDelay: number
    }>
  }
>(({ className, elements = [], ...props }, ref) => {
  const [defaultElements, setDefaultElements] = React.useState<typeof elements>([])

  React.useEffect(() => {
    if (elements.length === 0) {
      const icons = ['🎓', '📚', '💼', '⭐', '🎯', '🚀', '💡', '🏆']
      const newElements = Array.from({ length: 8 }, (_, i) => ({
        id: i,
        icon: icons[i % icons.length],
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: Math.random() * 20 + 20,
        animationDelay: Math.random() * 3
      }))
      setDefaultElements(newElements)
    }
  }, [elements])

  const elementsToRender = elements.length > 0 ? elements : defaultElements

  return (
    <div
      ref={ref}
      className={cn(
        "absolute inset-0 overflow-hidden pointer-events-none",
        className
      )}
      {...props}
    >
      {elementsToRender.map((element) => (
        <div
          key={element.id}
          className="absolute animate-bounce"
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
            fontSize: `${element.size}px`,
            animationDelay: `${element.animationDelay}s`,
            animationDuration: '3s'
          }}
        >
          {element.icon}
        </div>
      ))}
    </div>
  )
})
FloatingElements.displayName = "FloatingElements"

export { 
  ParticleBackground, 
  AnimatedBackground, 
  GlowingOrb, 
  FloatingElements 
}