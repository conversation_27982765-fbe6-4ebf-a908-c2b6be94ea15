import React, { useState } from 'react'
import { Exam, ExamResult } from '../../../types'
import { Button } from '../../../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { useModal } from '../../../components/ui/modal-provider'
import { ExamForm } from './ExamForm'
import { 
  Calendar, 
  Clock, 
  BookOpen, 
  Award,
  Target,
  FileText,
  Users,
  CheckCircle,
  XCircle,
  AlertCircle,
  Edit3,
  Trash2
} from 'lucide-react'

interface ExamDetailsModalProps {
  exam: Exam
  onClose: () => void
}

// Mock exam results for demonstration
const mockExamResults: ExamResult[] = [
  {
    id: '1',
    examId: '1',
    apprenticeId: '1',
    score: 85,
    completedAt: '2024-01-15T10:30:00Z',
    timeSpent: 55,
    feedback: 'Excellent understanding of core concepts. Minor improvements needed in advanced topics.',
    passed: true,
    retakeAllowed: false,
    retakeCount: 0
  },
  {
    id: '2',
    examId: '1',
    apprenticeId: '2',
    score: 55,
    completedAt: '2024-01-15T11:15:00Z',
    timeSpent: 75,
    feedback: 'Good effort but needs more practice with practical implementation.',
    passed: false,
    retakeAllowed: true,
    retakeCount: 0
  },
  {
    id: '3',
    examId: '1',
    apprenticeId: '3',
    score: 92,
    completedAt: '2024-01-15T14:20:00Z',
    timeSpent: 48,
    feedback: 'Outstanding performance with innovative solutions.',
    passed: true,
    retakeAllowed: false,
    retakeCount: 0
  }
]

export const ExamDetailsModal: React.FC<ExamDetailsModalProps> = ({
  exam,
  onClose
}) => {
  const [isEditing, setIsEditing] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const { confirmModal } = useModal()

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleUpdate = async (data: Partial<Exam>) => {
    try {
      // In a real app, this would call an API to update the exam
      console.log('Updating exam:', data)
      setIsEditing(false)
      onClose()
    } catch (error) {
      console.error('Failed to update exam:', error)
    }
  }

  const handleDelete = () => {
    confirmModal({
      title: 'Delete Exam',
      message: `Are you sure you want to delete "${exam.title}"? This action cannot be undone.`,
      onConfirm: async () => {
        setIsDeleting(true)
        try {
          // In a real app, this would call an API to delete the exam
          console.log('Deleting exam:', exam.id)
          onClose()
        } catch (error) {
          console.error('Failed to delete exam:', error)
        } finally {
          setIsDeleting(false)
        }
      }
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'active':
        return 'bg-blue-100 text-blue-800'
      case 'upcoming':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'written':
        return <FileText className="w-5 h-5" />
      case 'practical':
        return <Target className="w-5 h-5" />
      case 'oral':
        return <Users className="w-5 h-5" />
      case 'project':
        return <BookOpen className="w-5 h-5" />
      default:
        return <FileText className="w-5 h-5" />
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  const calculateStatistics = () => {
    const results = mockExamResults.filter(r => r.examId === exam.id)
    const totalAttempts = results.length
    const passed = results.filter(r => r.passed).length
    const failed = totalAttempts - passed
    const averageScore = totalAttempts > 0 ? Math.round(results.reduce((sum, r) => sum + r.score, 0) / totalAttempts) : 0
    const averageTime = totalAttempts > 0 ? Math.round(results.reduce((sum, r) => sum + r.timeSpent, 0) / totalAttempts) : 0
    const passRate = totalAttempts > 0 ? Math.round((passed / totalAttempts) * 100) : 0

    return {
      totalAttempts,
      passed,
      failed,
      averageScore,
      averageTime,
      passRate
    }
  }

  const stats = calculateStatistics()

  if (isEditing) {
    return (
      <div className="max-w-4xl">
        <ExamForm
          exam={exam}
          onSubmit={handleUpdate}
          onCancel={() => setIsEditing(false)}
        />
      </div>
    )
  }

  return (
    <div className="max-w-4xl">
      {/* Header */}
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              {getTypeIcon(exam.type)}
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{exam.title}</h2>
              <p className="text-gray-500">{exam.subject}</p>
              <span className={`inline-flex rounded-full px-3 py-1 text-sm font-semibold mt-2 ${getStatusColor(exam.status)}`}>
                {exam.status}
              </span>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button onClick={handleEdit} variant="outline" size="sm">
              <Edit3 className="w-4 h-4 mr-2" />
              Edit
            </Button>
            <Button 
              onClick={handleDelete} 
              variant="destructive" 
              size="sm"
              disabled={isDeleting}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              {isDeleting ? 'Deleting...' : 'Delete'}
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6 space-y-6">
        {/* Exam Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="w-5 h-5 mr-2" />
              Exam Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500">Type</p>
                <p className="font-medium capitalize">{exam.type}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Subject</p>
                <p className="font-medium">{exam.subject}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Duration</p>
                <p className="font-medium">{formatDuration(exam.duration)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Scheduled Date</p>
                <p className="font-medium">{formatDate(exam.scheduledDate)}</p>
              </div>
            </div>
            <div className="mt-4">
              <p className="text-sm text-gray-500">Description</p>
              <p className="mt-1 text-gray-700">{exam.description}</p>
            </div>
          </CardContent>
        </Card>

        {/* Scoring Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="w-5 h-5 mr-2" />
              Scoring Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <p className="text-sm text-gray-500">Maximum Score</p>
                <p className="text-2xl font-bold text-blue-600">{exam.maxScore}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500">Passing Score</p>
                <p className="text-2xl font-bold text-green-600">{exam.passingScore}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500">Pass Percentage</p>
                <p className="text-2xl font-bold text-purple-600">
                  {Math.round((exam.passingScore / exam.maxScore) * 100)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="w-5 h-5 mr-2" />
              Performance Statistics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              <div className="text-center">
                <p className="text-sm text-gray-500">Total Attempts</p>
                <p className="text-xl font-bold">{stats.totalAttempts}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500">Passed</p>
                <p className="text-xl font-bold text-green-600">{stats.passed}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500">Failed</p>
                <p className="text-xl font-bold text-red-600">{stats.failed}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500">Pass Rate</p>
                <p className="text-xl font-bold text-blue-600">{stats.passRate}%</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500">Avg Score</p>
                <p className="text-xl font-bold">{stats.averageScore}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500">Avg Time</p>
                <p className="text-xl font-bold">{formatDuration(stats.averageTime)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        {exam.instructions && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertCircle className="w-5 h-5 mr-2" />
                Instructions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-blue-50 rounded-lg p-4">
                <p className="text-gray-700 whitespace-pre-wrap">{exam.instructions}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Recent Results */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="w-5 h-5 mr-2" />
              Recent Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            {mockExamResults.filter(r => r.examId === exam.id).length > 0 ? (
              <div className="space-y-4">
                {mockExamResults
                  .filter(r => r.examId === exam.id)
                  .slice(0, 5)
                  .map((result) => (
                    <div key={result.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          result.passed ? 'bg-green-100' : 'bg-red-100'
                        }`}>
                          {result.passed ? (
                            <CheckCircle className="w-4 h-4 text-green-600" />
                          ) : (
                            <XCircle className="w-4 h-4 text-red-600" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">Apprentice {result.apprenticeId}</p>
                          <p className="text-sm text-gray-500">
                            {formatDate(result.completedAt)}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-bold ${
                          result.passed ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {result.score}/{exam.maxScore}
                        </p>
                        <p className="text-sm text-gray-500">
                          {formatDuration(result.timeSpent)}
                        </p>
                      </div>
                    </div>
                  ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No results yet</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Exam Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Exam Timeline
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium">Exam Created</p>
                  <p className="text-xs text-gray-500">
                    {exam.status === 'upcoming' ? 'Ready for scheduling' : 'Scheduled'}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className={`w-2 h-2 rounded-full ${
                  exam.status === 'active' || exam.status === 'completed' ? 'bg-green-500' : 'bg-gray-300'
                }`}></div>
                <div>
                  <p className="text-sm font-medium">Exam Scheduled</p>
                  <p className="text-xs text-gray-500">
                    {formatDate(exam.scheduledDate)}
                  </p>
                </div>
              </div>
              
              {exam.status === 'completed' && (
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium">Exam Completed</p>
                    <p className="text-xs text-gray-500">
                      {stats.totalAttempts} apprentices completed
                    </p>
                  </div>
                </div>
              )}
              
              {exam.status === 'cancelled' && (
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <div>
                    <p className="text-sm font-medium">Exam Cancelled</p>
                    <p className="text-xs text-gray-500">
                      Contact administrator for more information
                    </p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}