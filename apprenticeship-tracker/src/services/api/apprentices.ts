import { apiClient } from './client'

export interface Apprentice {
  id: string
  name: string
  email: string
  department: string
  startDate: string
  endDate: string
  status: 'active' | 'completed' | 'inactive'
}

export const apprenticesApi = {
  getAll: () => apiClient.get<Apprentice[]>('/apprentices'),
  getById: (id: string) => apiClient.get<Apprentice>(`/apprentices/${id}`),
  create: (data: Omit<Apprentice, 'id'>) => apiClient.post<Apprentice>('/apprentices', data),
  update: (id: string, data: Partial<Apprentice>) => apiClient.put<Apprentice>(`/apprentices/${id}`, data),
  delete: (id: string) => apiClient.delete<void>(`/apprentices/${id}`),
}