{"name": "apprenticeship-tracker", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-router": "^1.127.3", "@tanstack/react-router-devtools": "^1.127.3", "@tanstack/react-table": "^8.21.3", "@tanstack/router-generator": "^1.127.5", "@tanstack/start": "^1.120.20", "@types/node": "^24.0.13", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "msw": "^2.10.4", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "vite": "^7.0.4", "zustand": "^5.0.6"}, "msw": {"workerDirectory": ["public"]}, "devDependencies": {"@tanstack/router-plugin": "^1.127.5"}}