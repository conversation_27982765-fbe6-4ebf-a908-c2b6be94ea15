{"version": 3, "file": "webpack.cjs", "sources": ["../../src/webpack.ts"], "sourcesContent": ["import { createWebpackPlugin } from 'unplugin'\n\nimport { configSchema } from './core/config'\nimport { unpluginRouterCodeSplitterFactory } from './core/router-code-splitter-plugin'\nimport { unpluginRouterGeneratorFactory } from './core/router-generator-plugin'\nimport { unpluginRouterComposedFactory } from './core/router-composed-plugin'\nimport type { Config } from './core/config'\n\n/**\n * @example\n * ```ts\n * export default {\n *   // ...\n *   plugins: [TanStackRouterGeneratorWebpack()],\n * }\n * ```\n */\nconst TanStackRouterGeneratorWebpack = /* #__PURE__ */ createWebpackPlugin(\n  unpluginRouterGeneratorFactory,\n)\n\n/**\n * @example\n * ```ts\n * export default {\n *   // ...\n *   plugins: [TanStackRouterCodeSplitterWebpack()],\n * }\n * ```\n */\nconst TanStackRouterCodeSplitterWebpack = /* #__PURE__ */ createWebpackPlugin(\n  unpluginRouterCodeSplitterFactory,\n)\n\n/**\n * @example\n * ```ts\n * export default {\n *   // ...\n *   plugins: [tanstackRouter()],\n * }\n * ```\n */\nconst TanStackRouterWebpack = /* #__PURE__ */ createWebpackPlugin(\n  unpluginRouterComposedFactory,\n)\n\nconst tanstackRouter = TanStackRouterWebpack\nexport default TanStackRouterWebpack\nexport {\n  configSchema,\n  TanStackRouterWebpack,\n  TanStackRouterGeneratorWebpack,\n  TanStackRouterCodeSplitterWebpack,\n  tanstackRouter,\n}\nexport type { Config }\n"], "names": ["createWebpackPlugin", "unpluginRouterGeneratorFactory", "unpluginRouterCodeSplitterFactory", "unpluginRouterComposedFactory"], "mappings": ";;;;;;;AAiBA,MAAM,iCAAiDA,yBAAA;AAAA,EACrDC,sBAAAA;AACF;AAWA,MAAM,oCAAoDD,yBAAA;AAAA,EACxDE,yBAAAA;AACF;AAWA,MAAM,wBAAwCF,yBAAA;AAAA,EAC5CG,qBAAAA;AACF;AAEA,MAAM,iBAAiB;;;;;;;"}